'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { apiClient, handleApiResponse } from '@/lib/api-client';
import { formatDateTime } from '@/lib/date-utils';
import '../track.css';
import '../enhanced-styles.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Barcode,
  Package,
  Wrench,
  ShoppingCart,
  Undo2,
  ClipboardCheck,
  Shuffle,
  User,
  <PERSON>age<PERSON>heck,
  FileText,
  Printer,
  FileDown,
  AlertCircle,
  CheckCircle,
  Filter,
  ArrowDown,
  ArrowUp,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DeviceEvent {
  id: string;
  type: string;
  typeName: string;
  title: string;
  description: string;
  date: Date;
  timestamp: number;
  user: string;
  orderNumber: string;
  status: string;
  details?: any;
}

export default function DeviceEventLogPage() {
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();

  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [devices, setDevices] = useState<any[]>([]);
  const [sales, setSales] = useState<any[]>([]);
  const [returns, setReturns] = useState<any[]>([]);
  const [supplyOrders, setSupplyOrders] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [evaluationOrders, setEvaluationOrders] = useState<any[]>([]);
  const [maintenanceHistory, setMaintenanceHistory] = useState<any[]>([]);
  const [maintenanceOrders, setMaintenanceOrders] = useState<any[]>([]);
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<any[]>([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState<any[]>([]);
  const [maintenanceLogs, setMaintenanceLogs] = useState<any[]>([]);
  const [maintenanceReceipts, setMaintenanceReceipts] = useState<any[]>([]);
  const [deliveryOrders, setDeliveryOrders] = useState<any[]>([]);

  const [imei, setImei] = useState('');
  const [searchedImei, setSearchedImei] = useState('');
  const [deviceEvents, setDeviceEvents] = useState<DeviceEvent[]>([]);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterType, setFilterType] = useState('all');
  const [filterUser, setFilterUser] = useState('all');
  const [uniqueUsers, setUniqueUsers] = useState<string[]>([]);

  // API functions
  const fetchAllData = async () => {
    setIsLoading(true);
    try {
      const [
        devicesRes,
        salesRes,
        returnsRes,
        supplyRes,
        suppliersRes,
        evaluationsRes,
        maintenanceRes,
        maintenanceOrdersRes,
        maintenanceReceiptsRes,
        warehouseTransfersRes,
        deliveryOrdersRes
      ] = await Promise.all([
        apiClient.get('/api/devices?view=simple'),
        apiClient.get('/api/sales?view=simple'),
        apiClient.get('/api/returns?view=simple'),
        apiClient.get('/api/supply?view=simple'),
        apiClient.get('/api/suppliers?view=simple'),
        apiClient.get('/api/evaluations?view=simple'),
        apiClient.get('/api/maintenance-logs?view=simple'),
        apiClient.get('/api/maintenance-orders?view=simple'),
        apiClient.get('/api/maintenance-receipts?view=simple'),
        apiClient.get('/api/warehouse-transfers'),
        apiClient.get('/api/delivery-orders?view=simple')
      ]);

      if (devicesRes.ok) {
        const devicesData = await handleApiResponse(devicesRes);
        console.log('✅ Loaded devices:', devicesData?.length || 0);
        setDevices(devicesData);
      }
      if (salesRes.ok) {
        const salesData = await handleApiResponse(salesRes);
        console.log('✅ Loaded sales:', salesData?.length || 0);
        setSales(salesData);
      }
      if (returnsRes.ok) {
        const returnsData = await handleApiResponse(returnsRes);
        console.log('✅ Loaded returns:', returnsData?.length || 0);
        setReturns(returnsData);
      }
      if (supplyRes.ok) {
        const supplyData = await handleApiResponse(supplyRes);
        console.log('✅ Loaded supply orders:', supplyData?.length || 0);
        setSupplyOrders(supplyData);
      }
      if (suppliersRes.ok) {
        const suppliersData = await handleApiResponse(suppliersRes);
        console.log('✅ Loaded suppliers:', suppliersData?.length || 0);
        setSuppliers(suppliersData);
      }
      if (evaluationsRes.ok) {
        const evaluationsData = await handleApiResponse(evaluationsRes);
        console.log('✅ Loaded evaluations:', evaluationsData?.length || 0);
        setEvaluationOrders(evaluationsData);
      }
      if (maintenanceRes.ok) {
        const maintenanceData = await handleApiResponse(maintenanceRes);
        console.log('✅ Loaded maintenance logs:', maintenanceData?.length || 0);
        setMaintenanceLogs(maintenanceData);
      }
      if (maintenanceOrdersRes.ok) {
        const maintenanceOrdersData = await handleApiResponse(maintenanceOrdersRes);
        console.log('✅ Loaded maintenance orders:', maintenanceOrdersData?.length || 0);
        setMaintenanceOrders(maintenanceOrdersData);
      }
      if (maintenanceReceiptsRes.ok) {
        const maintenanceReceiptsData = await handleApiResponse(maintenanceReceiptsRes);
        console.log('✅ Loaded maintenance receipts:', maintenanceReceiptsData?.length || 0);
        setMaintenanceReceipts(maintenanceReceiptsData);
      }
      if (warehouseTransfersRes.ok) {
        const warehouseTransfersData = await handleApiResponse(warehouseTransfersRes);
        console.log('✅ Loaded warehouse transfers:', warehouseTransfersData?.length || 0);
        setWarehouseTransfers(warehouseTransfersData);
      }
      if (deliveryOrdersRes.ok) {
        const deliveryOrdersData = await handleApiResponse(deliveryOrdersRes) as any[];
        console.log('✅ Loaded delivery orders:', deliveryOrdersData?.length || 0);
        setDeliveryOrders(deliveryOrdersData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'حدث خطأ أثناء تحميل بيانات النظام',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  // Handle URL params
  useEffect(() => {
    const idFromUrl = searchParams.get('id');
    if (idFromUrl) {
      setImei(idFromUrl);
      setSearchedImei(idFromUrl);
    }
  }, [searchParams]);

  // Handle search
  const handleSearch = (imei: string) => {
    setSearchedImei(imei);
    const params = new URLSearchParams(searchParams);
    params.set('id', imei);
    router.replace(`/track/event-log?${params.toString()}`);
  };

  // Get type badge color
  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'supply': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'evaluation': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'maintenanceReceive': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'transfer': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'sale': return 'bg-green-100 text-green-800 border-green-200';
      case 'return': return 'bg-red-100 text-red-800 border-red-200';
      case 'warehouseReceive': return 'bg-teal-100 text-teal-800 border-teal-200';
      case 'warehouseDelivery': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get type icon
  const getTypeIcon = (type: string): JSX.Element => {
    switch (type) {
      case 'supply': return <Package className="h-4 w-4" />;
      case 'evaluation': return <ClipboardCheck className="h-4 w-4" />;
      case 'maintenance': return <Wrench className="h-4 w-4" />;
      case 'maintenanceReceive': return <PackageCheck className="h-4 w-4" />;
      case 'transfer': return <Shuffle className="h-4 w-4" />;
      case 'sale': return <ShoppingCart className="h-4 w-4" />;
      case 'return': return <Undo2 className="h-4 w-4" />;
      case 'warehouseReceive': return <PackageCheck className="h-4 w-4" />;
      case 'warehouseDelivery': return <Package className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  // Process device events from all sources
  useEffect(() => {
    if (!searchedImei) return;

    console.log('Processing data for device:', searchedImei);
    const events: DeviceEvent[] = [];
    const device = devices.find(d => d.id === searchedImei);
    
    // Process supply orders
    supplyOrders.forEach(order => {
      if (!order.items) return;
      
      let items = [];
      try {
        items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
      } catch {
        return;
      }
      
      const deviceItem = items.find(item => 
        item.deviceId === searchedImei || item.id === searchedImei || item.imei === searchedImei
      );
      
      if (deviceItem) {
        const supplier = suppliers.find(s => s.id === order.supplierId);
        events.push({
          id: `supply-${order.orderNumber || order.id}`,
          type: 'supply',
          typeName: 'توريد',
          title: 'توريد الجهاز',
          description: `تم توريد الجهاز من المورد "${supplier?.name || 'غير معروف'}"`,
          date: order.date || order.createdAt,
          timestamp: new Date(order.date || order.createdAt).getTime(),
          user: order.employeeName || 'غير معروف',
          orderNumber: order.orderNumber || '-',
          status: order.status || 'مكتمل',
          details: {
            orderNumber: order.orderNumber,
            supplierName: supplier?.name,
            cost: deviceItem.cost,
            condition: deviceItem.condition,
            notes: order.notes,
            warehouseName: order.warehouseName
          }
        });
      }
    });
    
    // Process evaluations
    evaluationOrders.forEach(order => {
      if (!order.items) return;
      
      let items = [];
      try {
        items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
      } catch {
        return;
      }
      
      const deviceItem = items.find(item => item.deviceId === searchedImei);
      
      if (deviceItem) {
        const evaluationResult = deviceItem.result === 'working' ? 'يعمل بشكل طبيعي' :
                               deviceItem.result === 'defective' ? 'يحتاج صيانة' :
                               deviceItem.result === 'damaged' ? 'تالف' :
                               deviceItem.result || 'غير محدد';
                               
        events.push({
          id: `evaluation-${order.orderNumber || order.id}`,
          type: 'evaluation',
          typeName: 'فحص وتقييم',
          title: 'فحص وتقييم الجهاز',
          description: `تم فحص وتقييم الجهاز. النتيجة: ${evaluationResult}`,
          date: order.date || order.createdAt,
          timestamp: new Date(order.date || order.createdAt).getTime(),
          user: order.employeeName || 'غير معروف',
          orderNumber: order.orderNumber || '-',
          status: evaluationResult,
          details: {
            orderNumber: order.orderNumber,
            result: deviceItem.result,
            evaluationResult: evaluationResult,
            condition: deviceItem.condition,
            notes: deviceItem.notes,
            estimatedValue: deviceItem.estimatedValue,
            recommendedAction: deviceItem.recommendedAction
          }
        });
      }
    });
    
    // Process maintenance orders
    maintenanceOrders.forEach(order => {
      if (!order.items) return;
      
      let items = [];
      try {
        items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
      } catch {
        return;
      }
      
      const deviceItem = items.find(item => item.deviceId === searchedImei);
      
      if (deviceItem) {
        const maintenanceReason = deviceItem.fault || deviceItem.damageType || deviceItem.issueDescription || 'صيانة عامة';
        
        events.push({
          id: `maintenance-${order.orderNumber || order.id}`,
          type: 'maintenance',
          typeName: 'إرسال للصيانة',
          title: 'إرسال للصيانة',
          description: `تم إرسال الجهاز للصيانة. السبب: ${maintenanceReason}`,
          date: order.date || order.createdAt,
          timestamp: new Date(order.date || order.createdAt).getTime(),
          user: order.employeeName || 'غير معروف',
          orderNumber: order.orderNumber || '-',
          status: order.status || 'مرسل',
          details: {
            orderNumber: order.orderNumber,
            fault: deviceItem.fault,
            damageType: deviceItem.damageType,
            issueDescription: deviceItem.issueDescription,
            expectedCost: deviceItem.expectedCost,
            maintenanceEmployee: order.maintenanceEmployeeName,
            notes: deviceItem.notes || order.notes
          }
        });
      }
    });
    
    // Process maintenance receipts
    const allMaintenanceReceipts = [...(maintenanceReceiptOrders || []), ...(maintenanceReceipts || [])];
    allMaintenanceReceipts.forEach(order => {
      if (!order.items) return;
      
      let items = [];
      try {
        items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
      } catch {
        return;
      }
      
      const deviceItem = items.find(item => item.deviceId === searchedImei);
      
      if (deviceItem) {
        const resultText = deviceItem.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          deviceItem.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل فنياً' :
                          deviceItem.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف فيزيائياً' :
                          deviceItem.result === 'PartiallyRepaired' ? 'تم الإصلاح جزئياً' :
                          deviceItem.result || 'نتيجة غير محددة';
        
        events.push({
          id: `maintenanceReceive-${order.receiptNumber || order.id}`,
          type: 'maintenanceReceive',
          typeName: 'استلام من الصيانة',
          title: 'استلام من الصيانة',
          description: `تم استلام الجهاز من قسم الصيانة. النتيجة: ${resultText}`,
          date: order.date || order.createdAt,
          timestamp: new Date(order.date || order.createdAt).getTime(),
          user: order.employeeName || 'غير معروف',
          orderNumber: order.receiptNumber || '-',
          status: resultText,
          details: {
            receiptNumber: order.receiptNumber,
            result: deviceItem.result,
            resultText: resultText,
            repairCost: deviceItem.repairCost,
            repairDescription: deviceItem.repairDescription,
            maintenanceEmployee: order.maintenanceEmployeeName
          }
        });
      }
    });
    
    // Process warehouse transfers
    warehouseTransfers.forEach(transfer => {
      if (!Array.isArray(transfer.items)) return;
      
      const deviceItem = transfer.items.find(item => item.deviceId === searchedImei);
      
      if (deviceItem) {
        events.push({
          id: `transfer-${transfer.transferNumber || transfer.id}`,
          type: 'transfer',
          typeName: 'تحويل مخزني',
          title: 'تحويل مخزني',
          description: `تم تحويل الجهاز من "${transfer.fromWarehouseName}" إلى "${transfer.toWarehouseName}"`,
          date: transfer.date || transfer.createdAt,
          timestamp: new Date(transfer.date || transfer.createdAt).getTime(),
          user: transfer.employeeName || 'غير معروف',
          orderNumber: transfer.transferNumber || '-',
          status: transfer.status || 'مكتمل',
          details: {
            transferNumber: transfer.transferNumber,
            fromWarehouse: transfer.fromWarehouseName,
            toWarehouse: transfer.toWarehouseName,
            status: transfer.status
          }
        });
      }
    });
    
    // Process sales
    sales.forEach(sale => {
      if (!Array.isArray(sale.items)) return;
      
      const deviceItem = sale.items.find(item => item.deviceId === searchedImei);
      
      if (deviceItem) {
        const warrantyText = sale.warrantyPeriod === '3d' ? '3 أيام' :
                            sale.warrantyPeriod === '1w' ? 'أسبوع' :
                            sale.warrantyPeriod === '1m' ? 'شهر' :
                            sale.warrantyPeriod === '3m' ? '3 أشهر' :
                            sale.warrantyPeriod === '6m' ? '6 أشهر' :
                            sale.warrantyPeriod === '1y' ? 'سنة' : 'بدون ضمان';
        
        events.push({
          id: `sale-${sale.soNumber || sale.id}`,
          type: 'sale',
          typeName: 'بيع',
          title: 'بيع الجهاز',
          description: `تم بيع الجهاز للعميل "${sale.clientName}"`,
          date: sale.date || sale.createdAt,
          timestamp: new Date(sale.date || sale.createdAt).getTime(),
          user: sale.employeeName || 'غير معروف',
          orderNumber: sale.soNumber || '-',
          status: 'مباع',
          details: {
            soNumber: sale.soNumber,
            opNumber: sale.opNumber,
            clientName: sale.clientName,
            salePrice: deviceItem.salePrice,
            warrantyPeriod: sale.warrantyPeriod,
            warrantyText: warrantyText
          }
        });
      }
    });
    
    // Process returns
    returns.forEach(returnOrder => {
      if (!Array.isArray(returnOrder.items)) return;
      
      // Check if this device was returned
      const returnedItem = returnOrder.items.find(item => item.deviceId === searchedImei);
      
      if (returnedItem) {
        events.push({
          id: `return-${returnOrder.roNumber || returnOrder.id}`,
          type: 'return',
          typeName: 'إرجاع',
          title: 'إرجاع الجهاز',
          description: `تم إرجاع الجهاز من العميل "${returnOrder.clientName}"`,
          date: returnOrder.date || returnOrder.createdAt,
          timestamp: new Date(returnOrder.date || returnOrder.createdAt).getTime(),
          user: returnOrder.employeeName || 'غير معروف',
          orderNumber: returnOrder.roNumber || '-',
          status: 'مرتجع',
          details: {
            returnOrderNumber: returnOrder.roNumber,
            clientName: returnOrder.clientName,
            returnReason: returnedItem.returnReason,
            returnType: returnedItem.returnType
          }
        });
      }
      
      // Check if this device was given as a replacement
      const replacementItem = returnOrder.items.find(item => item.replacementDeviceId === searchedImei);
      
      if (replacementItem) {
        events.push({
          id: `replacement-${returnOrder.roNumber || returnOrder.id}`,
          type: 'sale',
          typeName: 'استبدال',
          title: 'تسليم كبديل',
          description: `تم تسليم الجهاز كبديل للعميل "${returnOrder.clientName}"`,
          date: returnOrder.date || returnOrder.createdAt,
          timestamp: new Date(returnOrder.date || returnOrder.createdAt).getTime(),
          user: returnOrder.employeeName || 'غير معروف',
          orderNumber: returnOrder.roNumber || '-',
          status: 'تم التسليم',
          details: {
            returnOrderNumber: returnOrder.roNumber,
            clientName: returnOrder.clientName,
            isReplacementDevice: true,
            originalDeviceId: replacementItem.deviceId
          }
        });
      }
    });

    // Process warehouse receives and deliveries
    if (deliveryOrders.length > 0) {
      deliveryOrders.forEach(order => {
        if (!Array.isArray(order.items)) return;
        
        const deviceItem = order.items.find(item => item.deviceId === searchedImei);
        
        if (deviceItem) {
          if (order.type === 'receive') {
            events.push({
              id: `warehouseReceive-${order.orderNumber || order.id}`,
              type: 'warehouseReceive',
              typeName: 'استلام للمخزن',
              title: 'استلام للمخزن',
              description: `تم استلام الجهاز في مخزن "${order.warehouseName || 'غير معروف'}"`,
              date: order.date || order.createdAt,
              timestamp: new Date(order.date || order.createdAt).getTime(),
              user: order.employeeName || 'غير معروف',
              orderNumber: order.orderNumber || '-',
              status: 'تم الاستلام',
              details: {
                orderNumber: order.orderNumber,
                warehouseName: order.warehouseName,
                notes: order.notes
              }
            });
          } else if (order.type === 'delivery') {
            events.push({
              id: `warehouseDelivery-${order.orderNumber || order.id}`,
              type: 'warehouseDelivery',
              typeName: 'تسليم من المخزن',
              title: 'تسليم من المخزن',
              description: `تم تسليم الجهاز من مخزن "${order.warehouseName || 'غير معروف'}"`,
              date: order.date || order.createdAt,
              timestamp: new Date(order.date || order.createdAt).getTime(),
              user: order.employeeName || 'غير معروف',
              orderNumber: order.orderNumber || '-',
              status: 'تم التسليم',
              details: {
                orderNumber: order.orderNumber,
                warehouseName: order.warehouseName,
                destination: order.destination || 'غير محدد',
                notes: order.notes
              }
            });
          }
        }
      });
    }

    console.log(`Found ${events.length} events for device: ${searchedImei}`);
    
    // Set all events
    setDeviceEvents(events);
    
    // Extract unique users
    const users = Array.from(new Set(events.map(event => event.user))).filter(Boolean);
    setUniqueUsers(users);
    
  }, [
    searchedImei,
    devices,
    sales,
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceHistory,
    maintenanceOrders,
    maintenanceReceiptOrders,
    maintenanceLogs,
    warehouseTransfers,
    deliveryOrders
  ]);

  // Get device details
  const device = useMemo(() => {
    return devices.find(d => d.id === searchedImei);
  }, [devices, searchedImei]);

  // Filter and sort events
  const filteredEvents = useMemo(() => {
    if (!deviceEvents.length) return [];
    
    return deviceEvents
      .filter(event => {
        if (filterType === 'all') return true;
        return event.type === filterType;
      })
      .filter(event => {
        if (filterUser === 'all') return true;
        return event.user === filterUser;
      })
      .sort((a, b) => {
        return sortOrder === 'desc' 
          ? b.timestamp - a.timestamp 
          : a.timestamp - b.timestamp;
      });
  }, [deviceEvents, sortOrder, filterType, filterUser]);

  // Event type statistics
  const eventStats = useMemo(() => {
    if (!deviceEvents.length) return {};
    
    const stats: Record<string, number> = {};
    deviceEvents.forEach(event => {
      stats[event.type] = (stats[event.type] || 0) + 1;
    });
    
    return stats;
  }, [deviceEvents]);

  return (
    <div className="space-y-6 p-6">
      {/* شريط البحث */}
      <Card>
        <CardHeader>
          <CardTitle className="text-right">سجل أحداث الأجهزة</CardTitle>
          <CardDescription className="text-right">
            عرض جميع العمليات والأحداث المرتبطة بجهاز محدد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="أدخل الرقم التسلسلي (IMEI)"
              value={imei}
              onChange={(e) => setImei(e.target.value)}
              className="text-right"
              dir="ltr"
            />
            <Button 
              onClick={() => handleSearch(imei)}
              disabled={!imei.trim() || isLoading}
            >
              <Barcode className="ml-2 h-4 w-4" />
              بحث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* عرض النتائج */}
      {isLoading && (
        <Card>
          <CardContent className="flex justify-center items-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-500">جاري تحميل البيانات...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* عرض معلومات الجهاز والأحداث */}
      {!isLoading && searchedImei && device && (
        <>
          {/* معلومات الجهاز */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-wrap justify-between items-start gap-2">
                <div>
                  <CardTitle className="text-right flex items-center gap-2">
                    <Package className="h-5 w-5 text-blue-600" />
                    معلومات الجهاز
                  </CardTitle>
                  <CardDescription className="text-right mt-1">
                    {searchedImei}
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/track?id=${searchedImei}`)}
                >
                  <FileText className="h-4 w-4 ml-2" />
                  عرض سجل الأحداث التفصيلي
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg border-r-4 border-blue-500">
                  <div className="font-semibold text-gray-700 mb-1">الموديل</div>
                  <p className="text-gray-800 font-medium">{device.model}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg border-r-4 border-green-500">
                  <div className="font-semibold text-gray-700 mb-1">الرقم التسلسلي</div>
                  <p className="text-gray-800 font-medium font-mono text-sm" dir="ltr">{searchedImei}</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg border-r-4 border-orange-500">
                  <div className="font-semibold text-gray-700 mb-1">الحالة الحالية</div>
                  <p className="text-gray-800 font-medium">{device.status || 'غير محدد'}</p>
                </div>
              </div>
              
              {/* الإحصائيات السريعة */}
              {deviceEvents.length > 0 && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold text-gray-800 mb-3">إحصائيات الأحداث</h3>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="bg-gray-100 text-gray-800 border-gray-200 text-xs py-1">
                      إجمالي العمليات: {deviceEvents.length}
                    </Badge>
                    {Object.entries(eventStats).map(([type, count]) => (
                      <Badge key={type} className={`${getTypeColor(type)} text-xs py-1 flex items-center gap-1`}>
                        {getTypeIcon(type)}
                        {count} {type === 'supply' ? 'توريد' : 
                                type === 'evaluation' ? 'فحص' :
                                type === 'maintenance' ? 'صيانة' :
                                type === 'maintenanceReceive' ? 'استلام من الصيانة' :
                                type === 'transfer' ? 'تحويل مخزني' :
                                type === 'sale' ? 'بيع' :
                                type === 'return' ? 'إرجاع' :
                                type === 'warehouseReceive' ? 'استلام للمخزن' :
                                type === 'warehouseDelivery' ? 'تسليم من المخزن' : 'أخرى'}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* جدول الأحداث */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-wrap justify-between items-center gap-4">
                <CardTitle className="text-right flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  سجل الأحداث والعمليات
                </CardTitle>

                {/* أدوات الفلترة والترتيب */}
                <div className="flex flex-wrap items-center gap-2">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="flex items-center gap-1">
                        <Filter className="h-4 w-4" />
                        {filterType === 'all' ? 'جميع الأنواع' : 
                         filterType === 'supply' ? 'توريد' : 
                         filterType === 'evaluation' ? 'فحص وتقييم' :
                         filterType === 'maintenance' ? 'صيانة' :
                         filterType === 'maintenanceReceive' ? 'استلام من الصيانة' :
                         filterType === 'transfer' ? 'تحويل مخزني' :
                         filterType === 'sale' ? 'بيع' :
                         filterType === 'return' ? 'إرجاع' :
                         filterType === 'warehouseReceive' ? 'استلام للمخزن' :
                         filterType === 'warehouseDelivery' ? 'تسليم من المخزن' : 'أخرى'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setFilterType('all')}>جميع الأنواع</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('supply')}>توريد</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('evaluation')}>فحص وتقييم</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('maintenance')}>إرسال للصيانة</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('maintenanceReceive')}>استلام من الصيانة</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('transfer')}>تحويل مخزني</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('sale')}>بيع</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('return')}>إرجاع</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('warehouseReceive')}>استلام للمخزن</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setFilterType('warehouseDelivery')}>تسليم من المخزن</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  
                  {uniqueUsers.length > 0 && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {filterUser === 'all' ? 'جميع المستخدمين' : filterUser}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setFilterUser('all')}>جميع المستخدمين</DropdownMenuItem>
                        {uniqueUsers.map((user, index) => (
                          <DropdownMenuItem key={index} onClick={() => setFilterUser(user)}>
                            {user}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                    className="flex items-center gap-1"
                  >
                    {sortOrder === 'desc' ? <ArrowDown className="h-4 w-4" /> : <ArrowUp className="h-4 w-4" />}
                    {sortOrder === 'desc' ? 'الأحدث أولاً' : 'الأقدم أولاً'}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {filteredEvents.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right whitespace-nowrap">النوع</TableHead>
                        <TableHead className="text-right whitespace-nowrap">العملية</TableHead>
                        <TableHead className="text-right whitespace-nowrap">التاريخ والوقت</TableHead>
                        <TableHead className="text-right whitespace-nowrap">المستخدم</TableHead>
                        <TableHead className="text-right whitespace-nowrap">رقم الأمر</TableHead>
                        <TableHead className="text-right whitespace-nowrap">الحالة</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredEvents.map((event) => (
                        <TableRow key={event.id}>
                          <TableCell>
                            <Badge className={`${getTypeColor(event.type)} whitespace-nowrap flex items-center gap-1`}>
                              {getTypeIcon(event.type)}
                              {event.typeName}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger>
                                  <div className="font-medium">{event.title}</div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p dir="rtl">{event.description}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </TableCell>
                          <TableCell className="whitespace-nowrap">
                            {formatDateTime(event.date, { arabic: true })}
                          </TableCell>
                          <TableCell className="whitespace-nowrap">
                            {event.user || 'غير معروف'}
                          </TableCell>
                          <TableCell className="whitespace-nowrap font-mono">
                            {event.orderNumber}
                          </TableCell>
                          <TableCell className="whitespace-nowrap">
                            {event.status}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-10">
                  <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  {deviceEvents.length > 0 ? (
                    <p className="text-gray-500">لا توجد أحداث تطابق معايير الفلترة</p>
                  ) : (
                    <p className="text-gray-500">لا يوجد سجل للعمليات على هذا الجهاز</p>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between pt-4 border-t">
              <div className="text-sm text-gray-500">
                {filteredEvents.length} من أصل {deviceEvents.length} عملية
              </div>
              
              {deviceEvents.length > 0 && (
                <Button variant="outline" size="sm" onClick={() => window.print()}>
                  <Printer className="h-4 w-4 ml-2" />
                  طباعة السجل
                </Button>
              )}
            </CardFooter>
          </Card>

          {/* نتائج مفصلة بالتبويبات */}
          {deviceEvents.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-right">تحليل مفصل للأحداث</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="table" dir="rtl">
                  <TabsList className="grid grid-cols-2 mb-4">
                    <TabsTrigger value="table">عرض الجدول</TabsTrigger>
                    <TabsTrigger value="timeline">العرض الزمني</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="table">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-right">التاريخ</TableHead>
                            <TableHead className="text-right">النوع</TableHead>
                            <TableHead className="text-right">الوصف</TableHead>
                            <TableHead className="text-right">المستخدم</TableHead>
                            <TableHead className="text-right">رقم الأمر</TableHead>
                            <TableHead className="text-right">تفاصيل إضافية</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredEvents.map((event) => (
                            <TableRow key={event.id}>
                              <TableCell className="whitespace-nowrap font-medium">
                                {formatDateTime(event.date, { arabic: true })}
                              </TableCell>
                              <TableCell>
                                <Badge className={getTypeColor(event.type)}>
                                  {event.typeName}
                                </Badge>
                              </TableCell>
                              <TableCell>{event.description}</TableCell>
                              <TableCell>{event.user}</TableCell>
                              <TableCell className="font-mono">{event.orderNumber}</TableCell>
                              <TableCell>
                                {event.details && (
                                  <ul className="text-xs space-y-1">
                                    {event.details.clientName && (
                                      <li>العميل: {event.details.clientName}</li>
                                    )}
                                    {event.details.supplierName && (
                                      <li>المورد: {event.details.supplierName}</li>
                                    )}
                                    {event.details.cost && (
                                      <li>التكلفة: {event.details.cost}</li>
                                    )}
                                    {event.details.repairCost && (
                                      <li>تكلفة الإصلاح: {event.details.repairCost}</li>
                                    )}
                                    {event.details.salePrice && (
                                      <li>سعر البيع: {event.details.salePrice}</li>
                                    )}
                                    {event.details.notes && (
                                      <li>ملاحظات: {event.details.notes}</li>
                                    )}
                                  </ul>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="timeline">
                    <div className="relative border-r-2 border-gray-200 mr-4">
                      {filteredEvents.map((event, index) => (
                        <div key={event.id} className="mb-8 relative">
                          <div className="absolute right-[-9px] top-2 w-4 h-4 rounded-full bg-white border-2 border-blue-500"></div>
                          <div className="mr-8 p-4 bg-white rounded-lg border shadow-sm">
                            <div className="flex flex-wrap justify-between items-start gap-2 mb-2">
                              <div className="flex items-center gap-2">
                                <Badge className={getTypeColor(event.type)}>
                                  {getTypeIcon(event.type)}
                                  <span className="mr-1">{event.typeName}</span>
                                </Badge>
                                <h4 className="font-semibold">{event.title}</h4>
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatDateTime(event.date, { arabic: true })}
                              </div>
                            </div>
                            
                            <p className="mb-2 text-gray-700">{event.description}</p>
                            
                            <div className="flex flex-wrap gap-4 text-xs text-gray-600">
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {event.user}
                              </div>
                              {event.orderNumber !== '-' && (
                                <div className="flex items-center gap-1">
                                  <FileText className="h-3 w-3" />
                                  {event.orderNumber}
                                </div>
                              )}
                              <div>{event.status}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* عرض عندما لا يوجد نتائج */}
      {!isLoading && searchedImei && !device && (
        <Card>
          <CardContent className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">لم يتم العثور على جهاز بهذا الرقم التسلسلي</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
