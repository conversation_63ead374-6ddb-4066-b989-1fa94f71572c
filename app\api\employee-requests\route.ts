import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع طلبات الموظفين مع ترتيب مختلف حسب النوع
    const orderBy = view === 'simple' 
      ? { employeeName: 'asc' as const }
      : { id: 'desc' as const };

    // استرجاع كل طلبات الموظفين من قاعدة البيانات، مرتبة تنازلياً
    const employeeRequests = await prisma.employeeRequest.findMany({
      orderBy
    });

    return NextResponse.json(employeeRequests);
  } catch (error) {
    console.error('Failed to fetch employee requests:', error);
    return NextResponse.json({ error: 'Failed to fetch employee requests' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newRequest = await request.json();

    // Basic validation
    if (!newRequest.requestType || !newRequest.priority || !newRequest.notes) {
      return NextResponse.json(
        { error: 'Request type, priority, and notes are required' },
        { status: 400 }
    );
    }

    const result = await executeInTransaction(async (tx) => {
      // توليد رقم طلب فريد
      const existingRequests = await tx.employeeRequest.findMany({
        select: { requestNumber: true }
      });

      const existingNumbers = existingRequests
        .map(req => {
          const match = req.requestNumber.match(/REQ-(\d+)$/);
          return match ? parseInt(match[1], 10) : 0;
        })
        .filter(num => num > 0);

      const maxRequestNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : 0;
      const requestNumber = `REQ-${maxRequestNumber + 1}`;

      // إنشاء الطلب الجديد
      const createdRequest = await tx.employeeRequest.create({
        data: {
          requestNumber,
          requestType: newRequest.requestType,
          priority: newRequest.priority,
          notes: newRequest.notes,
          status: 'قيد المراجعة',
          requestDate: new Date(),
          employeeName: newRequest.employeeName || 'مستخدم غير معروف',
          employeeId: newRequest.employeeId || 0,
          relatedOrderType: newRequest.relatedOrderType || null,
          relatedOrderId: newRequest.relatedOrderId || null,
          relatedOrderDisplayId: newRequest.relatedOrderDisplayId || null,
          attachmentName: newRequest.attachmentName || null,
          adminNotes: null,
          processedBy: null,
          processedDate: null
        }
      });

      return { success: true, request: createdRequest };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create employee request:', error);
    return NextResponse.json({ error: 'Failed to create employee request' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية لمعالجة الطلبات
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedRequest = await request.json();

    // Basic validation
    if (!updatedRequest.id || !updatedRequest.status) {
      return NextResponse.json(
        { error: 'Request ID and status are required' },
        { status: 400 }
    );
    }

    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود الطلب
      const existingRequest = await tx.employeeRequest.findUnique({
        where: { id: updatedRequest.id }
      });

      if (!existingRequest) {
        throw new Error('Employee request not found');
      }

      // تحديث الطلب
      const updated = await tx.employeeRequest.update({
        where: { id: updatedRequest.id },
        data: {
          status: updatedRequest.status,
          adminNotes: updatedRequest.adminNotes || null,
          processedBy: updatedRequest.processedBy || null,
          processedDate: new Date()
        }
      });

      return { success: true, request: updated };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update employee request:', error);
    return NextResponse.json({ error: 'Failed to update employee request' }, { status: 500 });
  }
}
