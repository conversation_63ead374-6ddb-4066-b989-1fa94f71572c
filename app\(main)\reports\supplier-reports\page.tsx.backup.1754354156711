
'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { isAfter, subDays, format, subMonths, subYears, startOfDay, endOfDay } from 'date-fns';
import { Printer, FileDown, Calendar as CalendarIcon, ChevronsUpDown, Check, Eye } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { EvaluatedDevice, Device, SystemSettings, SupplyOrder } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { DateRange } from 'react-day-picker';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';


type AugmentedEvaluatedDevice = EvaluatedDevice & { supplyDate?: string };

const SupplierDeviceDetailsDialog = ({
  open,
  onOpenChange,
  title,
  devices,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  devices: AugmentedEvaluatedDevice[];
}) => {
  const { systemSettings } = useStore();
  const handleExport = (action: 'print' | 'download') => {
    const doc = new jsPDF();
    doc.setR2L(true);

    const addHeader = () => {
      if (systemSettings.logoUrl) {
        try {
          doc.addImage(systemSettings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(systemSettings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(systemSettings.companyAddress, 190, 22, { align: 'right' });
      doc.text(systemSettings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    addHeader();

    doc.setFontSize(20);
    doc.text(title, 190, 45, { align: 'right' });

    const head = [
      [
        'تاريخ التوريد',
        'السبب/الوصف',
        'التقييم النهائي',
        'الموديل',
        'الرقم التسلسلي',
      ],
    ];
    const body = devices.map((device) => [
      device.supplyDate
        ? format(new Date(device.supplyDate), 'yyyy-MM-dd')
        : '-',
      device.fault || device.damageType || '-',
      device.finalGrade,
      device.model,
      device.deviceId,
    ]);

    autoTable(doc, {
      startY: 55,
      theme: 'grid',
      head: head,
      body: body,
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`${title.replace(/\s+/g, '_')}.pdf`);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            قائمة مفصلة بالأجهزة لهذا التصنيف من المورد المحدد.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] pr-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الرقم التسلسلي</TableHead>
                <TableHead>الموديل</TableHead>
                <TableHead>التقييم النهائي</TableHead>
                <TableHead>السبب/الوصف</TableHead>
                <TableHead>تاريخ التوريد</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {devices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    لا توجد سجلات لعرضها.
                  </TableCell>
                </TableRow>
              ) : (
                devices.map((device) => (
                  <TableRow key={device.deviceId}>
                    <TableCell dir="ltr">{device.deviceId}</TableCell>
                    <TableCell>{device.model}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          device.finalGrade === 'تالف'
                            ? 'destructive'
                            : device.finalGrade === 'يحتاج صيانة'
                              ? 'secondary'
                              : 'outline'
                        }
                      >
                        {device.finalGrade}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {device.fault || device.damageType || '-'}
                    </TableCell>
                    <TableCell>
                      {device.supplyDate
                        ? format(new Date(device.supplyDate), 'yyyy-MM-dd')
                        : '-'}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </ScrollArea>
        <DialogFooter className="gap-2 sm:justify-start">
          <Button variant="outline" onClick={() => handleExport('print')}>
            <Printer className="ml-2 h-4 w-4" /> طباعة
          </Button>
          <Button variant="outline" onClick={() => handleExport('download')}>
            <FileDown className="ml-2 h-4 w-4" /> تصدير
          </Button>
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const ChartWrapper = ({
  data,
  title,
}: {
  data: { name: string; count: number }[];
  title: string;
}) => (
  <Card>
    <CardHeader>
      <CardTitle className="text-base">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          layout="vertical"
          margin={{ right: 20, left: 40 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" allowDecimals={false} />
          <YAxis
            type="category"
            dataKey="name"
            width={100}
            tick={{ fontSize: 12 }}
          />
          <Tooltip />
          <Legend />
          <Bar dataKey="count" name="العدد" fill="hsl(var(--primary))" />
        </BarChart>
      </ResponsiveContainer>
    </CardContent>
  </Card>
);

export default function SupplierReports() {
  const { suppliers, devices, evaluationOrders, supplyOrders, systemSettings } =
    useStore();
  const [selectedSupplier, setSelectedSupplier] = useState('all');
  const [selectedModel, setSelectedModel] = useState('all');
  const [selectedSupplyOrder, setSelectedSupplyOrder] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange | undefined>();

  const [isSupplierSearchOpen, setIsSupplierSearchOpen] = useState(false);
  const [isModelSearchOpen, setIsModelSearchOpen] = useState(false);

  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [detailsTitle, setDetailsTitle] = useState('');
  const [detailsDevices, setDetailsDevices] = useState<
    AugmentedEvaluatedDevice[]
  >([]);
    
  const [isAllOrdersOpen, setIsAllOrdersOpen] = useState(false);


  const modelOptions = useMemo(() => {
    return [...new Set(devices.map((d) => d.model))].sort();
  }, [devices]);
  
  const supplierSupplyOrders = useMemo(() => {
    if (selectedSupplier === 'all') return [];
    return supplyOrders.filter(o => o.supplierId.toString() === selectedSupplier);
  }, [selectedSupplier, supplyOrders]);

  const supplierData = useMemo(() => {
    if (selectedSupplier === 'all') return null;

    const supplierId = parseInt(selectedSupplier, 10);
    
    const getSinceDate = () => {
        if (timeFilter === 'all') return null;
        if (timeFilter === 'custom' && customDateRange?.from) return startOfDay(customDateRange.from);
        const now = new Date();
        if (timeFilter === '30') return subDays(now, 30);
        if (timeFilter === '180') return subDays(now, 180);
        if (timeFilter === '365') return subYears(now, 1);
        return null;
    };
    
    const getEndDate = () => {
        if (timeFilter === 'custom' && customDateRange?.to) return endOfDay(customDateRange.to);
        return new Date();
    };

    const sinceDate = getSinceDate();
    const endDate = getEndDate();

    let relevantSupplyOrders = supplyOrders.filter((o) => {
      if (o.supplierId !== supplierId) return false;
      const orderDate = new Date(o.supplyDate);
      if (sinceDate && orderDate < sinceDate) return false;
      if (endDate && orderDate > endDate) return false;
      return true;
    });

    if (selectedSupplyOrder !== 'all') {
      relevantSupplyOrders = relevantSupplyOrders.filter(o => o.id.toString() === selectedSupplyOrder);
    }

    const suppliedDeviceIdsFromOrders = new Set(
      relevantSupplyOrders.flatMap((o) => (Array.isArray(o.items) ? o.items : []).map((i) => i.imei))
    );

    const suppliedDevices = devices.filter((d) => {
      if (!suppliedDeviceIdsFromOrders.has(d.id)) return false;
      if (selectedModel !== 'all' && d.model !== selectedModel) return false;
      return true;
    });

    if (suppliedDevices.length === 0) return {
        totalSupplied: 0,
        quality: 100,
        damaged: { count: 0, devices: [] },
        defective: { count: 0, devices: [] },
        needsMaintenance: { count: 0, devices: [] },
        breakdown: [],
        supplyOrders: relevantSupplyOrders,
      };

    const suppliedDeviceIds = new Set(suppliedDevices.map((d) => d.id));
    const evals = evaluationOrders
      .flatMap((o) => o.items)
      .filter((i) => suppliedDeviceIds.has(i.deviceId));

    const augmentedEvals: AugmentedEvaluatedDevice[] = evals.map((e) => {
      const order = relevantSupplyOrders.find((o) =>
        (Array.isArray(o.items) && o.items.some((i) => i.imei === e.deviceId))
      );
      return { ...e, supplyDate: order?.supplyDate };
    });

    const damagedDevices = augmentedEvals.filter(
      (e) => e.finalGrade === 'تالف'
    );
    const defectiveDevices = augmentedEvals.filter(
      (e) => e.finalGrade === 'عيب فني'
    );
    const maintenanceDevices = augmentedEvals.filter(
      (e) => e.finalGrade === 'يحتاج صيانة'
    );

    const totalFaulty =
      damagedDevices.length +
      defectiveDevices.length +
      maintenanceDevices.length;
    const quality =
      suppliedDevices.length > 0
        ? 100 - (totalFaulty / suppliedDevices.length) * 100
        : 100;

    const breakdown = augmentedEvals
      .filter((e) => e.fault || e.damageType)
      .reduce(
        (acc, curr) => {
          const key = curr.fault || curr.damageType || 'غير محدد';
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
    );

    return {
      totalSupplied: suppliedDevices.length,
      quality: Math.round(quality),
      damaged: { count: damagedDevices.length, devices: damagedDevices },
      defective: { count: defectiveDevices.length, devices: defectiveDevices },
      needsMaintenance: {
        count: maintenanceDevices.length,
        devices: maintenanceDevices,
      },
      breakdown: Object.entries(breakdown).map(([name, count]) => ({
        name,
        count,
      })),
      supplyOrders: relevantSupplyOrders.sort((a,b) => new Date(b.supplyDate).getTime() - new Date(a.supplyDate).getTime()),
    };
  }, [
    selectedSupplier,
    selectedModel,
    selectedSupplyOrder,
    timeFilter,
    customDateRange,
    suppliers,
    devices,
    evaluationOrders,
    supplyOrders,
  ]);

  const handleOpenDetails = (
    title: string,
    devices: AugmentedEvaluatedDevice[],
  ) => {
    setDetailsTitle(title);
    setDetailsDevices(devices);
    setIsDetailsOpen(true);
  };

  const handlePrintSummary = (action: 'print' | 'download') => {
    if (!supplierData) return;
    const doc = new jsPDF();
    doc.setR2L(true);

    const addHeader = () => {
      if (systemSettings.logoUrl) {
        try {
          doc.addImage(systemSettings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(systemSettings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(systemSettings.companyAddress, 190, 22, { align: 'right' });
      doc.text(systemSettings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    addHeader();

    const supplierName =
      suppliers.find((s) => s.id.toString() === selectedSupplier)?.name ||
      'كل الموردين';
    const title = `تقرير المورد: ${supplierName}`;

    doc.setFontSize(20);
    doc.text(title, 190, 45, { align: 'right' });

    const summaryBody = [
      [supplierData.totalSupplied, 'إجمالي الأجهزة الموردة'],
      [supplierData.damaged.count, 'أجهزة تالفة'],
      [supplierData.defective.count, 'أجهزة معيوبة'],
      [supplierData.needsMaintenance.count, 'تحتاج صيانة'],
      [`${supplierData.quality}%`, 'مؤشر الجودة'],
    ];

    autoTable(doc, {
      startY: 55,
      theme: 'grid',
      head: [['العدد', 'الإحصائية']],
      body: summaryBody,
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });

    if (supplierData.breakdown.length > 0) {
      doc.addPage();
      doc.setR2L(true);
      doc.text('تفاصيل الأعطال والعيوب', 190, 20, { align: 'right' });
      autoTable(doc, {
        startY: 30,
        theme: 'grid',
        head: [['العدد', 'العطل/العيب']],
        body: supplierData.breakdown.map((item) => [item.count, item.name]),
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      });
    }

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`supplier_report_${supplierName.replace(/\s+/g, '_')}.pdf`);
    }
  };

  return (
    <>
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>فلاتر التقرير</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>اختيار المورد</Label>
               <Popover open={isSupplierSearchOpen} onOpenChange={setIsSupplierSearchOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" role="combobox" className="w-full justify-between">
                    {selectedSupplier !== 'all' ? suppliers.find(s => s.id.toString() === selectedSupplier)?.name : 'اختر موردًا...'}
                    <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                  <Command>
                    <CommandInput placeholder="بحث عن مورد..." />
                    <CommandList>
                      <CommandEmpty>لا يوجد مورد بهذا الاسم.</CommandEmpty>
                      <CommandGroup>
                        <CommandItem key="all" value="كل الموردين" onSelect={() => { setSelectedSupplier('all'); setSelectedSupplyOrder('all'); setIsSupplierSearchOpen(false); }}>
                          <Check className={cn('ml-2 h-4 w-4', selectedSupplier === 'all' ? 'opacity-100' : 'opacity-0')} />
                           كل الموردين
                        </CommandItem>
                        {suppliers.map(s => (
                          <CommandItem key={s.id} value={s.name} onSelect={() => { setSelectedSupplier(s.id.toString()); setSelectedSupplyOrder('all'); setIsSupplierSearchOpen(false); }}>
                            <Check className={cn('ml-2 h-4 w-4', selectedSupplier === s.id.toString() ? 'opacity-100' : 'opacity-0')} />
                            {s.name}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
            <div className="space-y-2">
              <Label>اختيار الموديل</Label>
              <Popover open={isModelSearchOpen} onOpenChange={setIsModelSearchOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" role="combobox" className="w-full justify-between">
                    {selectedModel !== 'all' ? selectedModel : 'اختر موديلًا...'}
                    <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                  <Command>
                    <CommandInput placeholder="بحث عن موديل..." />
                    <CommandList>
                      <CommandEmpty>لا يوجد موديل بهذا الاسم.</CommandEmpty>
                      <CommandGroup>
                        <CommandItem key="all" value="كل الموديلات" onSelect={() => { setSelectedModel('all'); setIsModelSearchOpen(false); }}>
                          <Check className={cn('ml-2 h-4 w-4', selectedModel === 'all' ? 'opacity-100' : 'opacity-0')} />
                           كل الموديلات
                        </CommandItem>
                        {modelOptions.map((model) => (
                          <CommandItem key={model} value={model} onSelect={() => { setSelectedModel(model); setIsModelSearchOpen(false); }}>
                            <Check className={cn('ml-2 h-4 w-4', selectedModel === model ? 'opacity-100' : 'opacity-0')} />
                            {model}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
             <div className="space-y-2">
              <Label>أمر التوريد</Label>
              <Select dir="rtl" value={selectedSupplyOrder} onValueChange={setSelectedSupplyOrder} disabled={selectedSupplier === 'all'}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر أمر توريد" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل أوامر التوريد</SelectItem>
                  {supplierSupplyOrders.map((order) => (
                    <SelectItem key={order.id} value={order.id.toString()}>
                      {order.supplyOrderId} - {formatDate(new Date(order.supplyDate))}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>الفترة الزمنية</Label>
              <Select dir="rtl" value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل الأوقات</SelectItem>
                  <SelectItem value="30">آخر شهر</SelectItem>
                  <SelectItem value="180">آخر 6 أشهر</SelectItem>
                  <SelectItem value="365">آخر سنة</SelectItem>
                  <SelectItem value="custom">تاريخ مخصص</SelectItem>
                </SelectContent>
              </Select>
            </div>
             {timeFilter === 'custom' && (
              <div className="space-y-2">
                <Label>تحديد التاريخ</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button id="date" variant={"outline"} className={cn("w-full justify-start text-left font-normal", !customDateRange && "text-muted-foreground")}>
                      <CalendarIcon className="ml-2 h-4 w-4" />
                      {customDateRange?.from ? (
                        customDateRange.to ? (
                          <>
                            {format(customDateRange.from, "LLL dd, y")} -{" "}
                            {format(customDateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(customDateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>اختر فترة</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={customDateRange?.from}
                      selected={customDateRange}
                      onSelect={setCustomDateRange}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )}
          </CardContent>
        </Card>

        {supplierData ? (
          <>
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-5">
              <Card>
                <CardHeader>
                  <CardTitle>إجمالي الأجهزة الموردة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold">
                    {supplierData.totalSupplied}
                  </p>
                </CardContent>
              </Card>
              <Card
                className="cursor-pointer transition-colors hover:bg-card/80"
                onClick={() =>
                  handleOpenDetails(
                    `تفاصيل الأجهزة التالفة من المورد`,
                    supplierData.damaged.devices,
                  )
                }
              >
                <CardHeader>
                  <CardTitle>أجهزة تالفة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold">
                    {supplierData.damaged.count}
                  </p>
                </CardContent>
              </Card>
              <Card
                className="cursor-pointer transition-colors hover:bg-card/80"
                onClick={() =>
                  handleOpenDetails(
                    `تفاصيل الأجهزة المعيوبة من المورد`,
                    supplierData.defective.devices,
                  )
                }
              >
                <CardHeader>
                  <CardTitle>أجهزة معيوبة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold">
                    {supplierData.defective.count}
                  </p>
                </CardContent>
              </Card>
              <Card
                className="cursor-pointer transition-colors hover:bg-card/80"
                onClick={() =>
                  handleOpenDetails(
                    `تفاصيل الأجهزة التي تحتاج صيانة من المورد`,
                    supplierData.needsMaintenance.devices,
                  )
                }
              >
                <CardHeader>
                  <CardTitle>تحتاج صيانة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold">
                    {supplierData.needsMaintenance.count}
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>مؤشر الجودة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold">{supplierData.quality}%</p>
                </CardContent>
              </Card>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {supplierData.breakdown.length > 0 && (
                <ChartWrapper
                    data={supplierData.breakdown}
                    title="تفاصيل الأعطال والعيوب"
                />
                )}
                <Card>
                    <CardHeader>
                        <CardTitle>أوامر التوريد</CardTitle>
                        <CardDescription>قائمة بأوامر التوريد للمورد المحدد</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ScrollArea className="h-72">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>رقم الأمر</TableHead>
                                        <TableHead>التاريخ</TableHead>
                                        <TableHead>الكمية</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {supplierData.supplyOrders.slice(0, 5).map((order) => (
                                        <TableRow key={order.id}>
                                            <TableCell>{order.supplyOrderId}</TableCell>
                                            <TableCell>{format(new Date(order.supplyDate), 'yyyy/MM/dd')}</TableCell>
                                            <TableCell>{(Array.isArray(order.items) ? order.items.length : 0)}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </ScrollArea>
                    </CardContent>
                    <CardFooter className="justify-between">
                         <span className="text-sm text-muted-foreground">
                            إجمالي الأجهزة: {supplierData.supplyOrders.reduce((acc, o) => acc + (Array.isArray(o.items) ? o.items.length : 0), 0)}
                        </span>
                        {supplierData.supplyOrders.length > 5 && (
                        <Button variant="outline" size="sm" onClick={() => setIsAllOrdersOpen(true)}>
                            <Eye className="mr-2 h-4 w-4" />
                            عرض الكل
                        </Button>
                        )}
                    </CardFooter>
                </Card>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>الإجراءات</CardTitle>
              </CardHeader>
              <CardContent className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => handlePrintSummary('print')}
                >
                  <Printer className="ml-2 h-4 w-4" /> طباعة
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handlePrintSummary('download')}
                >
                  <FileDown className="ml-2 h-4 w-4" /> تصدير
                </Button>
              </CardContent>
            </Card>
          </>
        ) : (
          <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
              يرجى اختيار مورد لعرض تقريره المفصل.
            </CardContent>
          </Card>
        )}
      </div>
      <SupplierDeviceDetailsDialog
        open={isDetailsOpen}
        onOpenChange={setIsDetailsOpen}
        title={detailsTitle}
        devices={detailsDevices}
      />
      <Dialog open={isAllOrdersOpen} onOpenChange={setIsAllOrdersOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>جميع أوامر التوريد للمورد</DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[70vh]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الأمر</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الكمية</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {supplierData?.supplyOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>{order.supplyOrderId}</TableCell>
                    <TableCell>{format(new Date(order.supplyDate), 'yyyy/MM/dd')}</TableCell>
                    <TableCell>{(Array.isArray(order.items) ? order.items.length : 0)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
           <DialogFooter>
            <DialogClose asChild>
                <Button variant="outline">إغلاق</Button>
            </DialogClose>
           </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}