// فحص قاعدة البيانات لرؤية أرقام الأوامر المحفوظة
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSalesData() {
  try {
    console.log('🔍 فحص بيانات المبيعات المحفوظة...\n');

    // جلب آخر 5 فواتير
    const sales = await prisma.sale.findMany({
      orderBy: { id: 'desc' },
      take: 5,
      select: {
        id: true,
        soNumber: true,
        opNumber: true,
        clientName: true,
        createdAt: true
      }
    });

    if (sales.length === 0) {
      console.log('❌ لا توجد فواتير في قاعدة البيانات');
      return;
    }

    console.log('📊 آخر 5 فواتير:');
    console.log('════════════════════════════════════════════════════════════════');
    
    sales.forEach((sale, index) => {
      console.log(`${index + 1}. ID: ${sale.id}`);
      console.log(`   رقم الفاتورة (SO): ${sale.soNumber}`);
      console.log(`   رقم الأمر (OP): ${sale.opNumber}`);
      console.log(`   العميل: ${sale.clientName}`);
      console.log(`   تاريخ الإنشاء: ${sale.createdAt.toLocaleString('ar-SA')}`);
      console.log(`   نوع بيانات opNumber: ${typeof sale.opNumber}`);
      console.log(`   طول opNumber: ${sale.opNumber ? sale.opNumber.length : 'null'}`);
      console.log('────────────────────────────────────────────────────────────────');
    });

    // فحص إضافي للتحقق من البيانات
    console.log('\n🔬 تحليل تفصيلي لأحدث فاتورة:');
    const latestSale = sales[0];
    console.log(`Raw opNumber: "${latestSale.opNumber}"`);
    console.log(`هل opNumber رقم؟ ${!isNaN(latestSale.opNumber)}`);
    console.log(`هل opNumber يحتوي على أحرف؟ ${/[a-zA-Z]/.test(latestSale.opNumber)}`);
    console.log(`هل opNumber يحتوي على رموز؟ ${/[^0-9]/.test(latestSale.opNumber)}`);

  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSalesData();
