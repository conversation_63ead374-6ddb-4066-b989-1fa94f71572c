{"timestamp": "2025-08-05T01:55:31.987Z", "totalIssues": 65, "critical": 12, "high": 44, "medium": 9, "issues": [{"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\analyze-evaluation-issue.js", "line": 28, "content": "console.log(`   التاريخ: ${order.date} (${new Date(order.date).toISOString()})`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\analyze-evaluation-issue.js", "line": 58, "content": "console.log(`   التاريخ: ${order.date} (${new Date(order.date).toISOString()})`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\grading\\2page.tsx", "line": 680, "content": "doc.text(`التاريخ: ${new Date()}`, 190, 59, {", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\grading\\2page.tsx", "line": 905, "content": "defaultValue={new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\grading\\page.tsx", "line": 227, "content": "description: `تم تحميل المسودة المحفوظة بتاريخ ${new Date(draftData.timestamp)}`,", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\grading\\page.tsx", "line": 723, "content": "doc.text(`التاريخ: ${new Date()}`, 190, 59, {", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\grading\\page.tsx", "line": 907, "content": "defaultValue={new Date().toISOString().slice(0, 16)}", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 724, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 741, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1818, "content": "doc.save(`${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.pdf`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1852, "content": "XLSX.writeFile(workbook, `${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page_backup.tsx", "line": 373, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page_backup.tsx", "line": 390, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page_backup.tsx", "line": 932, "content": "doc.save(`all_devices_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page_backup.tsx", "line": 953, "content": "`all_devices_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\maintenance\\page.tsx", "line": 1665, "content": "التاريخ: ${new Date()}", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\reports\\employee-reports\\page.tsx", "line": 109, "content": "doc.save(`employee_productivity_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\reports\\grading-reports\\page.tsx", "line": 204, "content": "doc.save(`grading_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\settings\\appearance-settings.tsx", "line": 593, "content": "`<div class=\"timestamp\">تم إنشاء التقرير في: ${new Date()}</div>` : ''", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\supply\\page.tsx", "line": 947, "content": "`تاريخ الطباعة: ${new Date()}`,", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\api\\database\\backup\\route.ts", "line": 101, "content": "name: name || `نسخة احتياطية - ${new Date()}`,", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\database-management-backup.tsx", "line": 535, "content": "name: backupForm.name || `نسخة احتياطية - ${new Date()}`,", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\database-management-backup.tsx", "line": 999, "content": "placeholder={`نسخة احتياطية - ${new Date()}`}", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\database-management-new.tsx", "line": 418, "content": "name: backupForm.name || `نسخة احتياطية - ${new Date()}`,", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\database-management-new.tsx", "line": 1073, "content": "placeholder={`نسخة احتياطية - ${new Date()}`}", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\context\\store.tsx", "line": 941, "content": "console.log(`   أمر التقييم: ${evalOrder.orderNumber || evalOrder.id} - تاريخ: ${new Date(evalOrder.date)}`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\debug-specific-orders.js", "line": 31, "content": "console.log(`   التاريخ: ${evaluationOrder.date} (${new Date(evaluationOrder.date).toISOString()})`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\debug-specific-orders.js", "line": 63, "content": "console.log(`   ${order.orderNumber || order.id}: ${order.date} (${new Date(order.date).toISOString()})`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\debug-specific-orders.js", "line": 96, "content": "console.log(`  - كـ Date: ${new Date(evaluationOrder.date)}`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\debug-specific-orders.js", "line": 98, "content": "console.log(`  - كـ timestamp: ${new Date(evaluationOrder.date).getTime()}`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\final-error-check.js", "line": 28, "content": "pattern: /dateTimeString/g,", "description": "متغير dateTimeString غير معرف", "severity": "critical", "pattern": "dateTimeString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\final-error-check.js", "line": 38, "content": "pattern: /const formatDateTime = \\([^)]*\\): string => \\{[^}]*dateTimeString/g,", "description": "متغير dateTimeString غير معرف", "severity": "critical", "pattern": "dateTimeString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\final-error-check.js", "line": 33, "content": "pattern: /dateString/g,", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\final-error-check.js", "line": 34, "content": "description: 'متغير dateString محتمل غير معرف',", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 12, "content": "'dateTimeString',", "description": "متغير dateTimeString غير معرف", "severity": "critical", "pattern": "dateTimeString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 13, "content": "'dateString',", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-all-remaining-issues-final.js", "line": 55, "content": "replace: 'defaultValue={new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input',", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-main-pages.js", "line": 74, "content": "search: /const formatDateTime = \\(dateTimeString: string\\): string => \\{[\\s\\S]*?\\};/g,", "description": "متغير dateTimeString غير معرف", "severity": "critical", "pattern": "dateTimeString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-medium-priority-issues.js", "line": 139, "content": "search: /\\(dateTimeString: string\\)/g,", "description": "متغير dateTimeString غير معرف", "severity": "critical", "pattern": "dateTimeString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-medium-priority-issues.js", "line": 134, "content": "search: /\\(dateString: string\\)/g,", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-remaining-components.js", "line": 156, "content": "search: /const formatDateTime = \\(dateTimeString: string\\): string/g,", "description": "متغير dateTimeString غير معرف", "severity": "critical", "pattern": "dateTimeString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 18, "content": "const convertToISO = (dateString, fieldName) => {", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 21, "content": "if (dateString.match(/^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$/)) {", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 22, "content": "const isoDate = new Date(dateString.replace(' ', 'T') + '.000Z');", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 24, "content": "console.log(`   - ${fieldName} للمرتجع ${ret.roNumber}: سيتم تحويل \"${dateString}\" إلى ${isoDate.toISOString()}`);", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 25, "content": "description: 'إصلاح new Date().slice(0, 16) إلى new Date().toISOString().slice(0, 16)'", "description": "استخدام .slice() على Date object مباشرة", "severity": "critical", "pattern": "new Date\\([^)]*\\)\\.slice"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 30, "content": "description: 'إصلاح new Date().slice(0, 10) إلى new Date().toISOString().slice(0, 10)'", "description": "استخدام .slice() على Date object مباشرة", "severity": "critical", "pattern": "new Date\\([^)]*\\)\\.slice"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 36, "content": "replace: 'defaultValue={new Date().toISOString().slice(0, 16)}',", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 73, "content": "replace: 'doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`)',", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 78, "content": "replace: '`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`',", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 83, "content": "replace: 'doc.save(`${deviceDetails?.title.replace(/\\\\s+/g, \\'_\\')}_${new Date().toISOString().slice(0, 10)}.pdf`)',", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-slice-errors-urgent.js", "line": 88, "content": "replace: '`${deviceDetails?.title.replace(/\\\\s+/g, \\'_\\')}_${new Date().toISOString().slice(0, 10)}.xlsx`',", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-split-errors-urgent.js", "line": 30, "content": "type: 'new Date().split()'", "description": "استخدام .split() على Date object مباشرة", "severity": "critical", "pattern": "new Date\\([^)]*\\)\\.split"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-split-errors-urgent.js", "line": 69, "content": "description: 'إصلاح new Date().split(\\'T\\')[0] إلى new Date().toISOString().split(\\'T\\')[0]'", "description": "استخدام .split() على Date object مباشرة", "severity": "critical", "pattern": "new Date\\([^)]*\\)\\.split"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-split-errors-urgent.js", "line": 74, "content": "description: 'إصلاح new Date().split(\\'T\\')[1] إلى new Date().toISOString().split(\\'T\\')[1]'", "description": "استخدام .split() على Date object مباشرة", "severity": "critical", "pattern": "new Date\\([^)]*\\)\\.split"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-split-errors-urgent.js", "line": 79, "content": "description: 'إصلاح new Date().split( إلى new Date().toISOString().split('", "description": "استخدام .split() على Date object مباشرة", "severity": "critical", "pattern": "new Date\\([^)]*\\)\\.split"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-string-date-types.js", "line": 132, "content": "search: /const formatUploadDate = \\(dateString: Date \\| string\\): string/g,", "description": "متغير dateString محتمل غير معرف", "severity": "medium", "pattern": "dateString"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\hooks\\usePrintExport.ts", "line": 258, "content": "${includeTimestamp ? `<p class=\"print-subtitle\">تم الطباعة في: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</p>` : ''}", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\simple-database-check.js", "line": 37, "content": "(${roNumber}, ${roNumber}, ${new Date()}, ${1}, ${'SO-FINAL-TEST'}, ${'عميل اختبار نهائي'}, ${'مخزن اختبار'}, ${'مرتجع اختبار نهائي'}, ${'معلق'}, ${'مختبر النظام'})", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\simple-database-check.js", "line": 65, "content": "(${doNumber}, ${new Date()}, ${'موقع اختبار'}, ${'أمر تسليم اختبار نهائي'}, ${'معلق'}, ${'مختبر النظام'})", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\test-all-return-operations.js", "line": 28, "content": "(${roNumber}, ${roNumber}, ${new Date().toISOString()}, ${1}, ${'SO-TEST'}, ${'عميل تجريبي'}, ${'مخزن تجريبي'}, ${'مرتجع تجريبي'}, ${'معلق'}, ${null}, ${null}, ${'مختبر النظام'}, ${null})", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\test-deletion-logic.js", "line": 27, "content": "console.log(`   التاريخ: ${maintenanceOrder.date} (${new Date(maintenanceOrder.date).toISOString()})`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\test-return-api-complete.js", "line": 74, "content": "(${roNumber}, ${roNumber}, ${new Date()}, ${1}, ${'SO-TEST'}, ${'عميل تجريبي'}, ${'مخزن تجريبي'}, ${'مرتجع تجريبي'}, ${'معلق'}, ${null}, ${null}, ${'مختبر النظام'}, ${null})", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\test-sale-api.js", "line": 49, "content": "console.log(`   التاريخ: ${new Date(result.date).toLocaleString('ar-SA')}`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\test-sale-detailed.js", "line": 52, "content": "console.log(`   التاريخ: ${new Date(result.date).toLocaleString('ar-SA')}`);", "description": "عرض Date object مباشرة في JSX", "severity": "high", "pattern": "\\{new Date\\([^}]*\\)\\}"}]}