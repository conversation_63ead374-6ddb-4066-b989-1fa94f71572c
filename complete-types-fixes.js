/**
 * Complete Types Fixes Script
 * Date: 2025-08-04
 * Description: Complete fixing all remaining date type issues in lib/types.ts
 */

const fs = require('fs');
const path = require('path');

// Complete fixes for lib/types.ts
const typesFixes = [
  // Fix remaining string date types
  {
    search: /createdAt\?: string;/g,
    replace: 'createdAt?: Date;',
    description: 'تحويل createdAt إلى Date'
  },
  {
    search: /returnDate: string;/g,
    replace: 'returnDate: Date;',
    description: 'تحويل returnDate إلى Date'
  },
  {
    search: /supplyDate: string;/g,
    replace: 'supplyDate: Date;',
    description: 'تحويل supplyDate إلى Date'
  },
  {
    search: /repairDate: string;/g,
    replace: 'repairDate: Date;',
    description: 'تحويل repairDate إلى Date'
  },
  {
    search: /acknowledgedDate\?: string;/g,
    replace: 'acknowledgedDate?: Date;',
    description: 'تحويل acknowledgedDate إلى Date'
  },
  {
    search: /resolutionDate\?: string;/g,
    replace: 'resolutionDate?: Date;',
    description: 'تحويل resolutionDate إلى Date'
  },
  {
    search: /soldDate\?: string;/g,
    replace: 'soldDate?: Date;',
    description: 'تحويل soldDate إلى Date'
  },
  {
    search: /startTime: string;/g,
    replace: 'startTime: Date;',
    description: 'تحويل startTime إلى Date'
  },
  {
    search: /endTime: string \| null;/g,
    replace: 'endTime: Date | null;',
    description: 'تحويل endTime إلى Date'
  },
  {
    search: /scheduledDate\?: string;/g,
    replace: 'scheduledDate?: Date;',
    description: 'تحويل scheduledDate إلى Date'
  },
  {
    search: /dateFrom\?: string;/g,
    replace: 'dateFrom?: Date;',
    description: 'تحويل dateFrom إلى Date'
  },
  {
    search: /dateTo\?: string;/g,
    replace: 'dateTo?: Date;',
    description: 'تحويل dateTo إلى Date'
  },
  {
    search: /lastLogin\?: string;/g,
    replace: 'lastLogin?: Date;',
    description: 'تحويل lastLogin إلى Date'
  }
];

// Additional utility type fixes
const utilityTypesFixes = [
  {
    search: /export type DateField = Date \| string;/g,
    replace: 'export type DateField = Date; // استخدم Date فقط للاتساق',
    description: 'توحيد نوع DateField'
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    console.log(`   📁 نسخة احتياطية: ${backupPath}\n`);
    
    return true;
  }

  return false;
}

async function completeTypesFixes() {
  console.log('🔧 إكمال إصلاح lib/types.ts...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    const typesFilePath = path.join(process.cwd(), 'lib/types.ts');
    
    console.log('🔍 إصلاح أنواع البيانات الأساسية...');
    if (applyFixes(typesFilePath, typesFixes)) {
      totalFixed += typesFixes.length;
      filesModified.push('lib/types.ts');
    }
    
    console.log('🔍 إصلاح الأنواع المساعدة...');
    if (applyFixes(typesFilePath, utilityTypesFixes)) {
      totalFixed += utilityTypesFixes.length;
      if (!filesModified.includes('lib/types.ts')) {
        filesModified.push('lib/types.ts');
      }
    }

    // Add comprehensive date utility types
    console.log('🔍 إضافة أنواع مساعدة للتواريخ...');
    let content = fs.readFileSync(typesFilePath, 'utf8');
    
    const dateUtilityTypes = `
// ===== أنواع مساعدة للتواريخ =====
export type DateInput = Date | string | number;
export type DateRange = {
  from?: Date;
  to?: Date;
};

export type DateFormatOptions = {
  arabic?: boolean;
  format?: 'full' | 'short' | 'time' | 'date';
  timezone?: string;
};

// نوع موحد للتواريخ في الواجهات
export interface DateFields {
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

// نوع للعمليات التي تتطلب تواريخ
export interface TimestampedOperation {
  timestamp: Date;
  operation: string;
  userId?: number;
}
`;

    if (!content.includes('أنواع مساعدة للتواريخ')) {
      content += dateUtilityTypes;
      fs.writeFileSync(typesFilePath, content);
      console.log('✅ تم إضافة أنواع مساعدة للتواريخ');
      totalFixed += 5;
    }

    // Generate summary
    console.log('📊 ملخص إصلاح lib/types.ts:');
    console.log('='.repeat(35));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      typesFixes: typesFixes,
      utilityTypesFixes: utilityTypesFixes
    };

    fs.writeFileSync('complete-types-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح الأنواع في: complete-types-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إكمال إصلاح lib/types.ts بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح في lib/types.ts');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح lib/types.ts:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  completeTypesFixes()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح lib/types.ts');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح lib/types.ts:', error);
      process.exit(1);
    });
}

module.exports = { completeTypesFixes };
