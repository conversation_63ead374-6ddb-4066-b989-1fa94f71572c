const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkReturnTable() {
  try {
    console.log('🔍 Checking Return table structure...');
    
    const columns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'Return' 
      ORDER BY ordinal_position
    `;
    
    console.log('\nReturn table columns:');
    columns.forEach(col => {
      console.log(`- ${col.column_name} (${col.data_type}) ${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    // Check if attachments column exists
    const hasAttachmentsColumn = columns.some(col => col.column_name === 'attachments');
    console.log(`\n📎 Attachments column exists: ${hasAttachmentsColumn}`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkReturnTable();
