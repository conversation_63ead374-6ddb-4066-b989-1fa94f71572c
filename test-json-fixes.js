/**
 * Test JSON Fixes Script
 * Date: 2025-08-04
 * Description: Test the new relational structure instead of JSON
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testJsonFixes() {
  console.log('🧪 اختبار إصلاحات JSON...\n');

  try {
    // 1. Test User permissions (no more JSON)
    console.log('1️⃣ اختبار صلاحيات المستخدمين...');
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' },
      include: {
        userPermissions: {
          include: {
            permission: true
          }
        },
        userWarehouseAccess: {
          include: {
            warehouse: true
          }
        }
      }
    });

    if (adminUser) {
      console.log(`   ✅ المستخدم: ${adminUser.name}`);
      console.log(`   ✅ الصلاحيات: ${adminUser.userPermissions.length}`);
      console.log(`   ✅ المخازن: ${adminUser.userWarehouseAccess.length}`);
    } else {
      console.log('   ❌ لم يتم العثور على المستخدم الإداري');
    }

    // 2. Test Internal Messages with recipients
    console.log('\n2️⃣ اختبار الرسائل الداخلية...');
    
    // Create a test message with recipients
    const testMessage = await prisma.internalMessage.create({
      data: {
        threadId: 1,
        senderId: adminUser?.id || 1,
        senderName: adminUser?.name || 'Admin',
        recipientId: 0,
        recipientName: 'Test Group',
        text: 'رسالة اختبار للنظام الجديد',
        sentDate: new Date(),
        status: 'مرسلة',
        isRead: false
      }
    });

    // Add recipients using relations instead of JSON
    if (adminUser) {
      await prisma.messageRecipient.create({
        data: {
          messageId: testMessage.id,
          userId: adminUser.id,
          isRead: false
        }
      });
    }

    // Fetch message with recipients
    const messageWithRecipients = await prisma.internalMessage.findUnique({
      where: { id: testMessage.id },
      include: {
        recipients: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    });

    if (messageWithRecipients) {
      console.log(`   ✅ الرسالة: ${messageWithRecipients.text}`);
      console.log(`   ✅ المستقبلين: ${messageWithRecipients.recipients.length}`);
      messageWithRecipients.recipients.forEach(recipient => {
        console.log(`      - ${recipient.user?.name} (${recipient.user?.email})`);
      });
    }

    // 3. Test Device without replacementInfo JSON
    console.log('\n3️⃣ اختبار الأجهزة...');
    
    // Create test devices
    const device1 = await prisma.device.create({
      data: {
        id: 'TEST001',
        model: 'iPhone 15',
        status: 'available',
        storage: '128GB',
        price: 3500,
        condition: 'new'
      }
    });

    const device2 = await prisma.device.create({
      data: {
        id: 'TEST002',
        model: 'iPhone 15',
        status: 'available',
        storage: '128GB',
        price: 3500,
        condition: 'new'
      }
    });

    // Create replacement relation instead of JSON
    const replacement = await prisma.deviceReplacement.create({
      data: {
        originalDeviceId: device1.id,
        replacementDeviceId: device2.id,
        reason: 'اختبار النظام الجديد',
        notes: 'استبدال تجريبي'
      }
    });

    // Fetch device with replacements
    const deviceWithReplacements = await prisma.device.findUnique({
      where: { id: device1.id },
      include: {
        originalReplacements: {
          include: {
            replacementDevice: true
          }
        }
      }
    });

    if (deviceWithReplacements) {
      console.log(`   ✅ الجهاز: ${deviceWithReplacements.model} (${deviceWithReplacements.id})`);
      console.log(`   ✅ الاستبدالات: ${deviceWithReplacements.originalReplacements.length}`);
      deviceWithReplacements.originalReplacements.forEach(rep => {
        console.log(`      - استبدل بـ: ${rep.replacementDevice.id} (${rep.reason})`);
      });
    }

    // 4. Test System Settings with JSON (acceptable for complex settings)
    console.log('\n4️⃣ اختبار إعدادات النظام...');
    const systemSettings = await prisma.systemSetting.findFirst();
    
    if (systemSettings && systemSettings.reportSettings) {
      console.log(`   ✅ إعدادات التقارير موجودة (JSON مقبول للإعدادات المعقدة)`);
      console.log(`   ✅ الشركة: ${systemSettings.companyNameAr}`);
    }

    // 5. Clean up test data
    console.log('\n5️⃣ تنظيف البيانات التجريبية...');
    await prisma.deviceReplacement.delete({ where: { id: replacement.id } });
    await prisma.device.delete({ where: { id: device1.id } });
    await prisma.device.delete({ where: { id: device2.id } });
    await prisma.messageRecipient.deleteMany({ where: { messageId: testMessage.id } });
    await prisma.internalMessage.delete({ where: { id: testMessage.id } });
    console.log('   ✅ تم تنظيف البيانات التجريبية');

    console.log('\n🎉 تم اختبار جميع الإصلاحات بنجاح!');
    console.log('\n📊 ملخص الاختبارات:');
    console.log('✅ صلاحيات المستخدمين - تم التحويل من JSON إلى علاقات');
    console.log('✅ الرسائل الداخلية - تم التحويل من recipientIds JSON إلى MessageRecipient');
    console.log('✅ استبدال الأجهزة - تم التحويل من replacementInfo JSON إلى DeviceReplacement');
    console.log('✅ إعدادات النظام - الاحتفاظ بـ JSON للإعدادات المعقدة (مقبول)');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run tests
if (require.main === module) {
  testJsonFixes()
    .then(() => {
      console.log('\n✅ تم الانتهاء من الاختبارات بنجاح');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في الاختبارات:', error);
      process.exit(1);
    });
}

module.exports = { testJsonFixes };
