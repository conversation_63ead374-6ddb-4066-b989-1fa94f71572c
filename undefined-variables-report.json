{"timestamp": "2025-08-05T01:00:27.098Z", "totalIssues": 75, "issues": [{"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 39, "variable": "month", "content": "{ month: 'يناير', sales: 186 },"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 40, "variable": "month", "content": "{ month: 'فبراير', sales: 305 },"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 41, "variable": "month", "content": "{ month: 'مارس', sales: 237 },"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 42, "variable": "month", "content": "{ month: 'أبريل', sales: 273 },"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 43, "variable": "month", "content": "{ month: 'مايو', sales: 209 },"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 44, "variable": "month", "content": "{ month: 'يونيو', sales: 214 },"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\dashboard\\page.tsx", "line": 184, "variable": "month", "content": "dataKey=\"month\""}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 412, "variable": "day", "content": "case 'day':"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 424, "variable": "month", "content": "case 'month':"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 436, "variable": "year", "content": "case 'year':"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1173, "variable": "day", "content": "<SelectItem value=\"day\">اليوم</SelectItem>"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1176, "variable": "month", "content": "<SelectItem value=\"month\">آخر شهر</SelectItem>"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1179, "variable": "year", "content": "<SelectItem value=\"year\">آخر سنة</SelectItem>"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1256, "variable": "day", "content": "{dateRange === 'day' ? 'اليوم' :"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\inventory\\page.tsx", "line": 1258, "variable": "month", "content": "dateRange === 'month' ? 'شهر' :"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\reports\\client-reports\\page.tsx", "line": 29, "variable": "day", "content": "import { DateRange } from 'react-day-picker';"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\reports\\supplier-reports\\page.tsx", "line": 59, "variable": "day", "content": "import { DateRange } from 'react-day-picker';"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\returns\\page.tsx", "line": 681, "variable": "day", "content": "const today = new Date();"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\returns\\page.tsx", "line": 682, "variable": "day", "content": "if (isAfter(expiryDate, today)) {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\(main)\\track\\DeviceHistoryTimeline.tsx", "line": 368, "variable": "day", "content": "{event.details.daysBetweenSaleAndReturn && <div><span className=\"font-medium text-gray-700\">أيام منذ البيع:</span> <span className=\"text-gray-600\">{event.details.daysBetweenSaleAndReturn}</span></div>}"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\api\\archive\\route.ts", "line": 17, "variable": "day", "content": "const { daysOld = 90 } = await request.json();"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\app\\api\\archive\\route.ts", "line": 20, "variable": "day", "content": "cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\evaluation-cleanup.tsx", "line": 184, "variable": "dateString", "content": "return new Date(dateString);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\notifications\\NotificationSystem.tsx", "line": 151, "variable": "day", "content": "if (days > 0) return `منذ ${days} يوم`;"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 5, "variable": "day", "content": "import { DayPicker } from 'react-day-picker';"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 23, "variable": "month", "content": "months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 24, "variable": "month", "content": "month: 'space-y-4',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 39, "variable": "day", "content": "cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 40, "variable": "day", "content": "day: cn("}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 44, "variable": "day", "content": "day_range_end: 'day-range-end',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 45, "variable": "day", "content": "day_selected:"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 47, "variable": "day", "content": "day_today: 'bg-accent text-accent-foreground',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 48, "variable": "day", "content": "day_outside:"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 49, "variable": "day", "content": "'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 50, "variable": "day", "content": "day_disabled: 'text-muted-foreground opacity-50',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 51, "variable": "day", "content": "day_range_middle:"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\components\\ui\\calendar.tsx", "line": 53, "variable": "day", "content": "day_hidden: 'invisible',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\date-format-test.ts", "line": 14, "variable": "day", "content": "is<PERSON><PERSON><PERSON>,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\date-format-test.ts", "line": 22, "variable": "day", "content": "const today = new Date();"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\date-format-test.ts", "line": 63, "variable": "day", "content": "console.log('\\n7. isToday() - فحص التاريخ:');"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\date-format-test.ts", "line": 64, "variable": "day", "content": "console.log(`   • اليوم: ${isToday(today)}`);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\date-format-test.ts", "line": 65, "variable": "day", "content": "console.log(`   • تاريخ آخر: ${isToday(testDate)}`);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 12, "variable": "dateTimeString", "content": "'dateTimeString',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 13, "variable": "dateString", "content": "'dateString',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 14, "variable": "year", "content": "'year',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 15, "variable": "month", "content": "'month',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\find-undefined-variables.js", "line": 16, "variable": "day", "content": "'day'"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-date-formatting-issues.js", "line": 127, "variable": "year", "content": "search: /return `\\$\\{year\\}-\\$\\{month\\}-\\$\\{day\\}`;/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-date-formatting-issues.js", "line": 127, "variable": "month", "content": "search: /return `\\$\\{year\\}-\\$\\{month\\}-\\$\\{day\\}`;/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-date-formatting-issues.js", "line": 127, "variable": "day", "content": "search: /return `\\$\\{year\\}-\\$\\{month\\}-\\$\\{day\\}`;/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-main-pages.js", "line": 74, "variable": "dateTimeString", "content": "search: /const formatDateTime = \\(dateTimeString: string\\): string => \\{[\\s\\S]*?\\};/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-medium-priority-issues.js", "line": 134, "variable": "dateString", "content": "search: /\\(dateString: string\\)/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-medium-priority-issues.js", "line": 139, "variable": "dateTimeString", "content": "search: /\\(dateTimeString: string\\)/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-remaining-api-files-complete.js", "line": 119, "variable": "day", "content": "search: /cutoffDate\\.setDate\\(cutoffDate\\.getDate\\(\\) - daysOld\\);/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-remaining-api-files-complete.js", "line": 120, "variable": "day", "content": "replace: 'cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);',"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-remaining-components.js", "line": 156, "variable": "dateTimeString", "content": "search: /const formatDateTime = \\(dateTimeString: string\\): string/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 18, "variable": "dateString", "content": "const convertToISO = (dateString, fieldName) => {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 19, "variable": "dateString", "content": "if (!dateString) return null;"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 21, "variable": "dateString", "content": "if (dateString.match(/^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$/)) {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 22, "variable": "dateString", "content": "const isoDate = new Date(dateString.replace(' ', 'T') + '.000Z');"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-return-date-format.js", "line": 24, "variable": "dateString", "content": "console.log(`   - ${fieldName} للمرتجع ${ret.roNumber}: سيتم تحويل \"${dateString}\" إلى ${isoDate.toISOString()}`);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-string-date-types.js", "line": 132, "variable": "dateString", "content": "search: /const formatUploadDate = \\(dateString: Date \\| string\\): string/g,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-tracking-components.js", "line": 89, "variable": "day", "content": "function getDateDaysAgo(days: number): Date {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\fix-tracking-components.js", "line": 90, "variable": "day", "content": "return new Date(Date.now() - days * 24 * 60 * 60 * 1000);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\hooks\\useComprehensiveDeviceHistory.ts", "line": 497, "variable": "day", "content": "description: `تم إرجاع الجهاز من العميل \"${returnOrder.clientName}\" في أمر المرتجع ${returnOrder.roNumber || returnOrder.returnNumber}. السبب: ${returnedItem.returnReason || 'غير محدد'}${daysBetween ? ` بعد ${daysBetween} يوم من البيع` : ''}${returnedItem.replacementDeviceId ? ` مع تقديم جهاز بديل` : ''}`,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\hooks\\useComprehensiveDeviceHistory.ts", "line": 511, "variable": "day", "content": "daysBetweenSaleAndReturn: daysBetween,"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\date-utils.ts", "line": 49, "variable": "day", "content": "import { format, formatDistanceToNow, isToday as dateFnsIsToday, parseISO } from 'date-fns';"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\date-utils.ts", "line": 277, "variable": "day", "content": "export function isToday(date: Date | null | undefined): boolean {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\date-utils.ts", "line": 282, "variable": "day", "content": "return dateFnsIsToday(parsedDate);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\device-tracking-utils.ts", "line": 514, "variable": "day", "content": "function getDateDaysAgo(days: number): Date {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\device-tracking-utils.ts", "line": 515, "variable": "day", "content": "return new Date(Date.now() - days * 24 * 60 * 60 * 1000);"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\network.ts", "line": 109, "variable": "day", "content": "if (days > 0) {"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\network.ts", "line": 110, "variable": "day", "content": "return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\lib\\notification-service.ts", "line": 138, "variable": "day", "content": "return `${days} يوم`;"}, {"file": "C:\\Users\\<USER>\\Downloads\\111\\102\\105\\test-supply-style.js", "line": 73, "variable": "month", "content": "warrantyPeriod: '3months',"}]}