import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings } from '@/lib/types';
import { getPdfHeaderFooter, createPdfWithSettings, savePdf } from './pdf-utils';
import { createArabicPDFWithCanvas } from './export-utils/canvas-pdf-enhanced';
import { exportDeviceTrackingReportHTML } from './export-utils/enhanced-html-export';
import { formatDate, formatDateTime } from '@/lib/date-utils';

export interface DeviceTrackingData {
  model: string;
  id: string;
  status: string;
  lastSale?: {
    clientName: string;
    soNumber: string;
    opNumber: string;
    date: Date;
  };
  warrantyInfo?: {
    status: string;
    expiryDate: Date;
    remaining: string;
  };
  originalItemInfo?: {
    model: string;
    [key: string]: any;
  };
}

export interface TimelineEvent {
  id: string;
  type: string;
  title: string;
  description: string;
  date: Date;
  user?: string;
  details?: any;
  status?: string;
}

export interface PrintOptions {
  language?: 'ar' | 'en' | 'both';
  isCustomerView?: boolean;
  action?: 'print' | 'download';
  filename?: string;
  useCanvasMethod?: boolean; // خيار جديد لاختيار طريقة التصدير
}

/**
 * طباعة تقرير تتبع الجهاز المحدث مع خيارات متعددة
 */
export async function printDeviceTrackingReport(
  deviceData: DeviceTrackingData,
  timelineEvents: TimelineEvent[],
  options: PrintOptions = {}
) {
  const {
    language = 'both',
    isCustomerView = false,
    action = 'print',
    filename,
    useCanvasMethod = false // افتراضياً نستخدم HTML المحسن
  } = options;

  try {
    const defaultFilename = `${isCustomerView ? 'customer_' : ''}device_report_${deviceData.id}`;

    if (useCanvasMethod) {
      // استخدام Canvas (الطريقة القديمة)
      await createArabicPDFWithCanvas(
        deviceData,
        timelineEvents,
        filename || defaultFilename,
        isCustomerView,
        action,
        language
      );
    } else {
      // استخدام HTML المحسن (الطريقة الجديدة المفضلة)
      await exportDeviceTrackingReportHTML(
        deviceData,
        timelineEvents,
        {
          fileName: filename || defaultFilename,
          isCustomerView,
          action,
          language
        }
      );
    }

  } catch (error) {
    console.error('Error generating device tracking report:', error);
    
    // العودة للطريقة القديمة في حالة فشل الطريقة الجديدة
    if (!useCanvasMethod) {
      console.log('Falling back to Canvas method...');
      try {
        const fallbackFilename = filename || `${isCustomerView ? 'customer_' : ''}device_report_${deviceData.id}`;
        await createArabicPDFWithCanvas(
          deviceData,
          timelineEvents,
          fallbackFilename,
          isCustomerView,
          action,
          language
        );
      } catch (fallbackError) {
        console.error('Canvas fallback also failed:', fallbackError);
        // العودة للطريقة الاحتياطية الأخيرة
        await printDeviceTrackingReportFallback(deviceData, timelineEvents, options);
      }
    } else {
      // العودة للطريقة الاحتياطية
      await printDeviceTrackingReportFallback(deviceData, timelineEvents, options);
    }
  }
}/**
 * طريقة احتياطية لطباعة التقرير (الطريقة القديمة)
 */
async function printDeviceTrackingReportFallback(
  deviceData: DeviceTrackingData,
  timelineEvents: TimelineEvent[],
  options: PrintOptions = {}
) {
  const {
    language = 'both',
    isCustomerView = false,
    action = 'print',
    filename
  } = options;

  // جلب الإعدادات
  const response = await fetch('/api/settings');
  if (!response.ok) {
    throw new Error('فشل في جلب إعدادات النظام');
  }
  const settings: SystemSettings = await response.json();

  // تحديد عنوان التقرير
  const title = isCustomerView
    ? (language === 'en' ? 'Device Tracking Report (Customer Copy)' : 'تقرير تتبع الجهاز (نسخة العميل)')
    : (language === 'en' ? 'Device History Log' : 'سجل تاريخ الجهاز');

  const subtitle = `${deviceData.model} - ${deviceData.id}`;

  // إنشاء PDF
  const { doc, addHeader, addFooter } = createPdfWithSettings(
    settings,
    title,
    subtitle,
    { language, showLogo: true, showFooter: true, showTimestamp: true }
  );

  let yPosition = addHeader();

  // معلومات الجهاز الأساسية
  yPosition = addDeviceBasicInfo(doc, deviceData, yPosition, language);

  // معلومات البيع (للعملاء فقط)
  if (isCustomerView && deviceData.lastSale) {
    yPosition = addSaleInfo(doc, deviceData.lastSale, yPosition, language);
  }

  // معلومات الضمان (للعملاء فقط)
  if (isCustomerView && deviceData.warrantyInfo) {
    yPosition = addWarrantyInfo(doc, deviceData.warrantyInfo, yPosition, language);
  }

  // سجل الأحداث
  yPosition = addTimelineEvents(doc, timelineEvents, yPosition, language, isCustomerView);

  // إضافة التذييل
  addFooter();

  // حفظ أو طباعة
  const defaultFilename = `${isCustomerView ? 'customer_' : ''}device_report_${deviceData.id}.pdf`;
  savePdf(doc, filename || defaultFilename, action === 'download' ? 'save' : action);
}

/**
 * إضافة معلومات الجهاز الأساسية
 */
function addDeviceBasicInfo(
  doc: jsPDF,
  deviceData: DeviceTrackingData,
  startY: number,
  language: string
): number {
  let yPosition = startY + 10;

  // عنوان القسم
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  const sectionTitle = language === 'en' ? 'Device Information' : 'معلومات الجهاز';
  doc.text(sectionTitle, 190, yPosition, { align: 'right' });
  yPosition += 10;

  // خط فاصل
  doc.setLineWidth(0.3);
  doc.line(15, yPosition, 195, yPosition);
  yPosition += 8;

  // البيانات
  const deviceInfo = [
    [
      language === 'en' ? 'Model' : 'الموديل',
      deviceData.model
    ],
    [
      language === 'en' ? 'Serial Number' : 'الرقم التسلسلي',
      deviceData.id
    ],
    [
      language === 'en' ? 'Current Status' : 'الحالة الحالية',
      deviceData.status
    ],
    [
      language === 'en' ? 'Report Date' : 'تاريخ التقرير',
      formatDate(new Date(), { arabic: true })
    ]
  ];

  autoTable(doc, {
    startY: yPosition,
    body: deviceInfo,
    styles: {
      font: 'helvetica',
      fontSize: 10,
      halign: 'right',
      cellPadding: 3
    },
    columnStyles: {
      0: { fontStyle: 'bold', fillColor: [240, 240, 240] },
      1: { fillColor: [255, 255, 255] }
    },
    theme: 'grid'
  });

  return (doc as any).lastAutoTable.finalY + 15;
}

/**
 * إضافة معلومات البيع
 */
function addSaleInfo(
  doc: jsPDF,
  saleData: any,
  startY: number,
  language: string
): number {
  let yPosition = startY;

  // عنوان القسم
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  const sectionTitle = language === 'en' ? 'Sale Information' : 'معلومات البيع';
  doc.text(sectionTitle, 190, yPosition, { align: 'right' });
  yPosition += 10;

  // خط فاصل
  doc.setLineWidth(0.3);
  doc.line(15, yPosition, 195, yPosition);
  yPosition += 8;

  // البيانات
  const saleInfo = [
    [
      language === 'en' ? 'Customer Name' : 'اسم العميل',
      saleData.clientName
    ],
    [
      language === 'en' ? 'Sale Order' : 'رقم أمر البيع',
      saleData.soNumber
    ],
    [
      language === 'en' ? 'Operation Number' : 'رقم العملية',
      saleData.opNumber
    ],
    [
      language === 'en' ? 'Sale Date' : 'تاريخ البيع',
      formatDate(saleData.date, { arabic: true })
    ]
  ];

  autoTable(doc, {
    startY: yPosition,
    body: saleInfo,
    styles: {
      font: 'helvetica',
      fontSize: 10,
      halign: 'right',
      cellPadding: 3
    },
    columnStyles: {
      0: { fontStyle: 'bold', fillColor: [240, 248, 255] },
      1: { fillColor: [255, 255, 255] }
    },
    theme: 'grid'
  });

  return (doc as any).lastAutoTable.finalY + 15;
}

/**
 * إضافة معلومات الضمان
 */
function addWarrantyInfo(
  doc: jsPDF,
  warrantyData: any,
  startY: number,
  language: string
): number {
  let yPosition = startY;

  // عنوان القسم
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  const sectionTitle = language === 'en' ? 'Warranty Information' : 'معلومات الضمان';
  doc.text(sectionTitle, 190, yPosition, { align: 'right' });
  yPosition += 10;

  // خط فاصل
  doc.setLineWidth(0.3);
  doc.line(15, yPosition, 195, yPosition);
  yPosition += 8;

  // البيانات
  const warrantyInfo = [
    [
      language === 'en' ? 'Warranty Status' : 'حالة الضمان',
      warrantyData.status
    ],
    [
      language === 'en' ? 'Expiry Date' : 'تاريخ الانتهاء',
      warrantyData.expiryDate
    ],
    [
      language === 'en' ? 'Remaining' : 'المتبقي',
      warrantyData.remaining
    ]
  ];

  autoTable(doc, {
    startY: yPosition,
    body: warrantyInfo,
    styles: {
      font: 'helvetica',
      fontSize: 10,
      halign: 'right',
      cellPadding: 3
    },
    columnStyles: {
      0: { fontStyle: 'bold', fillColor: [240, 255, 240] },
      1: { fillColor: [255, 255, 255] }
    },
    theme: 'grid'
  });

  return (doc as any).lastAutoTable.finalY + 15;
}

/**
 * إضافة سجل الأحداث
 */
function addTimelineEvents(
  doc: jsPDF,
  events: TimelineEvent[],
  startY: number,
  language: string,
  isCustomerView: boolean
): number {
  let yPosition = startY;

  // عنوان القسم
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  const sectionTitle = isCustomerView 
    ? (language === 'en' ? 'Service History' : 'سجل الخدمات')
    : (language === 'en' ? 'Complete History' : 'السجل الكامل');
  doc.text(sectionTitle, 190, yPosition, { align: 'right' });
  yPosition += 10;

  // خط فاصل
  doc.setLineWidth(0.3);
  doc.line(15, yPosition, 195, yPosition);
  yPosition += 8;

  if (events.length === 0) {
    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    const noEventsText = language === 'en' ? 'No events recorded' : 'لا توجد أحداث مسجلة';
    doc.text(noEventsText, 105, yPosition + 10, { align: 'center' });
    return yPosition + 25;
  }

  // تحضير بيانات الجدول
  const headers = [
    language === 'en' ? 'Date' : 'التاريخ',
    language === 'en' ? 'Type' : 'النوع',
    language === 'en' ? 'Title' : 'العنوان',
    language === 'en' ? 'Description' : 'الوصف',
    language === 'en' ? 'User' : 'المستخدم'
  ];

  const tableData = events.map(event => [
    formatDate(event.date, { arabic: true }),
    event.type,
    event.title,
    event.description,
    event.user || '-'
  ]);

  autoTable(doc, {
    startY: yPosition,
    head: [headers],
    body: tableData,
    styles: {
      font: 'helvetica',
      fontSize: 9,
      halign: 'right',
      cellPadding: 2
    },
    headStyles: {
      halign: 'center',
      fillColor: [44, 51, 51],
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    columnStyles: {
      0: { cellWidth: 25 },
      1: { cellWidth: 25 },
      2: { cellWidth: 40 },
      3: { cellWidth: 70 },
      4: { cellWidth: 30 }
    },
    theme: 'grid',
    alternateRowStyles: {
      fillColor: [248, 249, 250]
    }
  });

  return (doc as any).lastAutoTable.finalY + 10;
}

/**
 * طباعة عنصر HTML محدث
 */
export function printElementWithSettings(
  elementId: string,
  title: string,
  options: PrintOptions = {}
) {
  const element = document.getElementById(elementId);
  if (!element) {
    console.error(`Element with ID '${elementId}' not found`);
    return;
  }

  const printWindow = window.open('', '_blank', 'width=800,height=600');
  if (!printWindow) {
    console.error('Failed to open print window');
    return;
  }

  // إنشاء محتوى HTML للطباعة
  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
      <link rel="stylesheet" href="/components/DocumentHeader.css">
      <link rel="stylesheet" href="/components/DeviceTrackingReport.css">
      <style>
        body {
          font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background: white;
          color: #333;
        }
        @media print {
          body { margin: 0; padding: 15mm; }
          .no-print { display: none !important; }
        }
      </style>
    </head>
    <body>
      ${element.outerHTML}
      <script>
        window.onload = function() {
          window.print();
          window.onafterprint = function() {
            window.close();
          };
        };
      </script>
    </body>
    </html>
  `;

  printWindow.document.write(htmlContent);
  printWindow.document.close();
}

// دوال مساعدة للتتبع
function formatNumber(value: number): string {
  return new Intl.NumberFormat('ar-EG').format(value);
}

function getDateDaysAgo(days: number): Date {
  return new Date(Date.now() - days * 24 * 60 * 60 * 1000);
}

function formatTrackingDate(date: Date): string {
  return formatDate(date, { arabic: true, format: 'full' });
}
