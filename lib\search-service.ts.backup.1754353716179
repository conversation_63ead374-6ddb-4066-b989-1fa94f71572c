// تم نقل استيراد prisma إلى API routes فقط

export interface SearchFilters {
  status?: string[];
  priority?: string[];
  requestType?: string[];
  employeeName?: string;
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
  hasAttachments?: boolean;
  isArchived?: boolean;
}

export interface SearchResult {
  id: number;
  requestNumber: string;
  employeeName: string;
  requestType: string;
  priority: string;
  status: string;
  requestDate: string;
  notes: string;
  adminNotes?: string;
  attachments?: any[];
  tags?: string[];
  relevanceScore?: number;
}

export class SearchService {
  // دوال مساعدة فقط - جميع عمليات قاعدة البيانات في API routes

}
}
