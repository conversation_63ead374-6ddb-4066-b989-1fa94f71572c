"use client";

import { useState, useEffect } from "react";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface DeviceMemo {
  id: string;
  name: string;
  maintenanceStartDate: Date; // ISO string
}

interface MaintenanceMemoProps {
  devices: DeviceMemo[];
}

export default function MaintenanceMemo({ devices }: MaintenanceMemoProps) {
  const LONG_MAINTENANCE_DAYS = 7;
  const [oldDevices, setOldDevices] = useState<DeviceMemo[]>([]);
  const [dailyMemo, setDailyMemo] = useState('');
  const [achievements, setAchievements] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    const now = new Date();
    const longDevices = devices.filter(device => {
      const start = new Date(device.maintenanceStartDate);
      const diffDays = (now.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
      return diffDays >= LONG_MAINTENANCE_DAYS;
    });
    setOldDevices(longDevices);
  }, [devices]);

  const handleSaveMemo = () => {
    if (!dailyMemo.trim()) return;
    setAchievements(prev => [...prev, `${format(new Date(), 'yyyy-MM-dd')} - ${dailyMemo.trim()}`]);
    toast({ title: 'تم حفظ المذكرة', description: 'تم تسجيل إنجازات اليوم' });
    setDailyMemo('');
  };

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm space-y-4">
      <h2 className="text-lg font-bold">مذكرات وإنجازات الصيانة</h2>
      {oldDevices.length > 0 && (
        <div className="p-2 bg-red-50 border border-red-200 rounded">
          <p className="text-sm font-semibold text-red-600">
            تنبيه: {oldDevices.length} جهاز/أجهزة موجودة بأمد طويل للصيانة.
          </p>
        </div>
      )}
      <div className="flex items-center gap-2">
        <Input
          placeholder="أضف ملاحظة أو سجل إنجاز اليوم..."
          value={dailyMemo}
          onChange={e => setDailyMemo(e.target.value)}
        />
        <Button onClick={handleSaveMemo}>حفظ</Button>
      </div>
      {achievements.length > 0 && (
        <div className="space-y-2">
          <h3 className="font-semibold">إنجازات اليوم</h3>
          <ul className="list-disc list-inside text-sm">
            {achievements.map((note, idx) => (
              <li key={idx}>{note}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
