/**
 * أداة شاملة لحل مشاكل المصادقة 401 - Complete 401 Auth Fix Tool
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function completeAuthFix() {
  console.log('🔧 أداة شاملة لحل مشاكل المصادقة 401...\n');
  
  try {
    // 1. التأكد من وجود المستخدم admin
    console.log('1️⃣ التأكد من المستخدم admin...');
    
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { 
        role: 'admin', 
        username: 'admin',
        status: 'Active'
      },
      create: {
        username: 'admin',
        email: '<EMAIL>', 
        name: 'System Administrator',
        role: 'admin',
        status: 'Active'
      }
    });
    
    console.log(`   ✅ المستخدم admin: ID=${adminUser.id}, Role=${adminUser.role}`);
    
    // 2. اختبار جميع APIs المتأثرة
    console.log('\n2️⃣ اختبار جميع APIs المتأثرة...');
    
    const correctToken = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    const problematicAPIs = [
      '/api/suppliers?view=simple',
      '/api/device-models?view=simple',
      '/api/settings?view=simple',
      '/api/employee-requests?view=simple',
      // APIs إضافية قد تحتاج فحص
      '/api/devices?view=simple',
      '/api/warehouses?view=simple',
      '/api/sales?view=simple',
      '/api/returns?view=simple'
    ];
    
    const testResults = [];
    
    for (const endpoint of problematicAPIs) {
      console.log(`\n   🔍 ${endpoint}`);
      
      try {
        const response = await fetch(`http://localhost:9005${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${correctToken}`
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          const count = Array.isArray(data) ? data.length : 'object';
          console.log(`      ✅ نجح - البيانات: ${count}`);
          testResults.push({ endpoint, status: 'نجح', count });
        } else {
          const errorText = await response.text();
          console.log(`      ❌ فشل - Status: ${response.status}`);
          console.log(`      الخطأ: ${errorText.substring(0, 100)}...`);
          testResults.push({ endpoint, status: 'فشل', error: response.status });
        }
      } catch (error) {
        console.log(`      ❌ خطأ شبكة: ${error.message}`);
        testResults.push({ endpoint, status: 'خطأ شبكة', error: error.message });
      }
    }
    
    // 3. تلخيص النتائج
    console.log('\n3️⃣ ملخص نتائج الاختبار...');
    
    const successful = testResults.filter(r => r.status === 'نجح');
    const failed = testResults.filter(r => r.status !== 'نجح');
    
    console.log(`   ✅ APIs ناجحة: ${successful.length}/${testResults.length}`);
    console.log(`   ❌ APIs فاشلة: ${failed.length}/${testResults.length}`);
    
    if (failed.length > 0) {
      console.log('\n   🔍 APIs الفاشلة:');
      failed.forEach(api => {
        console.log(`   - ${api.endpoint}: ${api.status} (${api.error})`);
      });
    }
    
    // 4. فحص حالة قاعدة البيانات
    console.log('\n4️⃣ فحص حالة قاعدة البيانات...');
    
    const dbTables = [
      { name: 'User', model: prisma.user },
      { name: 'Device', model: prisma.device },
      { name: 'Supplier', model: prisma.supplier },
      { name: 'DeviceModel', model: prisma.deviceModel },
      { name: 'SystemSetting', model: prisma.systemSetting },
      { name: 'EmployeeRequest', model: prisma.employeeRequest },
      { name: 'Warehouse', model: prisma.warehouse },
      { name: 'Sale', model: prisma.sale },
      { name: 'Return', model: prisma.return }
    ];
    
    for (const table of dbTables) {
      try {
        const count = await table.model.count();
        console.log(`   ${table.name}: ${count} سجل`);
      } catch (error) {
        console.log(`   ${table.name}: ❌ خطأ - ${error.message.substring(0, 50)}...`);
      }
    }
    
    // 5. فحص إعدادات المصادقة
    console.log('\n5️⃣ فحص إعدادات المصادقة...');
    
    // فحص التوكن
    const token = 'dXNlcjphZG1pbjphZG1pbg==';
    const decoded = Buffer.from(token, 'base64').toString();
    console.log(`   🔑 التوكن المستخدم: ${token}`);
    console.log(`   🔓 التوكن المفكك: ${decoded}`);
    
    // فحص المستخدمين المتاحين
    const users = await prisma.user.findMany({
      select: { id: true, username: true, email: true, role: true, status: true }
    });
    
    console.log('\n   👥 المستخدمين المتاحين:');
    users.forEach(user => {
      console.log(`   - ${user.username} (${user.email}) - Role: ${user.role}, Status: ${user.status}`);
    });
    
    // 6. إنشاء تقرير شامل
    console.log('\n📊 التقرير الشامل:');
    console.log('================================');
    console.log(`✅ إجمالي APIs تم اختبارها: ${testResults.length}`);
    console.log(`✅ APIs تعمل بنجاح: ${successful.length}`);
    console.log(`❌ APIs تحتاج إصلاح: ${failed.length}`);
    console.log(`👤 عدد المستخدمين: ${users.length}`);
    console.log(`🔑 التوكن المستخدم: صحيح`);
    
    // 7. توصيات الإصلاح
    console.log('\n🔧 توصيات الإصلاح:');
    console.log('================================');
    
    if (failed.length === 0) {
      console.log('🎉 جميع APIs تعمل بشكل صحيح!');
      console.log('💡 إذا كنت لا تزال ترى أخطاء 401 في المتصفح، جرب:');
      console.log('   1. مسح cache المتصفح');
      console.log('   2. إعادة تشغيل خادم Next.js');
      console.log('   3. فحص Developer Tools في المتصفح');
    } else {
      console.log('🔄 يُنصح بالخطوات التالية:');
      console.log('   1. إعادة تشغيل خادم Next.js');
      console.log('   2. فحص ملفات API routes المتأثرة');
      console.log('   3. التأكد من ملف lib/api-client.ts');
      console.log('   4. مراجعة ملف lib/auth.ts');
    }
    
    console.log('\n🚀 أوامر مفيدة:');
    console.log('   - إعادة تشغيل الخادم: npm run dev');
    console.log('   - فحص logs: تحقق من terminal الخادم');
    console.log('   - فحص المتصفح: F12 -> Console -> Network');
    
  } catch (error) {
    console.error('❌ خطأ عام في أداة الإصلاح الشاملة:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل أداة الإصلاح الشاملة
completeAuthFix();
