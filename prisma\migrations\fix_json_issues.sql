-- Migration to fix JSON issues and improve database structure
-- Date: 2025-08-04
-- Description: Remove JSON fields and replace with proper relational models

-- 1. Create new tables for attachments and tags

-- Request Tags table
CREATE TABLE IF NOT EXISTS "request_tags" (
    "id" SERIAL NOT NULL,
    "requestId" INTEGER NOT NULL,
    "tagName" TEXT NOT NULL,
    "tagValue" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "request_tags_pkey" PRIMARY KEY ("id")
);

-- Comment Attachments table
CREATE TABLE IF NOT EXISTS "comment_attachments" (
    "id" SERIAL NOT NULL,
    "commentId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "comment_attachments_pkey" PRIMARY KEY ("id")
);

-- Sale Attachments table
CREATE TABLE IF NOT EXISTS "sale_attachments" (
    "id" SERIAL NOT NULL,
    "saleId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "sale_attachments_pkey" PRIMARY KEY ("id")
);

-- Return Attachments table
CREATE TABLE IF NOT EXISTS "return_attachments" (
    "id" SERIAL NOT NULL,
    "returnId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "return_attachments_pkey" PRIMARY KEY ("id")
);

-- Supply Order Draft Items table
CREATE TABLE IF NOT EXISTS "supply_order_draft_items" (
    "id" SERIAL NOT NULL,
    "draftId" INTEGER NOT NULL,
    "deviceId" TEXT NOT NULL,
    "model" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "supply_order_draft_items_pkey" PRIMARY KEY ("id")
);

-- Supply Order Draft Attachments table
CREATE TABLE IF NOT EXISTS "supply_order_draft_attachments" (
    "id" SERIAL NOT NULL,
    "draftId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "supply_order_draft_attachments_pkey" PRIMARY KEY ("id")
);

-- Warehouse Transfer Attachments table
CREATE TABLE IF NOT EXISTS "warehouse_transfer_attachments" (
    "id" SERIAL NOT NULL,
    "transferId" INTEGER NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "warehouse_transfer_attachments_pkey" PRIMARY KEY ("id")
);

-- 2. Add reportSettings to SystemSetting if not exists
ALTER TABLE "SystemSetting" ADD COLUMN IF NOT EXISTS "reportSettings" JSONB;

-- 3. Create unique constraints and indexes
CREATE UNIQUE INDEX IF NOT EXISTS "request_tags_requestId_tagName_key" ON "request_tags"("requestId", "tagName");

-- 4. Add foreign key constraints
ALTER TABLE "request_tags" ADD CONSTRAINT "request_tags_requestId_fkey" FOREIGN KEY ("requestId") REFERENCES "employee_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "comment_attachments" ADD CONSTRAINT "comment_attachments_commentId_fkey" FOREIGN KEY ("commentId") REFERENCES "request_comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "sale_attachments" ADD CONSTRAINT "sale_attachments_saleId_fkey" FOREIGN KEY ("saleId") REFERENCES "Sale"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "return_attachments" ADD CONSTRAINT "return_attachments_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "supply_order_draft_items" ADD CONSTRAINT "supply_order_draft_items_draftId_fkey" FOREIGN KEY ("draftId") REFERENCES "supply_order_drafts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "supply_order_draft_attachments" ADD CONSTRAINT "supply_order_draft_attachments_draftId_fkey" FOREIGN KEY ("draftId") REFERENCES "supply_order_drafts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "warehouse_transfer_attachments" ADD CONSTRAINT "warehouse_transfer_attachments_transferId_fkey" FOREIGN KEY ("transferId") REFERENCES "warehouse_transfers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- 5. Remove JSON columns after data migration (will be done in separate script)
-- Note: These will be removed after data migration is complete
-- ALTER TABLE "users" DROP COLUMN IF EXISTS "warehouseAccess";
-- ALTER TABLE "users" DROP COLUMN IF EXISTS "permissions";
-- ALTER TABLE "internal_messages" DROP COLUMN IF EXISTS "recipientIds";
-- ALTER TABLE "Device" DROP COLUMN IF EXISTS "replacementInfo";
-- ALTER TABLE "employee_requests" DROP COLUMN IF EXISTS "attachments";
-- ALTER TABLE "employee_requests" DROP COLUMN IF EXISTS "tags";
-- ALTER TABLE "request_comments" DROP COLUMN IF EXISTS "attachments";
-- ALTER TABLE "MaintenanceOrder" DROP COLUMN IF EXISTS "items";
-- ALTER TABLE "MaintenanceReceiptOrder" DROP COLUMN IF EXISTS "items";
-- ALTER TABLE "Sale" DROP COLUMN IF EXISTS "attachments";
-- ALTER TABLE "Return" DROP COLUMN IF EXISTS "attachments";
-- ALTER TABLE "supply_order_drafts" DROP COLUMN IF EXISTS "formState";
-- ALTER TABLE "supply_order_drafts" DROP COLUMN IF EXISTS "currentItems";
-- ALTER TABLE "supply_order_drafts" DROP COLUMN IF EXISTS "attachments";
-- ALTER TABLE "warehouse_transfers" DROP COLUMN IF EXISTS "attachments";
