import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// دالة لمعالجة التواريخ بأمان
function safeToISOString(dateValue: any): string | null {
  if (!dateValue) return null;
  try {
    const date = new Date(dateValue);
    if (isNaN(date.getTime())) {
      console.warn('Invalid date value:', dateValue);
      return null;
    }
    return date.toISOString();
  } catch (error) {
    console.warn('Error parsing date:', dateValue, error);
    return null;
  }
}

// دالة لتنظيف البيانات من null bytes والأحرف غير الصالحة
function sanitizeString(str: string | null | undefined): string | null {
  if (!str) return null;
  if (typeof str !== 'string') return null;
  
  // إزالة null bytes وأحرف التحكم الأخرى
  let cleaned = str
    .replace(/\x00/g, '') // إزالة null bytes
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // إزالة أحرف التحكم
    .trim();
    
  return cleaned.length > 0 ? cleaned : null;
}

// دالة لتنظيف بيانات المرتجع
function sanitizeReturnData(data: any) {
  const sanitized: any = {};
  
  // نسخ جميع الحقول مع التنظيف
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    if (view === 'simple') {
      // استرجاع مبسط بدون items باستخدام raw query لتجنب مشاكل DateTime
      const returns = await prisma.$queryRaw`
        SELECT id, "roNumber", "opReturnNumber", date, "saleId", "soNumber",
               "clientName", "warehouseName", notes, status, "processedBy", 
               "processedDate", "employeeName", "createdAt", attachments
        FROM "Return" 
        ORDER BY id DESC
      `;
      return NextResponse.json(returns);
    }

    // استرجاع كامل مع items
    // استرجاع كل المرتجعات من قاعدة البيانات باستخدام raw query لتجنب مشاكل DateTime
    const returns = await prisma.$queryRaw`
      SELECT r.*, 
             ARRAY_AGG(
               CASE WHEN ri.id IS NOT NULL THEN
                 JSON_BUILD_OBJECT(
                   'id', ri.id,
                   'deviceId', ri."deviceId",
                   'model', ri.model,
                   'returnReason', ri."returnReason",
                   'replacementDeviceId', ri."replacementDeviceId",
                   'isReplacement', ri."isReplacement",
                   'originalDeviceId', ri."originalDeviceId"
                 )
               END
             ) FILTER (WHERE ri.id IS NOT NULL) as items
      FROM "Return" r
      LEFT JOIN "return_items" ri ON r.id = ri."returnId"
      GROUP BY r.id
      ORDER BY r.id DESC
    `;

    // تحويل النتائج للصيغة الصحيحة مع معالجة التواريخ غير الصالحة
    const returnsWithParsedData = (returns as any[]).map((returnItem: any) => {
      let parsedAttachments = [];
      let parsedItems = [];

      // معالجة attachments
      if (returnItem.attachments) {
        if (typeof returnItem.attachments === 'string') {
          try {
            parsedAttachments = JSON.parse(returnItem.attachments);
          } catch (error) {
            console.warn('Failed to parse attachments:', error);
            parsedAttachments = [];
          }
        } else {
          parsedAttachments = returnItem.attachments;
        }
      }

      // معالجة items
      if (returnItem.items) {
        parsedItems = Array.isArray(returnItem.items) ? 
          returnItem.items.filter((item: any) => item !== null) : [];
      }

      return {
        ...returnItem,
        date: safeToISOString(returnItem.date) || new Date().toISOString(),
        processedDate: safeToISOString(returnItem.processedDate),
        attachments: parsedAttachments,
        items: parsedItems
      };
    });

    return NextResponse.json(returnsWithParsedData);
  } catch (error) {
    console.error('Failed to fetch returns:', error);
    return NextResponse.json(
      { error: 'Failed to fetch returns' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newReturn = await request.json();

    // Basic validation
    if (!newReturn.clientName || !newReturn.items || !Array.isArray(newReturn.items) || newReturn.items.length === 0) {
      return NextResponse.json(
        { error: 'Client name and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // استخدام رقم الأمر المُرسل من الواجهة الأمامية، أو إنشاء رقم جديد إذا لم يكن متوفراً
      let roNumber = newReturn.roNumber;
      if (!roNumber) {
        roNumber = await generateUniqueId(tx, 'return', 'RO-');
      } else {
        // التحقق من عدم وجود رقم مكرر
        const existingReturn = await tx.return.findUnique({
          where: { roNumber }
        });
        if (existingReturn) {
          roNumber = await generateUniqueId(tx, 'return', 'RO-');
        }
      }

      // إنشاء المرتجع في قاعدة البيانات مع تنظيف البيانات
      const sanitizedReturnData = sanitizeReturnData({
        roNumber,
        opReturnNumber: newReturn.opReturnNumber && newReturn.opReturnNumber.trim() !== '' ? newReturn.opReturnNumber : roNumber,
        date: new Date(newReturn.date),
        saleId: newReturn.saleId || null,
        soNumber: newReturn.soNumber || null,
        clientName: newReturn.clientName,
        warehouseName: newReturn.warehouseName || null,
        notes: newReturn.notes || '',
        status: newReturn.status || 'معلق',
        processedBy: newReturn.processedBy || null,
        processedDate: newReturn.processedDate ? new Date(newReturn.processedDate) : null,
        employeeName: newReturn.employeeName || authResult.user!.username,
        attachments: newReturn.attachments ? JSON.stringify(newReturn.attachments) : null
      });

      // إنشاء المرتجع باستخدام raw query لتجنب مشكلة null bytes
      const returnRecord = await tx.$queryRaw`
        INSERT INTO "Return" 
        ("roNumber", "opReturnNumber", "date", "saleId", "soNumber", "clientName", "warehouseName", "notes", "status", "processedBy", "processedDate", "employeeName", "attachments")
        VALUES 
        (${sanitizedReturnData.roNumber}, ${sanitizedReturnData.opReturnNumber}, ${sanitizedReturnData.date}, ${sanitizedReturnData.saleId}, ${sanitizedReturnData.soNumber}, ${sanitizedReturnData.clientName}, ${sanitizedReturnData.warehouseName}, ${sanitizedReturnData.notes}, ${sanitizedReturnData.status}, ${sanitizedReturnData.processedBy}, ${sanitizedReturnData.processedDate}, ${sanitizedReturnData.employeeName}, ${sanitizedReturnData.attachments})
        RETURNING *
      `.then(result => Array.isArray(result) ? result[0] : result);

      // Create return items مع تنظيف البيانات
      if (newReturn.items && Array.isArray(newReturn.items)) {
        for (const item of newReturn.items) {
          const sanitizedItemData = {
            returnId: returnRecord.id,
            deviceId: sanitizeString(item.deviceId) || '',
            model: sanitizeString(item.model) || '',
            returnReason: sanitizeString(item.returnReason) || '',
            replacementDeviceId: sanitizeString(item.replacementDeviceId) || null,
            isReplacement: item.isReplacement || false,
            originalDeviceId: sanitizeString(item.originalDeviceId) || null
          };

          // إنشاء عنصر المرتجع باستخدام raw query لتجنب مشكلة null bytes
          await tx.$executeRaw`
            INSERT INTO "return_items" 
            ("returnId", "deviceId", "model", "returnReason", "replacementDeviceId", "isReplacement", "originalDeviceId")
            VALUES 
            (${sanitizedItemData.returnId}, ${sanitizedItemData.deviceId}, ${sanitizedItemData.model}, ${sanitizedItemData.returnReason}, ${sanitizedItemData.replacementDeviceId}, ${sanitizedItemData.isReplacement}, ${sanitizedItemData.originalDeviceId})
          `;
        }
      }

      // تحديث حالة الأجهزة
      if (newReturn.items && Array.isArray(newReturn.items)) {
        for (const item of newReturn.items) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              // تحديد الحالة الجديدة حسب سبب الإرجاع
              let newStatus = 'متاح للبيع';
              if (item.returnReason === 'خلل مصنعي') {
                newStatus = 'بانتظار إرسال للصيانة';
              } else if (item.returnReason === 'سبب آخر') {
                newStatus = 'بانتظار إرسال للصيانة';
              }

              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: newStatus }
              });
            } else {
              console.warn(`Device ${item.deviceId} not found for return ${returnRecord.roNumber}`);
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: sanitizeString(`Created return: ${returnRecord.roNumber} for client ${returnRecord.clientName}`) || `Created return: ${returnRecord.roNumber}`
      });

      // Return return with items using raw SQL to avoid DateTime conversion issues
      const returnWithItems = await tx.$queryRaw`
        SELECT 
          r.id, r."roNumber", r."opReturnNumber", r.date, r."saleId", r."soNumber", 
          r."clientName", r."warehouseName", r.notes, r.status, r."processedBy", 
          r."processedDate", r."employeeName", r."createdAt", r.attachments,
          json_agg(
            json_build_object(
              'id', ri.id,
              'deviceId', ri."deviceId", 
              'model', ri.model,
              'returnReason', ri."returnReason",
              'replacementDeviceId', ri."replacementDeviceId",
              'isReplacement', ri."isReplacement",
              'originalDeviceId', ri."originalDeviceId"
            )
          ) FILTER (WHERE ri.id IS NOT NULL) as items
        FROM "Return" r
        LEFT JOIN "return_items" ri ON r.id = ri."returnId"
        WHERE r.id = ${returnRecord.id}
        GROUP BY r.id, r."roNumber", r."opReturnNumber", r.date, r."saleId", r."soNumber", 
                 r."clientName", r."warehouseName", r.notes, r.status, r."processedBy", 
                 r."processedDate", r."employeeName", r."createdAt", r.attachments
      `.then(result => {
        const data = Array.isArray(result) ? result[0] : result;
        return {
          ...data,
          items: data.items || []
        };
      });

      return returnWithItems;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create return:', error);
    return NextResponse.json({ error: 'Failed to create return' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedReturn = await request.json();

    if (!updatedReturn.id) {
      return NextResponse.json(
        { error: 'Return ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المرتجع باستخدام raw SQL
      const existingReturnQuery = await tx.$queryRaw<Array<{
        id: string;
        roNumber: string;
        opReturnNumber: string;
        date: string;
        saleId: number;
        soNumber: string;
        clientName: string;
        warehouseName: string;
        notes: string;
        status: string;
        processedBy: string;
        processedDate: string;
        employeeName: string;
        attachments: string;
      }>>`
        SELECT * FROM "Return" WHERE id = ${updatedReturn.id}
      `;

      if (!existingReturnQuery || existingReturnQuery.length === 0) {
        throw new Error('Return not found');
      }

      const existingReturn = existingReturnQuery[0];

      // الحصول على العناصر الموجودة
      const existingItems = await tx.$queryRaw<Array<{
        id: string;
        returnId: string;
        deviceId: string;
        model: string;
        returnReason: string;
        isReplacement: boolean;
      }>>`
        SELECT * FROM "return_items" WHERE "returnId" = ${updatedReturn.id}
      `;

      // تحديث المرتجع باستخدام raw SQL
      const updateDate = updatedReturn.date ? safeToISOString(updatedReturn.date) || existingReturn.date : existingReturn.date;
      const updateProcessedDate = updatedReturn.processedDate !== undefined ? 
        (updatedReturn.processedDate ? safeToISOString(updatedReturn.processedDate) : null) : 
        existingReturn.processedDate;

      await tx.$executeRaw`
        UPDATE "Return" 
        SET 
          "opReturnNumber" = ${updatedReturn.opReturnNumber !== undefined ? sanitizeString(updatedReturn.opReturnNumber) : existingReturn.opReturnNumber},
          "date" = ${updateDate}::timestamp,
          "saleId" = ${updatedReturn.saleId !== undefined ? updatedReturn.saleId : existingReturn.saleId},
          "soNumber" = ${updatedReturn.soNumber !== undefined ? sanitizeString(updatedReturn.soNumber) : existingReturn.soNumber},
          "clientName" = ${updatedReturn.clientName ? sanitizeString(updatedReturn.clientName) : existingReturn.clientName},
          "warehouseName" = ${updatedReturn.warehouseName !== undefined ? sanitizeString(updatedReturn.warehouseName) : existingReturn.warehouseName},
          "notes" = ${updatedReturn.notes !== undefined ? sanitizeString(updatedReturn.notes) : existingReturn.notes},
          "status" = ${updatedReturn.status ? sanitizeString(updatedReturn.status) : existingReturn.status},
          "processedBy" = ${updatedReturn.processedBy !== undefined ? sanitizeString(updatedReturn.processedBy) : existingReturn.processedBy},
          "processedDate" = ${updateProcessedDate ? updateProcessedDate + '::timestamp' : null},
          "employeeName" = ${updatedReturn.employeeName ? sanitizeString(updatedReturn.employeeName) : existingReturn.employeeName}
        WHERE id = ${updatedReturn.id}
      `;

      // Update return items if provided
      if (updatedReturn.items && Array.isArray(updatedReturn.items)) {
        // Delete existing items using raw SQL
        await tx.$executeRaw`
          DELETE FROM "return_items" WHERE "returnId" = ${updatedReturn.id}
        `;

        // Create new items using raw SQL
        for (const item of updatedReturn.items) {
          const sanitizedItemData = {
            returnId: updatedReturn.id,
            deviceId: sanitizeString(item.deviceId) || '',
            model: sanitizeString(item.model) || '',
            returnReason: sanitizeString(item.returnReason) || '',
            replacementDeviceId: sanitizeString(item.replacementDeviceId) || null,
            isReplacement: item.isReplacement || false,
            originalDeviceId: sanitizeString(item.originalDeviceId) || null
          };

          await tx.$executeRaw`
            INSERT INTO "return_items" 
            ("returnId", "deviceId", "model", "returnReason", "replacementDeviceId", "isReplacement", "originalDeviceId")
            VALUES 
            (${sanitizedItemData.returnId}, ${sanitizedItemData.deviceId}, ${sanitizedItemData.model}, ${sanitizedItemData.returnReason}, ${sanitizedItemData.replacementDeviceId}, ${sanitizedItemData.isReplacement}, ${sanitizedItemData.originalDeviceId})
          `;
        }
      }

      // الحصول على المرتجع المحدث باستخدام raw SQL  
      const updatedReturnResult = await tx.$queryRaw<Array<{
        id: string;
        roNumber: string;
        opReturnNumber: string;
        date: string;
        saleId: number;
        soNumber: string;
        clientName: string;
        warehouseName: string;
        notes: string;
        status: string;
        processedBy: string;
        processedDate: string;
        employeeName: string;
        attachments: string;
      }>>`
        SELECT * FROM "Return" WHERE id = ${updatedReturn.id}
      `;

      const updatedReturnRecord = updatedReturnResult[0];

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: sanitizeString(`Updated return: ${updatedReturnRecord.roNumber}`) || `Updated return: ${updatedReturnRecord.roNumber}`
      });

      // Get the updated return with items using raw SQL
      const updatedReturnWithItems = await tx.$queryRaw`
        SELECT 
          r.id, r."roNumber", r."opReturnNumber", r.date, r."saleId", r."soNumber", 
          r."clientName", r."warehouseName", r.notes, r.status, r."processedBy", 
          r."processedDate", r."employeeName", r."createdAt", r.attachments,
          json_agg(
            json_build_object(
              'id', ri.id,
              'deviceId', ri."deviceId", 
              'model', ri.model,
              'returnReason', ri."returnReason",
              'replacementDeviceId', ri."replacementDeviceId",
              'isReplacement', ri."isReplacement",
              'originalDeviceId', ri."originalDeviceId"
            )
          ) FILTER (WHERE ri.id IS NOT NULL) as items
        FROM "Return" r
        LEFT JOIN "return_items" ri ON r.id = ri."returnId"
        WHERE r.id = ${updatedReturn.id}
        GROUP BY r.id, r."roNumber", r."opReturnNumber", r.date, r."saleId", r."soNumber", 
                 r."clientName", r."warehouseName", r.notes, r.status, r."processedBy", 
                 r."processedDate", r."employeeName", r."createdAt", r.attachments
      `.then(result => {
        const data = Array.isArray(result) ? result[0] : result;
        return {
          ...data,
          items: data.items || []
        };
      });

      return updatedReturnWithItems;
    });

    // معالجة حقول JSON قبل الإرسال  
    const processedReturn = {
      ...result,
      attachments: result?.attachments ?
        (typeof result.attachments === 'string' ?
          JSON.parse(result.attachments) : result.attachments) : null,
    };

    return NextResponse.json(processedReturn);
  } catch (error) {
    console.error('Failed to update return:', error);

    if (error instanceof Error && error.message === 'Return not found') {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update return' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Return ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المرتجع باستخدام raw SQL
      const existingReturnQuery = await tx.$queryRaw<Array<{
        id: string;
        roNumber: string;
        opReturnNumber: string;
        date: string;
        saleId: number;
        soNumber: string;
        clientName: string;
        warehouseName: string;
        notes: string;
        status: string;
        processedBy: string;
        processedDate: string;
        employeeName: string;
        attachments: string;
      }>>`
        SELECT * FROM "Return" WHERE id = ${id}
      `;

      if (!existingReturnQuery || existingReturnQuery.length === 0) {
        throw new Error('Return not found');
      }

      const existingReturn = existingReturnQuery[0];

      // الحصول على العناصر المرتبطة
      const existingItems = await tx.$queryRaw<Array<{
        id: string;
        returnId: string;
        deviceId: string;
        model: string;
        returnReason: string;
        isReplacement: boolean;
      }>>`
        SELECT * FROM "return_items" WHERE "returnId" = ${id}
      `;

      // إرجاع حالة الأجهزة إلى "مباع" قبل الحذف
      if (Array.isArray(existingItems)) {
        for (const item of existingItems) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'مباع' }
              });
            }
          }
        }
      }

      // حذف عناصر المرتجع أولاً
      await tx.$executeRaw`
        DELETE FROM "return_items" WHERE "returnId" = ${id}
      `;

      // حذف المرتجع
      await tx.$executeRaw`
        DELETE FROM "Return" WHERE id = ${id}
      `;

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: sanitizeString(`Deleted return: ${existingReturn.roNumber}`) || `Deleted return: ${existingReturn.roNumber}`
      });

      return { message: 'Return deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete return:', error);

    if (error instanceof Error && error.message === 'Return not found') {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete return' }, { status: 500 });
  }
}
