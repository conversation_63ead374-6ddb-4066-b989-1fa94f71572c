export type DeviceStatus =
  | 'متاح للبيع' // InStock
  | 'بانتظار إرسال للصيانة' // PendingMaintenanceSend
  | 'تحتاج صيانة' // NeedsMaintenance
  | 'بانتظار استلام في الصيانة' // PendingMaintenanceReceive
  | 'قيد الإصلاح' // InMaintenance
  | 'بانتظار تسليم من الصيانة' // PendingMaintenanceDelivery
  | 'بانتظار استلام في المخزن' // PendingWarehouseReceive (العائدة من الصيانة)
  | 'بانتظار قطع غيار' // AwaitingParts
  | 'مراجعة الطلب من الإدارة' // AwaitingAdminReview
  | 'تم التسليم' // Delivered
  | 'مرسل للمخزن' // SentToWarehouse
  | 'غير قابل للإصلاح' // Unrepairable
  | 'مباع' // Sold
  | 'تالف' // Damaged
  | 'معيب' // Defective
  | 'قيد النقل'; // InTransit
export type WarehouseType = 'رئيسي' | 'فرعي';
export type ReturnReason = 'خلل مصنعي' | 'رغبة العميل' | 'سبب آخر';
export type MaintenanceResult =
  | 'Repaired'
  | 'Unrepairable-Defective'
  | 'Unrepairable-Damaged';

export type Device = {
  id: string; // IMEI or custom ID
  model: string;
  status: DeviceStatus;
  storage: string;
  price: number;
  condition: 'جديد' | 'مستخدم';
  warehouseId?: number;
  supplierId?: number;
  dateAdded: Date;
  // معلومات الاستبدال
  replacementInfo?: {
    isReplacement: boolean; // هل هذا الجهاز بديل لجهاز آخر؟
    originalDeviceId?: string; // الجهاز الأصلي الذي تم استبداله
    replacedWithId?: string; // الجهاز البديل (إذا تم استبدال هذا الجهاز)
    returnOrderId?: number; // رقم أمر المرتجع المرتبط
    replacementDate?: Date;
    replacementReason?: string; // سبب الاستبدال
  };
};

export type Contact = {
  id: number;
  name: string;
  phone: string;
  email: string;
};

export type Warehouse = {
  id: number;
  name: string;
  type: WarehouseType;
  location: string;
};

export type SaleItem = {
  id?: number;
  saleId?: number;
  deviceId: string;
  model: string;
  price: number;
  condition: 'جديد' | 'مستخدم';
  createdAt?: Date;
};

export type Sale = {
  id: number;
  soNumber: string;
  opNumber: string;
  date: Date;
  clientName: string;
  warehouseName: string;
  items: SaleItem[];
  notes: string;
  warrantyPeriod: string;
  employeeName: string;
  createdAt: Date;
  attachments?: string[]; // ← إضافة المرفقات (اختيارية للتوافق مع البيانات القديمة)
};

export type ReturnItem = {
  id?: number;
  returnId?: number;
  deviceId: string;
  model: string;
  returnReason: string;
  replacementDeviceId?: string;
  isReplacement?: boolean; // هل هذا استبدال أم مرتجع عادي
  originalDeviceId?: string; // في حالة الاستبدال، الجهاز الأصلي
  createdAt?: Date;
};

export type Return = {
  id: number;
  roNumber: string;
  opReturnNumber: string;
  date: Date;
  saleId: number;
  soNumber: string;
  clientName: string;
  warehouseName: string;
  items: ReturnItem[];
  notes: string;
  status: 'معلق' | 'مقبول' | 'مرفوض' | 'مكتمل'; // حالة المرتجع
  processedBy?: string; // من قام بمعالجة المرتجع
  processedDate?: Date;
  employeeName: string; // ← اسم الموظف المنشئ
  createdAt: Date;
  attachments?: string[]; // ← المرفقات (اختيارية للتوافق مع البيانات القديمة)
};

// نوع جديد لتتبع الأجهزة المرتجعة
export type DeviceReturnHistory = {
  deviceId: string;
  returnOrderId: number;
  returnDate: Date;
  returnReason: ReturnReason;
  isReplacement: boolean;
  replacementDeviceId?: string;
  originalDeviceId?: string;
  status: 'returned' | 'resold'; // مرتجع أم تم بيعه من جديد
};

export type ActivityLog = {
  id: string;
  type:
    | 'sale'
    | 'return'
    | 'maintenance'
    | 'supply'
    | 'evaluation'
    | 'transfer'
    | 'request'
    | 'message'
    | 'stocktake_started'
    | 'stocktake_completed';
  description: string;
  date: Date;
  username: string;
};

export type AuditLog = {
  id: string;
  timestamp: Date;
  userId: number;
  username: string;
  operation: string;
  details: string;
};

// New types for Supply Chain
export type Manufacturer = {
  id: number;
  name: string;
};

export type DeviceModel = {
  id: number;
  manufacturerId: string; // Changed to string to handle BigInt serialization
  name: string;
};

export type SupplyOrderItem = {
  id?: number;
  supplyOrderId?: number;
  imei: string;
  model: string;
  manufacturer: string;
  condition: 'جديد' | 'مستخدم';
  createdAt?: Date;
};

export type SupplyOrder = {
  id: number; // Operation ID, auto-increment
  supplyOrderId: string; // SO-xxxx, auto-generated
  supplierId: number;
  invoiceNumber: string;
  supplyDate: Date;
  warehouseId: number;
  employeeName: string;
  items: SupplyOrderItem[];
  notes: string;
  invoiceFileName?: string;
  referenceNumber?: string;
  createdAt: Date;
  status?: 'draft' | 'completed'; // حالة الأمر: مسودة أو مكتمل
};

export type AcceptanceOrderItem = {
  deviceId: string; // IMEI or custom ID
  model: string;
  condition: 'جديد' | 'مستخدم';
  storage: string;
  price: number;
  notes?: string;
};

export type AcceptanceOrder = {
  id: number;
  acceptanceId: string; // ACC-xxxx, auto-generated
  date: Date;
  warehouseId: number;
  employeeName: string;
  items: AcceptanceOrderItem[];
  notes?: string;
};

// Types for Grading and Evaluation
export type EvaluationGrade = 'بدون' | 'A++' | 'A' | 'B' | 'C' | 'D';
export type ScreenGrade =
  | 'بدون'
  | 'A+'
  | 'M1'
  | 'M2'
  | 'M3'
  | 'M4'
  | 'N1'
  | 'N2'
  | 'F1'
  | 'F2'
  | 'F3';
export type NetworkGrade = 'مفتوح رسمي' | 'مغلق';
export type FinalGrade = 'جاهز للبيع' | 'يحتاج صيانة' | 'عيب فني' | 'تالف';
export type FaultType =
  | 'شاشة'
  | 'بطارية'
  | 'منفذ شحن'
  | 'كاميرا'
  | 'صوت'
  | 'لمس'
  | 'حساس'
  | 'هزاز'
  | 'وايفاي'
  | 'ذاكره'
  | 'بطاقة sim'
  | 'أعطال أخرى';
export type DamageType = 'شاشة' | 'ماذر بورد' | 'الغرق' | 'أخرى';

export type EvaluatedDevice = {
  id?: number;
  evaluationOrderId?: number;
  deviceId: string;
  model: string;
  externalGrade: string;
  screenGrade: string;
  networkGrade: string;
  finalGrade: string;
  fault?: string;
  damageType?: string;
  createdAt?: Date;
};

export type EvaluationOrder = {
  id: number;
  orderId: string;
  employeeName: string;
  date: Date;
  items: EvaluatedDevice[];
  notes?: string;
  createdAt: Date;
};

export type MaintenanceLog = {
  deviceId: string;
  model: string;
  repairDate: Date; // Date sent from maintenance
  notes?: string;
  result: MaintenanceResult;
  status: 'pending' | 'acknowledged';
  acknowledgedDate?: Date; // Date received by warehouse
  warehouseName?: string;
  acknowledgedBy?: string;
};

export type MaintenanceOrderItem = {
  id?: number;
  maintenanceOrderId?: number;
  deviceId: string;
  model: string;
  fault?: string;
  notes?: string;
  createdAt?: Date;
};

export type MaintenanceOrder = {
  id: number;
  orderNumber: string; // e.g., MAINT-005
  referenceNumber?: string;
  date: Date;
  employeeName: string; // Warehouse employee who created the order
  maintenanceEmployeeId?: number; // Maintenance employee assigned to the order
  maintenanceEmployeeName?: string; // Maintenance employee name
  items: MaintenanceOrderItem[];
  notes?: string;
  attachmentName?: string;
  status: 'wip' | 'completed'; // Removed 'draft' status
  source?: 'warehouse' | 'direct'; // Track where the order originated from
  createdAt: Date;
};

export type DeliveryOrderItem = {
  id?: number;
  deliveryOrderId?: number;
  deviceId: string;
  model: string;
  result: string;
  fault?: string;
  damage?: string;
  notes?: string;
  createdAt?: Date;
};

export type DeliveryOrder = {
  id: number;
  deliveryOrderNumber: string;
  referenceNumber?: string;
  date: Date;
  employeeName: string;
  receivingEmployeeName?: string; // Name of the employee receiving the devices
  warehouseId: number; // Added warehouseId
  warehouseName: string; // Added warehouseName (required by database)
  items: DeliveryOrderItem[];
  notes?: string;
  attachmentName?: string;
  createdAt: Date;
};

// New type for Maintenance Receipt Order Items
export type MaintenanceReceiptOrderItem = {
  id?: number;
  maintenanceReceiptOrderId?: number;
  deviceId: string;
  model: string;
  result: string;
  fault?: string;
  damage?: string;
  notes?: string;
  createdAt?: Date;
};

// New type for Maintenance Receipt Orders
export type MaintenanceReceiptOrder = {
  id: number;
  receiptNumber: string; // e.g., REC-1, REC-2, etc.
  referenceNumber?: string;
  date: Date;
  employeeName: string; // Employee who received the devices
  maintenanceEmployeeName?: string;
  items: MaintenanceReceiptOrderItem[]; // Devices received from maintenance
  notes?: string;
  attachmentName?: string;
  status?: string;
  createdAt: Date;
};


// New types for Warehouse Transfer
export type WarehouseTransferItem = {
  deviceId: string;
  model: string;
};

export type WarehouseTransfer = {
  id: number;
  transferNumber: string; // e.g., WTO-1
  date: Date;
  fromWarehouseId: number;
  fromWarehouseName: string;
  toWarehouseId: number;
  toWarehouseName: string;
  items: WarehouseTransferItem[];
  status: 'pending' | 'completed';
  notes?: string;
  employeeName: string;
  referenceNumber?: string;
};

// Types for Employee Requests
export type EmployeeRequestType = 'تعديل' | 'إعادة نظر' | 'حذف';
export type EmployeeRequestPriority = 'عادي' | 'طاريء' | 'طاريء جدا';
export type EmployeeRequestStatus = 'قيد المراجعة' | 'تم التنفيذ' | 'مرفوض';
export type RelatedOrderType =
  | 'supply'
  | 'sale'
  | 'return'
  | 'evaluation'
  | 'maintenance'
  | 'warehouse_transfer'
  | 'delivery'
  | 'stocktake_started'
  | 'stocktake_completed'
  | 'stocktake_draft_saved';

export type EmployeeRequest = {
  id: number;
  requestNumber: string; // REQ-1, REQ-2 etc.
  employeeName: string;
  employeeId: number;
  requestDate: Date;
  relatedOrderType: RelatedOrderType;
  relatedOrderDisplayId: string; // e.g., 'SO-1', 'SUP-2'
  relatedOrderId: number; // The actual ID of the order
  requestType: EmployeeRequestType;
  priority: EmployeeRequestPriority;
  notes: string;
  attachmentName?: string;
  status: EmployeeRequestStatus;
  adminNotes?: string;
  resolutionDate?: Date; // ISO string
};

// Types for User Management
export type PagePermission = {
  view: boolean;
  create: boolean;
  edit: boolean;
  delete: boolean;
  viewAll?: boolean; // Special permission for messaging
  manage?: number[]; // Array of warehouse IDs the user can manage for direct transfers
  acceptWithoutWarranty?: boolean; // Special permission for returns
};

export const permissionPages = [
  'dashboard',
  'track',
  'supply',
  'acceptDevices',
  'grading',
  'inventory',
  'sales',
  'maintenance',
  'maintenanceTransfer',
  'warehouseTransfer',
  'clients',
  'pricing',
  'returns',
  'warehouses',
  'users',
  'reports',
  'stocktaking',
  'settings',
  'requests',
  'messaging',
] as const;
export type PermissionPageKey = (typeof permissionPages)[number];

export type AppPermissions = Record<PermissionPageKey, PagePermission>;

export type User = {
  id: number;
  name: string;
  username: string; // Added username
  email: string;
  phone?: string; // Added phone
  photo?: string; // Added photo URL
  role?: string; // Added role/job title
  branchLocation?: string; // Added branch/location
  lastLogin?: Date;
  permissions: AppPermissions;
  status?: 'Active' | 'Inactive';
  warehouseAccess?: number[]; // Added access to warehouses
};

export type SystemSettings = {
  logoUrl: string;
  companyNameAr: string;
  companyNameEn: string;
  addressAr: string;
  addressEn: string;
  phone: string;
  email: string;
  website: string;
  footerTextAr: string;
  footerTextEn: string;
  defaultWarehouseId?: number; // Added default warehouse ID
};

// Network and Connection Types
export interface ConnectedDevice {
  id: string;
  name: string;
  ip: string;
  connectedAt: Date;
  status: 'متصل' | 'محظور' | 'مسموح';
  lastActivity?: Date;
  deviceType?: string;
  userAgent?: string;
}

export interface ServerInfo {
  port: number;
  ipAddress: string;
  isActive: boolean;
  remoteAccessEnabled: boolean;
}

// Types for Internal Messaging
export type MessageStatus = 'مرسلة' | 'مقروءة' | 'تم الرد' | 'تم الحل';

export type MessageRecipient = {
  id: number;
  messageId: number;
  userId: number;
  isRead: boolean;
  readAt?: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
};

export type InternalMessage = {
  id: number;
  threadId: number;
  senderId: number;
  senderName: string;
  recipientId: number; // 0 for all
  recipientName: string;
  text: string;
  attachmentName?: string;
  attachmentContent?: string; // Base64 encoded content (للصور الصغيرة فقط)
  attachmentType?: string; // Mime type
  attachmentUrl?: string; // URL للملف المحفوظ
  attachmentFileName?: string; // اسم الملف المحفوظ
  attachmentSize?: number; // حجم الملف
  sentDate: Date;
  status: MessageStatus;
  isRead: boolean;
  parentMessageId?: number;
  employeeRequestId?: number;
  resolutionNote?: string;
  // New relational fields - use these instead of recipientIds JSON
  recipients?: MessageRecipient[];
  // Keep for backward compatibility during migration
  recipientIds?: number[];
};

// --- V2 STOCKTAKING SYSTEM ---

export type StocktakeStatusV2 = 'active' | 'paused' | 'completed' | 'draft';

export type StocktakeScope = {
  warehouseIds: number[];
  deviceModelIds: number[];
  includeMaintenance?: boolean;
};

export type StocktakeResultItem = {
  deviceId: string;
  expectedWarehouseId?: number;
  actualWarehouseId?: number;
  soldDate?: Date;
};

export type StocktakeResultCategory = {
  count: number;
  items: StocktakeResultItem[];
};

export type StocktakeResult = {
  matched: StocktakeResultCategory;
  missing: StocktakeResultCategory;
  extra: StocktakeResultCategory;
  misplaced: StocktakeResultCategory;
  sold: StocktakeResultCategory;
  duplicated: StocktakeResultCategory;
};

export type Stocktake = {
  id: string; // يبقى string (معرف وليس تاريخ)
  scope: StocktakeScope;
  userId: number;
  userName: string;
  startTime: Date;
  endTime: Date | null;
  status: StocktakeStatusV2;
  processedSerialNumbers: string[];
  result: StocktakeResult | null;
  notes?: string;
  orderNumber?: string;
  responsibleUser?: string;
  scheduledDate?: Date;
};

export type StocktakeDraft = Stocktake & {
  lastSavedAt: string;
};

export type StocktakeHistory = {
  id: string; // Changed to string
  completedAt: string;
  userId: number;
  userName: string;
  scope: StocktakeScope;
  summary: {
    expected: number;
    matched: number;
    missing: number;
    extra: number;
    misplaced: number;
    sold: number;
    inMaintenance: number;
  };
  result: StocktakeResult;
};


// --- OLD TYPES FOR V1 STOCKTAKING SYSTEM (to be deprecated) ---
export type StocktakeStatus = 'مسودة' | 'جاري' | 'مكتمل' | 'معلق' | 'ملغي';

export type StocktakeItemV1 = {
  deviceId: string;
  expectedInStock: boolean; // هل من المتوقع وجود الجهاز
  actuallyFound: boolean; // هل تم العثور على الجهاز فعلياً
  condition?: 'جيد' | 'تالف' | 'معيب'; // حالة الجهاز عند الجرد
  notes?: string; // ملاحظات خاصة بالجهاز
  photoUrl?: string; // صورة للجهاز (في حالة التلف)
  scannedAt?: string; // وقت المسح
  location?: string; // الموقع داخل المخزن
};

export type StocktakeDiscrepancy = {
  type: 'مفقود' | 'زائد' | 'تالف' | 'موقع_خاطئ';
  deviceId: string;
  model: string;
  expectedLocation?: string;
  actualLocation?: string;
  notes?: string;
  severity: 'منخفض' | 'متوسط' | 'عالي' | 'حرج';
  resolved: boolean;
  resolutionNotes?: string;
  resolvedBy?: number;
  resolvedAt?: string;
};

export type StocktakeV1 = {
  id: number;
  operationNumber: string; // رقم العملية
  warehouseId: number;
  warehouseName: string;
  selectedModel: string; // 'all' أو موديل محدد
  status: StocktakeStatus;
  createdBy: number;
  createdByName: string;
  createdAt: Date;
  startedAt?: string;
  completedAt?: string;
  lastModifiedAt: string;
  
  // إحصائيات
  totalExpected: number;
  totalScanned: number;
  totalMatching: number;
  totalMissing: number;
  totalExtra: number;
  totalDiscrepancies: number;
  
  // البيانات
  items: StocktakeItemV1[];
  discrepancies: StocktakeDiscrepancy[];
  
  // الملاحظات والمرفقات
  notes?: string;
  attachments?: string[];
  
  // معلومات المراجعة
  reviewedBy?: number;
  reviewedByName?: string;
  reviewedAt?: string;
  reviewNotes?: string;
  approved?: boolean;
};

export type StocktakeFilter = {
  status?: StocktakeStatus;
  warehouseId?: number;
  dateFrom?: Date;
  dateTo?: Date;
  createdBy?: number;
  model?: string;
};

// ===== أنواع مساعدة للتواريخ =====
export type DateInput = Date | string | number;
export type DateRange = {
  from?: Date;
  to?: Date;
};

export type DateFormatOptions = {
  arabic?: boolean;
  format?: 'full' | 'short' | 'time' | 'date';
  timezone?: string; // يبقى string (ليس تاريخ)
};

// نوع موحد للتواريخ في الواجهات
export interface DateFields {
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}

// نوع للعمليات التي تتطلب تواريخ
export interface TimestampedOperation {
  timestamp: Date;
  operation: string;
  userId?: number;
}
