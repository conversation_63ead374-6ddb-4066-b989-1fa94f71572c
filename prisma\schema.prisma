generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Permission {
  id              Int              @id @default(autoincrement())
  name            String           @unique
  displayName     String
  category        String
  description     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  userPermissions UserPermission[]

  @@map("permissions")
}

model UserPermission {
  id           Int        @id @default(autoincrement())
  userId       Int
  permissionId Int
  canView      Boolean    @default(false)
  canCreate    Boolean    @default(false)
  canEdit      Boolean    @default(false)
  canDelete    Boolean    @default(false)
  canViewAll   Boolean    @default(false)
  canManage    Boolean    @default(false)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, permissionId])
  @@map("user_permissions")
}

model UserWarehouseAccess {
  id          Int       @id @default(autoincrement())
  userId      Int
  warehouseId Int
  accessType  String    @default("read")
  canTransfer Boolean   @default(false)
  canAudit    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([userId, warehouseId])
  @@map("user_warehouse_access")
}

model DeviceReplacement {
  id                  Int      @id @default(autoincrement())
  originalDeviceId    String
  replacementDeviceId String
  reason              String
  replacementDate     DateTime @default(now())
  notes               String?
  status              String   @default("active")
  processedBy         String?
  approvedBy          String?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  originalDevice      Device   @relation("OriginalDevice", fields: [originalDeviceId], references: [id])
  replacementDevice   Device   @relation("ReplacementDevice", fields: [replacementDeviceId], references: [id])

  @@unique([originalDeviceId, replacementDeviceId])
  @@map("device_replacements")
}

model MessageRecipient {
  id        Int             @id @default(autoincrement())
  messageId Int
  userId    Int
  isRead    Boolean         @default(false)
  readAt    DateTime?
  message   InternalMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id])

  @@unique([messageId, userId])
  @@map("message_recipients")
}

model User {
  id                  Int                   @id @default(autoincrement())
  email               String                @unique
  name                String?
  username            String?               @unique @default("user")
  role                String?               @default("user")
  phone               String?               @default("")
  photo               String?               @default("")
  status              String?               @default("Active")
  lastLogin           DateTime?
  branchLocation      String?
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @default(now()) @updatedAt
  posts               Post[]
  createdDatabases    Database[]
  messageRecipients   MessageRecipient[]
  notifications       Notification[]
  responseTemplates   ResponseTemplate[]
  supplyOrderDrafts   SupplyOrderDraft[]
  userPermissions     UserPermission[]
  userWarehouseAccess UserWarehouseAccess[]

  @@map("users")
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  authorId  Int
  author    User    @relation(fields: [authorId], references: [id])
}

model SystemSetting {
  id             Int      @id @default(1)
  logoUrl        String   @default("")
  companyNameAr  String   @default("")
  companyNameEn  String   @default("")
  addressAr      String   @default("")
  addressEn      String   @default("")
  phone          String   @default("")
  email          String   @default("")
  website        String   @default("")
  footerTextAr   String   @default("")
  footerTextEn   String   @default("")
  reportSettings Json?
  updatedAt      DateTime @default(now()) @updatedAt
  createdAt      DateTime @default(now())
}

model DeviceModel {
  id             Int      @id @default(autoincrement())
  name           String
  manufacturerId BigInt
  category       String   @default("هاتف ذكي")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([name, manufacturerId])
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String
  details   String
}

model SupplyOrder {
  id              Int               @id @default(autoincrement())
  supplyOrderId   String            @unique
  supplierId      Int
  invoiceNumber   String?
  supplyDate      DateTime
  warehouseId     Int
  employeeName    String
  notes           String?
  invoiceFileName String?
  referenceNumber String?
  createdAt       DateTime          @default(now())
  status          String?           @default("completed")
  items           SupplyOrderItem[]
}

model SupplyOrderItem {
  id            Int         @id @default(autoincrement())
  supplyOrderId Int
  imei          String
  model         String
  manufacturer  String
  condition     String
  createdAt     DateTime    @default(now())
  supplyOrder   SupplyOrder @relation(fields: [supplyOrderId], references: [id], onDelete: Cascade)

  @@map("supply_order_items")
}

model SupplyOrderDraft {
  id            Int                          @id @default(autoincrement())
  userId        Int
  supplyOrderId String?
  notes         String?
  createdAt     DateTime                     @default(now())
  updatedAt     DateTime                     @updatedAt
  attachments   SupplyOrderDraftAttachment[]
  items         SupplyOrderDraftItem[]
  user          User                         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("supply_order_drafts")
}

model SupplyOrderDraftItem {
  id        Int              @id @default(autoincrement())
  draftId   Int
  deviceId  String
  model     String
  quantity  Int              @default(1)
  notes     String?
  createdAt DateTime         @default(now())
  draft     SupplyOrderDraft @relation(fields: [draftId], references: [id], onDelete: Cascade)

  @@map("supply_order_draft_items")
}

model SupplyOrderDraftAttachment {
  id         Int              @id @default(autoincrement())
  draftId    Int
  fileName   String
  fileType   String
  fileSize   Int
  fileUrl    String
  uploadedAt DateTime         @default(now())
  draft      SupplyOrderDraft @relation(fields: [draftId], references: [id], onDelete: Cascade)

  @@map("supply_order_draft_attachments")
}

model Sale {
  id             Int              @id @default(autoincrement())
  soNumber       String           @unique
  opNumber       String
  date           DateTime
  clientName     String
  warehouseName  String
  notes          String?
  warrantyPeriod String
  employeeName   String
  createdAt      DateTime         @default(now())
  attachments    SaleAttachment[]
  items          SaleItem[]
}

model SaleItem {
  id        Int      @id @default(autoincrement())
  saleId    Int
  deviceId  String
  model     String
  price     Float
  condition String
  createdAt DateTime @default(now())
  sale      Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)

  @@map("sale_items")
}

model SaleAttachment {
  id         Int      @id @default(autoincrement())
  saleId     Int
  fileName   String
  fileType   String
  fileSize   Int
  fileUrl    String
  uploadedAt DateTime @default(now())
  sale       Sale     @relation(fields: [saleId], references: [id], onDelete: Cascade)

  @@map("sale_attachments")
}

model Device {
  id                   String              @id
  model                String
  status               String
  storage              String
  price                Float
  condition            String
  warehouseId          Int?
  supplierId           Int?
  dateAdded            DateTime            @default(now())
  originalReplacements DeviceReplacement[] @relation("OriginalDevice")
  replacementFor       DeviceReplacement[] @relation("ReplacementDevice")
}

model Warehouse {
  id         Int                   @id @default(autoincrement())
  name       String
  type       String
  location   String
  createdAt  DateTime              @default(now())
  updatedAt  DateTime              @default(now()) @updatedAt
  userAccess UserWarehouseAccess[]
}

model Return {
  id             Int                @id @default(autoincrement())
  roNumber       String             @unique
  opReturnNumber String
  date           DateTime
  saleId         Int
  soNumber       String
  clientName     String
  warehouseName  String
  notes          String?
  status         String             @default("معلق")
  processedBy    String?
  processedDate  DateTime?
  employeeName   String
  createdAt      DateTime           @default(now())
  attachments    ReturnAttachment[]
  items          ReturnItem[]
}

model ReturnItem {
  id                  Int      @id @default(autoincrement())
  returnId            Int
  deviceId            String
  model               String
  returnReason        String
  replacementDeviceId String?
  isReplacement       Boolean  @default(false)
  originalDeviceId    String?
  createdAt           DateTime @default(now())
  return              Return   @relation(fields: [returnId], references: [id], onDelete: Cascade)

  @@map("return_items")
}

model ReturnAttachment {
  id         Int      @id @default(autoincrement())
  returnId   Int
  fileName   String
  fileType   String
  fileSize   Int
  fileUrl    String
  uploadedAt DateTime @default(now())
  return     Return   @relation(fields: [returnId], references: [id], onDelete: Cascade)

  @@map("return_attachments")
}

model EvaluationOrder {
  id               Int                   @id @default(autoincrement())
  orderId          String                @unique
  employeeName     String
  date             DateTime
  notes            String?
  status           String                @default("معلق")
  acknowledgedBy   String?
  acknowledgedDate DateTime?
  warehouseName    String?
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @default(now()) @updatedAt
  items            EvaluationOrderItem[]

  @@map("evaluation_orders")
}

model EvaluationOrderItem {
  id                Int             @id @default(autoincrement())
  evaluationOrderId Int
  deviceId          String
  model             String
  externalGrade     String
  screenGrade       String
  networkGrade      String
  finalGrade        String
  fault             String?
  damageType        String?
  createdAt         DateTime        @default(now())
  evaluationOrder   EvaluationOrder @relation(fields: [evaluationOrderId], references: [id], onDelete: Cascade)

  @@map("evaluation_order_items")
}

model Client {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Supplier {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model MaintenanceOrder {
  id                      Int                    @id @default(autoincrement())
  orderNumber             String                 @unique
  referenceNumber         String?
  date                    DateTime
  employeeName            String
  maintenanceEmployeeId   Int?
  maintenanceEmployeeName String?
  notes                   String?
  attachmentName          String?
  status                  String                 @default("wip")
  source                  String                 @default("warehouse")
  createdAt               DateTime               @default(now())
  items                   MaintenanceOrderItem[] @relation("MaintenanceOrderItems")
}

model MaintenanceOrderItem {
  id                 Int              @id @default(autoincrement())
  maintenanceOrderId Int
  deviceId           String
  model              String
  fault              String?
  notes              String?
  createdAt          DateTime         @default(now())
  maintenanceOrder   MaintenanceOrder @relation("MaintenanceOrderItems", fields: [maintenanceOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_order_items")
}

model MaintenanceReceiptOrder {
  id                      Int                           @id @default(autoincrement())
  receiptNumber           String                        @unique
  referenceNumber         String?
  date                    DateTime
  employeeName            String
  maintenanceEmployeeName String?
  notes                   String?
  attachmentName          String?
  status                  String                        @default("completed")
  createdAt               DateTime                      @default(now())
  items                   MaintenanceReceiptOrderItem[] @relation("MaintenanceReceiptOrderItems")
}

model MaintenanceReceiptOrderItem {
  id                        Int                     @id @default(autoincrement())
  maintenanceReceiptOrderId Int
  deviceId                  String
  model                     String
  result                    String
  fault                     String?
  damage                    String?
  notes                     String?
  createdAt                 DateTime                @default(now())
  maintenanceReceiptOrder   MaintenanceReceiptOrder @relation("MaintenanceReceiptOrderItems", fields: [maintenanceReceiptOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_receipt_order_items")
}

model DeliveryOrder {
  id                  Int                 @id @default(autoincrement())
  deliveryOrderNumber String              @unique
  referenceNumber     String?
  date                DateTime
  warehouseId         Int
  warehouseName       String
  employeeName        String
  notes               String?
  attachmentName      String?
  status              String              @default("completed")
  createdAt           DateTime            @default(now())
  items               DeliveryOrderItem[]
}

model DeliveryOrderItem {
  id              Int           @id @default(autoincrement())
  deliveryOrderId Int
  deviceId        String
  model           String
  result          String
  fault           String?
  damage          String?
  notes           String?
  createdAt       DateTime      @default(now())
  deliveryOrder   DeliveryOrder @relation(fields: [deliveryOrderId], references: [id], onDelete: Cascade)

  @@map("delivery_order_items")
}

model MaintenanceLog {
  id               Int       @id @default(autoincrement())
  deviceId         String
  model            String
  repairDate       DateTime
  notes            String?
  result           String?
  status           String    @default("pending")
  acknowledgedDate DateTime?
  warehouseName    String?
  acknowledgedBy   String?
  createdAt        DateTime  @default(now())

  @@map("maintenance_logs")
}

model EmployeeRequest {
  id                    Int                 @id @default(autoincrement())
  requestNumber         String              @unique
  requestType           String
  priority              String
  notes                 String
  status                String              @default("قيد المراجعة")
  requestDate           DateTime
  employeeName          String
  employeeId            Int
  relatedOrderType      String?
  relatedOrderId        Int?
  relatedOrderDisplayId String?
  attachmentName        String?
  adminNotes            String?
  processedBy           Int?
  processedDate         DateTime?
  isArchived            Boolean             @default(false)
  archivedAt            DateTime?
  searchVector          String?
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @default(now()) @updatedAt
  attachmentFiles       RequestAttachment[]
  comments              RequestComment[]
  tags                  RequestTag[]

  @@map("employee_requests")
}

model RequestAttachment {
  id         Int             @id @default(autoincrement())
  requestId  Int
  fileName   String
  fileType   String
  fileSize   Int
  filePath   String
  fileUrl    String?
  mimeType   String
  uploadedBy Int
  uploadedAt DateTime        @default(now())
  isDeleted  Boolean         @default(false)
  deletedAt  DateTime?
  request    EmployeeRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)

  @@map("request_attachments")
}

model RequestTag {
  id        Int             @id @default(autoincrement())
  requestId Int
  tagName   String
  tagValue  String?
  createdAt DateTime        @default(now())
  request   EmployeeRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)

  @@unique([requestId, tagName])
  @@map("request_tags")
}

model RequestComment {
  id          Int                 @id @default(autoincrement())
  requestId   Int
  userId      Int
  userName    String?
  userRole    String?
  comment     String
  commentType String              @default("comment")
  isInternal  Boolean             @default(false)
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  attachments CommentAttachment[]
  request     EmployeeRequest     @relation(fields: [requestId], references: [id], onDelete: Cascade)

  @@map("request_comments")
}

model CommentAttachment {
  id         Int            @id @default(autoincrement())
  commentId  Int
  fileName   String
  fileType   String
  fileSize   Int
  fileUrl    String
  uploadedAt DateTime       @default(now())
  comment    RequestComment @relation(fields: [commentId], references: [id], onDelete: Cascade)

  @@map("comment_attachments")
}

model ResponseTemplate {
  id         Int      @id @default(autoincrement())
  name       String
  category   String
  title      String
  content    String
  variables  Json?
  isSystem   Boolean  @default(false)
  isActive   Boolean  @default(true)
  usageCount Int      @default(0)
  createdBy  Int?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  creator    User?    @relation(fields: [createdBy], references: [id])

  @@map("response_templates")
}

model Notification {
  id             Int       @id @default(autoincrement())
  userId         Int
  type           String
  title          String
  message        String
  requestId      Int?
  requestNumber  String?
  employeeName   String?
  priority       String?
  actionRequired Boolean   @default(false)
  read           Boolean   @default(false)
  readAt         DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model InternalMessage {
  id                 Int                @id @default(autoincrement())
  threadId           Int
  senderId           Int
  senderName         String
  recipientId        Int
  recipientName      String
  text               String
  attachmentName     String?
  attachmentContent  String?
  attachmentType     String?
  attachmentUrl      String?
  attachmentFileName String?
  attachmentSize     Int?
  sentDate           DateTime
  status             String             @default("مرسلة")
  isRead             Boolean            @default(false)
  parentMessageId    Int?
  employeeRequestId  Int?
  resolutionNote     String?
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @default(now()) @updatedAt
  recipients         MessageRecipient[]

  @@map("internal_messages")
}

model DatabaseConnection {
  id        Int              @id @default(autoincrement())
  name      String           @unique
  host      String
  port      Int              @default(5432)
  database  String
  username  String
  password  String
  isActive  Boolean          @default(false)
  isDefault Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  backups   DatabaseBackup[]
  databases Database[]

  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual")
  status       String             @default("completed")
  createdBy    String?
  createdAt    DateTime           @default(now())
  connectionId Int
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)

  @@map("database_backups")
}

model Database {
  id           Int                @id @default(autoincrement())
  name         String
  connectionId Int
  owner        String             @default("")
  template     String             @default("template0")
  encoding     String             @default("UTF8")
  createdBy    Int
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @default(now()) @updatedAt
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  creator      User               @relation(fields: [createdBy], references: [id])

  @@unique([name, connectionId])
  @@map("databases")
}

model WarehouseTransfer {
  id             Int                           @id @default(autoincrement())
  transferNumber String                        @unique
  fromWarehouse  String
  toWarehouse    String
  deviceId       String
  model          String
  status         String                        @default("معلق")
  requestedBy    String
  approvedBy     String?
  transferredBy  String?
  receivedBy     String?
  requestDate    DateTime                      @default(now())
  approvalDate   DateTime?
  transferDate   DateTime?
  receiveDate    DateTime?
  notes          String?
  reason         String?
  priority       String                        @default("عادي")
  createdAt      DateTime                      @default(now())
  updatedAt      DateTime                      @updatedAt
  attachments    WarehouseTransferAttachment[]

  @@map("warehouse_transfers")
}

model WarehouseTransferAttachment {
  id         Int               @id @default(autoincrement())
  transferId Int
  fileName   String
  fileType   String
  fileSize   Int
  fileUrl    String
  uploadedAt DateTime          @default(now())
  transfer   WarehouseTransfer @relation(fields: [transferId], references: [id], onDelete: Cascade)

  @@map("warehouse_transfer_attachments")
}
