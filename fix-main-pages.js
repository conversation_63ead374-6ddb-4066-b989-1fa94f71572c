/**
 * Fix Main Pages Script
 * Date: 2025-08-04
 * Description: Fix date type issues in main application pages
 */

const fs = require('fs');
const path = require('path');

// Main pages to fix
const mainPages = [
  'app/(main)/maintenance/page.tsx',
  'app/(main)/maintenance-transfer/page.tsx',
  'app/(main)/inventory/page.tsx',
  'app/(main)/grading/page.tsx',
  'app/(main)/accept-devices/page.tsx',
  'app/(main)/warehouse-transfer/page.tsx',
  'app/(main)/track/page.tsx',
  'app/(main)/returns/page.tsx'
];

// Page-specific fixes
const pageFixes = [
  {
    search: /new Date\(\)\.toISOString\(\)/g,
    replace: 'new Date()',
    description: 'إزالة toISOString() غير الضرورية'
  },
  {
    search: /new Date\(\)\.toISOString\(\)\.slice\(0, 16\)/g,
    replace: 'new Date().toISOString().slice(0, 16)',
    description: 'الاحتفاظ بـ slice للـ datetime-local inputs'
  },
  {
    search: /new Date\(\)\.toISOString\(\)\.slice\(0, 10\)/g,
    replace: 'new Date().toISOString().slice(0, 10)',
    description: 'الاحتفاظ بـ slice للـ date inputs'
  },
  {
    search: /\.toLocaleDateString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString للاستبدال بـ formatDate'
  },
  {
    search: /\.toLocaleDateString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString للاستبدال بـ formatDate'
  },
  {
    search: /\.toLocaleString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleString للاستبدال بـ formatDateTime'
  },
  {
    search: /date\.getFullYear\(\)/g,
    replace: 'formatDate(date, { format: "full" })',
    description: 'استبدال التنسيق اليدوي بـ formatDate'
  },
  {
    search: /date\.getMonth\(\) \+ 1/g,
    replace: 'formatDate(date, { format: "full" })',
    description: 'استبدال التنسيق اليدوي بـ formatDate'
  },
  {
    search: /date\.getDate\(\)/g,
    replace: 'formatDate(date, { format: "full" })',
    description: 'استبدال التنسيق اليدوي بـ formatDate'
  }
];

// Special fixes for specific patterns
const specialFixes = [
  {
    search: /const formatDateTime = \(dateTimeString: string\): string => \{[\s\S]*?\};/g,
    replace: '// استخدم formatDateTime من date-utils بدلاً من هذه الدالة',
    description: 'إزالة دوال التنسيق المحلية'
  },
  {
    search: /function formatArabicDate\([\s\S]*?\}/g,
    replace: '// استخدم formatDate من date-utils بدلاً من هذه الدالة',
    description: 'إزالة دوال التنسيق العربية المحلية'
  }
];

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${filePath}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${filePath}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    console.log(`   📁 نسخة احتياطية: ${backupPath}\n`);
    
    return true;
  }

  return false;
}

async function fixMainPages() {
  console.log('🔧 إصلاح الصفحات الرئيسية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    mainPages.forEach(page => {
      const filePath = path.join(process.cwd(), page);
      
      if (fs.existsSync(filePath)) {
        console.log(`🔍 فحص: ${page}`);
        
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply page fixes
        if (applyFixes(filePath, pageFixes)) {
          totalFixed += pageFixes.length;
          filesModified.push(page);
        }
        
        // Apply special fixes
        if (applyFixes(filePath, specialFixes)) {
          totalFixed += specialFixes.length;
          if (!filesModified.includes(page)) {
            filesModified.push(page);
          }
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${page}`);
      }
    });

    // Generate summary
    console.log('📊 ملخص إصلاح الصفحات الرئيسية:');
    console.log('='.repeat(40));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الصفحات المعدلة:');
      filesModified.forEach((page, index) => {
        console.log(`${index + 1}. ${page}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      pageFixes: pageFixes,
      specialFixes: specialFixes
    };

    fs.writeFileSync('main-pages-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح الصفحات في: main-pages-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح الصفحات الرئيسية بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح في الصفحات الرئيسية');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح الصفحات الرئيسية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixMainPages()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح الصفحات الرئيسية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح الصفحات الرئيسية:', error);
      process.exit(1);
    });
}

module.exports = { fixMainPages };
