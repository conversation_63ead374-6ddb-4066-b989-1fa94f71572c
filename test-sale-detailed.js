// إنشاء توكن صحيح للاختبار
const createToken = (username, role) => {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData); // ترميز base64
};

// اختبار إنشاء فاتورة مبيعات مع توكن صحيح
const testSaleCreation = async () => {
  try {
    console.log('🧪 بدء اختبار إنشاء فاتورة...');
    
    // إنشاء توكن للـ admin
    const adminToken = createToken('admin', 'admin');
    console.log('🔑 توكن المصادقة:', adminToken);
    
    const saleData = {
      clientName: 'عميل اختبار',
      opNumber: '', // ترك فارغ لاختبار الإنشاء التلقائي
      warehouseName: 'المخزن الرئيسي', 
      notes: 'فاتورة اختبار',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [
        {
          deviceId: 'TEST001',
          model: 'Samsung Galaxy S23',
          price: 1500,
          condition: 'جديد'
        }
      ]
    };

    console.log('📤 إرسال البيانات للفاتورة الأولى...');

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(saleData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ نجح إنشاء الفاتورة الأولى!');
      console.log('📋 تفاصيل الفاتورة:');
      console.log(`   رقم الفاتورة (SO): ${result.soNumber}`);
      console.log(`   رقم الأمر (OP): ${result.opNumber}`);
      console.log(`   العميل: ${result.clientName}`);
      console.log(`   التاريخ: ${new Date(result.date).toLocaleString('ar-SA')}`);
      
      // انتظار ثانية واحدة ثم إنشاء فاتورة ثانية
      console.log('\n⏳ انتظار ثانية واحدة...');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('🔄 اختبار فاتورة ثانية...');
      
      const secondSaleData = {
        ...saleData,
        clientName: 'عميل اختبار 2',
        opNumber: '', // أيضاً فارغ لاختبار التسلسل
        items: [{
          deviceId: 'TEST003',
          model: 'Huawei P50',
          price: 1200,
          condition: 'جديد'
        }]
      };

      const secondResponse = await fetch('http://localhost:9005/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`
        },
        body: JSON.stringify(secondSaleData)
      });

      const secondResult = await secondResponse.json();
      
      if (secondResponse.ok) {
        console.log('✅ نجح إنشاء الفاتورة الثانية!');
        console.log(`   رقم الفاتورة (SO): ${secondResult.soNumber}`);
        console.log(`   رقم الأمر (OP): ${secondResult.opNumber}`);
        
        // التحقق من تسلسل أرقام الأوامر
        console.log('\n🔍 تحليل أرقام الأوامر:');
        console.log(`   الفاتورة الأولى - OP: ${result.opNumber}`);
        console.log(`   الفاتورة الثانية - OP: ${secondResult.opNumber}`);
        
        // محاولة تحويل إلى أرقام للمقارنة
        const firstOpNumber = parseInt(result.opNumber);
        const secondOpNumber = parseInt(secondResult.opNumber);
        
        if (!isNaN(firstOpNumber) && !isNaN(secondOpNumber)) {
          if (secondOpNumber === firstOpNumber + 1) {
            console.log('✅ أرقام الأوامر متسلسلة بشكل صحيح!');
          } else {
            console.log('⚠️  أرقام الأوامر غير متسلسلة:');
            console.log(`   الفرق: ${secondOpNumber - firstOpNumber}`);
          }
        } else {
          console.log('⚠️  لا يمكن تحويل أرقام الأوامر إلى أرقام صحيحة');
        }

        // اختبار ثالث مع رقم أمر مخصص
        console.log('\n🎯 اختبار فاتورة ثالثة مع رقم أمر مخصص...');
        
        const thirdSaleData = {
          ...saleData,
          clientName: 'عميل اختبار 3',
          opNumber: '100', // رقم أمر مخصص
          items: [{
            deviceId: 'TEST004',
            model: 'OnePlus 11',
            price: 1800,
            condition: 'جديد'
          }]
        };

        const thirdResponse = await fetch('http://localhost:9005/api/sales', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${adminToken}`
          },
          body: JSON.stringify(thirdSaleData)
        });

        const thirdResult = await thirdResponse.json();
        
        if (thirdResponse.ok) {
          console.log('✅ نجح إنشاء الفاتورة الثالثة!');
          console.log(`   رقم الفاتورة (SO): ${thirdResult.soNumber}`);
          console.log(`   رقم الأمر (OP): ${thirdResult.opNumber}`);
          console.log(`   هل الرقم المخصص تم حفظه؟ ${thirdResult.opNumber === '100' ? 'نعم ✅' : 'لا ❌'}`);
        } else {
          console.log('❌ فشل في إنشاء الفاتورة الثالثة:', thirdResult);
        }
        
      } else {
        console.log('❌ فشل في إنشاء الفاتورة الثانية:', secondResult);
      }
      
    } else {
      console.log('❌ فشل في إنشاء الفاتورة:', result);
    }

  } catch (error) {
    console.log('❌ خطأ في الاختبار:', error.message);
    console.log('💡 تأكد من أن الخادم يعمل على http://localhost:9005');
  }
};

// دالة btoa للـ Node.js
function btoa(str) {
  return Buffer.from(str, 'binary').toString('base64');
}

// تشغيل الاختبار
testSaleCreation();
