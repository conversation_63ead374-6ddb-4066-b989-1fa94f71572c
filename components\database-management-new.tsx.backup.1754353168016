'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Database,
  Plus,
  Download,
  Upload,
  Trash2,
  Settings,
  Check,
  AlertTriangle,
  Edit,
  Server,
  Shield,
  Clock,
  FileDown,
  History,
} from 'lucide-react';

interface DatabaseConnection {
  id: number;
  name: string;
  host: string;
  port: number;
  database: string;
  username: string;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface DatabaseBackup {
  id: number;
  name: string;
  description?: string;
  filePath: string;
  fileSize: string;
  backupType: string;
  status: string;
  createdBy?: string;
  createdAt: string;
  connection: {
    name: string;
    database: string;
    host: string;
  };
}

interface DatabaseInfo {
  name: string;
  size: string;
  tables: number;
  lastBackup?: string;
  owner: string;
}

export function DatabaseManagement() {
  const [connections, setConnections] = useState<DatabaseConnection[]>([]);
  const [backups, setBackups] = useState<DatabaseBackup[]>([]);
  const [databases, setDatabases] = useState<DatabaseInfo[]>([]);
  const [selectedConnection, setSelectedConnection] = useState<number | null>(null);
  const [selectedBackup, setSelectedBackup] = useState<number | null>(null);
  const [selectedDatabase, setSelectedDatabase] = useState<string | null>(null);
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [isCreatingDatabase, setIsCreatingDatabase] = useState(false);
  const [isSwitchingDatabase, setIsSwitchingDatabase] = useState(false);
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [showBackupDialog, setShowBackupDialog] = useState(false);
  const [showRestoreConfirm, setShowRestoreConfirm] = useState(false);
  const [showDatabaseDialog, setShowDatabaseDialog] = useState(false);
  const [showDatabaseList, setShowDatabaseList] = useState(false);
  const [editingConnection, setEditingConnection] = useState<DatabaseConnection | null>(null);
  const { toast } = useToast();

  const [newConnection, setNewConnection] = useState({
    name: '',
    host: 'localhost',
    port: 5432,
    database: '',
    username: '',
    password: '',
    isDefault: false,
  });

  const [backupForm, setBackupForm] = useState({
    name: '',
    description: '',
  });

  const [newDatabaseForm, setNewDatabaseForm] = useState({
    name: '',
    owner: '',
    template: 'template0',
    encoding: 'UTF8',
  });

  // تحميل قواعد البيانات المتاحة
  useEffect(() => {
    loadConnections();
    loadBackups();
    if (selectedConnection) {
      loadDatabases();
    }
  }, [selectedConnection]);

  const loadConnections = async () => {
    try {
      const response = await fetch('/api/database/connections');
      if (response.ok) {
        const data = await response.json();
        setConnections(data);
        if (data.length > 0 && !selectedConnection) {
          const defaultConn = data.find((c: DatabaseConnection) => c.isDefault);
          setSelectedConnection(defaultConn?.id || data[0].id);
        }
      }
    } catch (error) {
      console.error('Failed to load connections:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل اتصالات قواعد البيانات',
        variant: 'destructive',
      });
    }
  };

  const loadBackups = async () => {
    try {
      const response = await fetch('/api/database/backup');
      if (response.ok) {
        const data = await response.json();
        setBackups(data);
      }
    } catch (error) {
      console.error('Failed to load backups:', error);
    }
  };

  const loadDatabases = async () => {
    if (!selectedConnection) return;
    
    try {
      const response = await fetch(`/api/database/list?connectionId=${selectedConnection}`);
      if (response.ok) {
        const data = await response.json();
        setDatabases(data);
      }
    } catch (error) {
      console.error('Failed to load databases:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل قواعد البيانات',
        variant: 'destructive',
      });
    }
  };

  const resetConnectionForm = () => {
    setNewConnection({
      name: '',
      host: 'localhost',
      port: 5432,
      database: '',
      username: '',
      password: '',
      isDefault: false,
    });
    setEditingConnection(null);
  };

  const openNewConnectionDialog = () => {
    resetConnectionForm();
    setShowConnectionDialog(true);
  };

  const openEditConnectionDialog = (connection: DatabaseConnection) => {
    setEditingConnection(connection);
    setNewConnection({
      name: connection.name,
      host: connection.host,
      port: connection.port,
      database: connection.database,
      username: connection.username,
      password: '', // لا نعرض كلمة المرور الحالية
      isDefault: connection.isDefault,
    });
    setShowConnectionDialog(true);
  };

  const createOrUpdateConnection = async () => {
    try {
      const method = editingConnection ? 'PUT' : 'POST';
      const body = editingConnection 
        ? { ...newConnection, id: editingConnection.id }
        : newConnection;

      const response = await fetch('/api/database/connections', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        toast({
          title: editingConnection ? 'تم تحديث الاتصال' : 'تم إنشاء الاتصال',
          description: `تم ${editingConnection ? 'تحديث' : 'إنشاء'} اتصال: ${newConnection.name}`,
        });
        await loadConnections();
        setShowConnectionDialog(false);
        resetConnectionForm();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في العملية');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في العملية',
        variant: 'destructive',
      });
    }
  };

  const deleteConnection = async (id: number) => {
    try {
      const response = await fetch('/api/database/connections', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id }),
      });

      if (response.ok) {
        toast({
          title: 'تم حذف الاتصال',
          description: 'تم حذف اتصال قاعدة البيانات بنجاح',
        });
        await loadConnections();
        if (selectedConnection === id) {
          setSelectedConnection(connections.length > 1 ? connections[0].id : null);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف الاتصال');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في حذف الاتصال',
        variant: 'destructive',
      });
    }
  };

  const createDatabase = async () => {
    if (!selectedConnection) {
      toast({
        title: 'خطأ',
        description: 'يرجى اختيار اتصال قاعدة بيانات',
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingDatabase(true);
    try {
      const response = await fetch('/api/database/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connectionId: selectedConnection,
          ...newDatabaseForm,
        }),
      });

      if (response.ok) {
        toast({
          title: 'تم إنشاء قاعدة البيانات',
          description: `تم إنشاء قاعدة البيانات: ${newDatabaseForm.name}`,
        });
        await loadDatabases();
        setShowDatabaseDialog(false);
        setNewDatabaseForm({
          name: '',
          owner: '',
          template: 'template0',
          encoding: 'UTF8',
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إنشاء قاعدة البيانات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في إنشاء قاعدة البيانات',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingDatabase(false);
    }
  };

  const switchDatabase = async (databaseName: string) => {
    if (!selectedConnection) return;

    setIsSwitchingDatabase(true);
    try {
      const response = await fetch('/api/database/switch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connectionId: selectedConnection,
          databaseName,
        }),
      });

      if (response.ok) {
        toast({
          title: 'تم تغيير قاعدة البيانات',
          description: `تم التبديل إلى قاعدة البيانات: ${databaseName}`,
        });
        setSelectedDatabase(databaseName);
        await loadConnections(); // إعادة تحميل الاتصالات لتحديث الحالة
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في تغيير قاعدة البيانات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في تغيير قاعدة البيانات',
        variant: 'destructive',
      });
    } finally {
      setIsSwitchingDatabase(false);
    }
  };

  const deleteDatabase = async (databaseName: string) => {
    if (!selectedConnection) return;

    try {
      const response = await fetch('/api/database/delete', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connectionId: selectedConnection,
          databaseName,
        }),
      });

      if (response.ok) {
        toast({
          title: 'تم حذف قاعدة البيانات',
          description: `تم حذف قاعدة البيانات: ${databaseName}`,
        });
        await loadDatabases();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف قاعدة البيانات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في حذف قاعدة البيانات',
        variant: 'destructive',
      });
    }
  };

  const createBackup = async () => {
    if (!selectedConnection) {
      toast({
        title: 'خطأ',
        description: 'يرجى اختيار اتصال قاعدة بيانات',
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingBackup(true);
    try {
      const response = await fetch('/api/database/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          connectionId: selectedConnection,
          name: backupForm.name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,
          description: backupForm.description,
          createdBy: 'المدير', // يمكن الحصول على اسم المستخدم الحقيقي
        }),
      });

      if (response.ok) {
        toast({
          title: 'تم إنشاء النسخة الاحتياطية',
          description: 'تم أخذ نسخة احتياطية من قاعدة البيانات بنجاح',
        });
        await loadBackups();
        setShowBackupDialog(false);
        setBackupForm({ name: '', description: '' });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إنشاء النسخة الاحتياطية');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في إنشاء النسخة الاحتياطية',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingBackup(false);
    }
  };

  const restoreBackup = async () => {
    if (!selectedBackup) return;

    setIsRestoring(true);
    try {
      const response = await fetch('/api/database/restore', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          backupId: selectedBackup,
          targetConnectionId: selectedConnection,
          restoredBy: 'المدير',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: 'تمت الاستعادة بنجاح',
          description: `تم استعادة النسخة الاحتياطية: ${result.backup.name}`,
        });
        setShowRestoreConfirm(false);
        setSelectedBackup(null);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في استعادة النسخة الاحتياطية');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في استعادة النسخة الاحتياطية',
        variant: 'destructive',
      });
    } finally {
      setIsRestoring(false);
    }
  };

  const deleteBackup = async (id: number) => {
    try {
      const response = await fetch('/api/database/backup', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id }),
      });

      if (response.ok) {
        toast({
          title: 'تم حذف النسخة الاحتياطية',
          description: 'تم حذف النسخة الاحتياطية بنجاح',
        });
        await loadBackups();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف النسخة الاحتياطية');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في حذف النسخة الاحتياطية',
        variant: 'destructive',
      });
    }
  };

  const selectedConnectionData = connections.find(c => c.id === selectedConnection);
  const selectedBackupData = backups.find(b => b.id === selectedBackup);

  return (
    <div className="space-y-6">
      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">اتصالات قواعد البيانات</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connections.length}</div>
            <p className="text-xs text-muted-foreground">
              {connections.filter(c => c.isActive).length} متصل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قواعد البيانات</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{databases.length}</div>
            <p className="text-xs text-muted-foreground">
              {selectedDatabase ? `الحالية: ${selectedDatabase}` : 'لا يوجد اختيار'}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">النسخ الاحتياطية</CardTitle>
            <FileDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{backups.length}</div>
            <p className="text-xs text-muted-foreground">
              {backups.filter(b => b.status === 'completed').length} مكتملة
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الحجم الإجمالي</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {backups.reduce((total, backup) => {
                const size = parseFloat(backup.fileSize.replace(' MB', ''));
                return total + (isNaN(size) ? 0 : size);
              }, 0).toFixed(1)} MB
            </div>
            <p className="text-xs text-muted-foreground">مساحة النسخ الاحتياطية</p>
          </CardContent>
        </Card>
      </div>

      {/* قائمة الاتصالات */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              اتصالات قواعد البيانات
            </CardTitle>
            <Button onClick={openNewConnectionDialog}>
              <Plus className="h-4 w-4 mr-2" />
              إضافة اتصال
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {connections.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد اتصالات قواعد بيانات</p>
              <p className="text-sm">قم بإضافة اتصال جديد للبدء</p>
            </div>
          ) : (
            connections.map((conn) => (
              <div
                key={conn.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedConnection === conn.id
                    ? 'border-primary bg-primary/5'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedConnection(conn.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{conn.name}</h4>
                      {conn.isDefault && (
                        <Badge variant="default">افتراضي</Badge>
                      )}
                      {conn.isActive && (
                        <Badge variant="secondary">
                          <Check className="h-3 w-3 mr-1" />
                          متصل
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">
                      <Server className="h-3 w-3 inline mr-1" />
                      {conn.host}:{conn.port}/{conn.database}
                    </p>
                    <p className="text-xs text-gray-400">
                      المستخدم: {conn.username}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        openEditConnectionDialog(conn);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (confirm('هل أنت متأكد من حذف هذا الاتصال؟')) {
                          deleteConnection(conn.id);
                        }
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* قسم إدارة قواعد البيانات */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              قواعد البيانات
            </CardTitle>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setShowDatabaseList(!showDatabaseList)}
                disabled={!selectedConnection}
              >
                <Database className="h-4 w-4 mr-2" />
                {showDatabaseList ? 'إخفاء القائمة' : 'عرض القائمة'}
              </Button>
              <Button 
                onClick={() => setShowDatabaseDialog(true)}
                disabled={!selectedConnection}
              >
                <Plus className="h-4 w-4 mr-2" />
                إنشاء قاعدة بيانات
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {!selectedConnection ? (
            <div className="text-center py-8 text-gray-500">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>يرجى اختيار اتصال قاعدة بيانات أولاً</p>
            </div>
          ) : showDatabaseList ? (
            databases.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Server className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>لا توجد قواعد بيانات</p>
                <p className="text-sm">قم بإنشاء قاعدة بيانات جديدة للبدء</p>
              </div>
            ) : (
              <div className="space-y-2">
                {databases.map((db) => (
                  <div
                    key={db.name}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedDatabase === db.name
                        ? 'border-green-500 bg-green-50'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedDatabase(db.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{db.name}</h4>
                          {selectedDatabase === db.name && (
                            <Badge variant="default">مختارة</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>الحجم: {db.size}</span>
                          <span>الجداول: {db.tables}</span>
                          <span>المالك: {db.owner}</span>
                        </div>
                        {db.lastBackup && (
                          <p className="text-xs text-gray-400 mt-1">
                            آخر نسخة احتياطية: {db.lastBackup}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            switchDatabase(db.name);
                          }}
                          disabled={isSwitchingDatabase || selectedDatabase === db.name}
                        >
                          {isSwitchingDatabase ? 'جاري التبديل...' : 'تبديل'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (confirm(`هل أنت متأكد من حذف قاعدة البيانات "${db.name}"؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه وسيتم فقدان جميع البيانات!`)) {
                              deleteDatabase(db.name);
                            }
                          }}
                          className="text-red-600 hover:text-red-700"
                          disabled={selectedDatabase === db.name}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          ) : (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-center">
                <Database className="h-5 w-5 inline mr-2" />
                انقر على "عرض القائمة" لعرض قواعد البيانات المتاحة
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* النسخ الاحتياطية */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileDown className="h-5 w-5" />
              النسخ الاحتياطية
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowBackupDialog(true)}
                disabled={!selectedConnection}
              >
                <Download className="h-4 w-4 mr-2" />
                نسخة احتياطية
              </Button>
              <Button
                variant="secondary"
                onClick={() => setShowRestoreConfirm(true)}
                disabled={!selectedBackup || !selectedConnection}
              >
                <Upload className="h-4 w-4 mr-2" />
                استعادة
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedConnectionData && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800">
                <Shield className="h-4 w-4 inline mr-1" />
                الاتصال المختار: <strong>{selectedConnectionData.name}</strong>
              </p>
            </div>
          )}

          {backups.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileDown className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>لا توجد نسخ احتياطية</p>
              <p className="text-sm">قم بإنشاء نسخة احتياطية للبدء</p>
            </div>
          ) : (
            <div className="space-y-2">
              {backups.map((backup) => (
                <div
                  key={backup.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedBackup === backup.id
                      ? 'border-green-500 bg-green-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedBackup(backup.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{backup.name}</h4>
                        <Badge 
                          variant={backup.status === 'completed' ? 'default' : 
                                  backup.status === 'failed' ? 'destructive' : 'secondary'}
                        >
                          {backup.status === 'completed' ? 'مكتمل' :
                           backup.status === 'failed' ? 'فاشل' : 'قيد المعالجة'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        {backup.connection.name} • {backup.fileSize}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                        <span>
                          <Clock className="h-3 w-3 inline mr-1" />
                          {new Date(backup.createdAt).toLocaleDateString('ar-SA', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </span>
                        {backup.createdBy && (
                          <span>بواسطة: {backup.createdBy}</span>
                        )}
                      </div>
                      {backup.description && (
                        <p className="text-xs text-gray-500 mt-1">{backup.description}</p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
                            deleteBackup(backup.id);
                          }
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {selectedBackupData && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-800">
                <Check className="h-4 w-4 inline mr-1" />
                النسخة المختارة: <strong>{selectedBackupData.name}</strong>
              </p>
              <p className="text-xs text-green-600 mt-1">
                💡 تأكد من أخذ نسخة احتياطية من البيانات الحالية قبل الاستعادة
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نموذج الاتصال */}
      <Dialog open={showConnectionDialog} onOpenChange={setShowConnectionDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingConnection ? 'تعديل اتصال قاعدة البيانات' : 'إضافة اتصال قاعدة بيانات جديد'}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="conn-name">اسم الاتصال</Label>
                <Input
                  id="conn-name"
                  value={newConnection.name}
                  onChange={(e) => setNewConnection({...newConnection, name: e.target.value})}
                  placeholder="قاعدة البيانات الرئيسية"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="conn-host">الخادم</Label>
                <Input
                  id="conn-host"
                  value={newConnection.host}
                  onChange={(e) => setNewConnection({...newConnection, host: e.target.value})}
                  placeholder="localhost"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="conn-port">المنفذ</Label>
                <Input
                  id="conn-port"
                  type="number"
                  value={newConnection.port}
                  onChange={(e) => setNewConnection({...newConnection, port: parseInt(e.target.value) || 5432})}
                  placeholder="5432"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="conn-database">اسم قاعدة البيانات</Label>
                <Input
                  id="conn-database"
                  value={newConnection.database}
                  onChange={(e) => setNewConnection({...newConnection, database: e.target.value})}
                  placeholder="deviceflow_db"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="conn-username">اسم المستخدم</Label>
                <Input
                  id="conn-username"
                  value={newConnection.username}
                  onChange={(e) => setNewConnection({...newConnection, username: e.target.value})}
                  placeholder="deviceflow_user"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="conn-password">كلمة المرور</Label>
                <Input
                  id="conn-password"
                  type="password"
                  value={newConnection.password}
                  onChange={(e) => setNewConnection({...newConnection, password: e.target.value})}
                  placeholder={editingConnection ? "اتركها فارغة للاحتفاظ بالحالية" : "••••••••"}
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="conn-default"
                checked={newConnection.isDefault}
                onCheckedChange={(checked) => setNewConnection({...newConnection, isDefault: checked})}
              />
              <Label htmlFor="conn-default">جعل هذا الاتصال افتراضي</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConnectionDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={createOrUpdateConnection}>
              {editingConnection ? 'تحديث' : 'إضافة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نموذج إنشاء قاعدة البيانات */}
      <Dialog open={showDatabaseDialog} onOpenChange={setShowDatabaseDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>إنشاء قاعدة بيانات جديدة</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="db-name">اسم قاعدة البيانات</Label>
              <Input
                id="db-name"
                value={newDatabaseForm.name}
                onChange={(e) => setNewDatabaseForm({...newDatabaseForm, name: e.target.value})}
                placeholder="my_new_database"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="db-owner">المالك</Label>
              <Input
                id="db-owner"
                value={newDatabaseForm.owner}
                onChange={(e) => setNewDatabaseForm({...newDatabaseForm, owner: e.target.value})}
                placeholder="deviceflow_user"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="db-template">القالب</Label>
                <Select 
                  value={newDatabaseForm.template} 
                  onValueChange={(value) => setNewDatabaseForm({...newDatabaseForm, template: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="template0">template0</SelectItem>
                    <SelectItem value="template1">template1</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="db-encoding">الترميز</Label>
                <Select 
                  value={newDatabaseForm.encoding} 
                  onValueChange={(value) => setNewDatabaseForm({...newDatabaseForm, encoding: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UTF8">UTF8</SelectItem>
                    <SelectItem value="LATIN1">LATIN1</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {selectedConnectionData && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  <Database className="h-4 w-4 inline mr-1" />
                  سيتم إنشاء قاعدة البيانات في: <strong>{selectedConnectionData.name}</strong>
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDatabaseDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={createDatabase} disabled={isCreatingDatabase}>
              {isCreatingDatabase ? 'جاري الإنشاء...' : 'إنشاء'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نموذج النسخة الاحتياطية */}
      <Dialog open={showBackupDialog} onOpenChange={setShowBackupDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>إنشاء نسخة احتياطية</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="backup-name">اسم النسخة الاحتياطية</Label>
              <Input
                id="backup-name"
                value={backupForm.name}
                onChange={(e) => setBackupForm({...backupForm, name: e.target.value})}
                placeholder={`نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="backup-description">الوصف (اختياري)</Label>
              <Textarea
                id="backup-description"
                value={backupForm.description}
                onChange={(e) => setBackupForm({...backupForm, description: e.target.value})}
                placeholder="وصف مختصر للنسخة الاحتياطية..."
                rows={3}
              />
            </div>
            {selectedConnectionData && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  <Database className="h-4 w-4 inline mr-1" />
                  سيتم أخذ نسخة احتياطية من: <strong>{selectedConnectionData.name}</strong>
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBackupDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={createBackup} disabled={isCreatingBackup}>
              {isCreatingBackup ? 'جاري الإنشاء...' : 'إنشاء'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* تأكيد الاستعادة */}
      <AlertDialog open={showRestoreConfirm} onOpenChange={setShowRestoreConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              تأكيد الاستعادة
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>هل أنت متأكد من استعادة النسخة الاحتياطية؟</p>
              {selectedBackupData && selectedConnectionData && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-sm">
                  <p><strong>النسخة الاحتياطية:</strong> {selectedBackupData.name}</p>
                  <p><strong>قاعدة البيانات المستهدفة:</strong> {selectedConnectionData.name}</p>
                  <p className="text-orange-600 mt-2">
                    ⚠️ <strong>تحذير:</strong> سيتم استبدال جميع البيانات الحالية. 
                    هذا الإجراء لا يمكن التراجع عنه.
                  </p>
                </div>
              )}
              <p className="text-red-600 font-medium">
                ينصح بشدة بأخذ نسخة احتياطية من البيانات الحالية أولاً.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction 
              onClick={restoreBackup}
              disabled={isRestoring}
              className="bg-red-600 hover:bg-red-700"
            >
              {isRestoring ? 'جاري الاستعادة...' : 'نعم، استعادة'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
