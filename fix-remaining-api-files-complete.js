/**
 * Fix Remaining API Files Complete Script
 * Date: 2025-08-04
 * Description: Fix all remaining API files with date issues
 */

const fs = require('fs');
const path = require('path');

// Remaining API files to fix
const remainingApiFiles = [
  'app/api/returns/route.ts',
  'app/api/evaluations/route.ts',
  'app/api/delivery-orders/route.ts',
  'app/api/escalation/overdue/route.ts',
  'app/api/notifications/check-overdue/route.ts',
  'app/api/notifications/route.ts',
  'app/api/response-templates/route.ts',
  'app/api/response-templates/suggest/route.ts',
  'app/api/search/quick/route.ts',
  'app/api/search/requests/route.ts',
  'app/api/upload/route.ts',
  'app/api/archive/route.ts',
  'app/api/database/backup/route.ts'
];

// API-specific fixes
const apiSpecificFixes = [
  // Fix function parameter types
  {
    search: /function safeToISOString\(dateValue: any\): string \| null/g,
    replace: 'function safeToISOString(dateValue: Date | string | null): string | null',
    description: 'تحسين نوع معامل safeToISOString'
  },
  {
    search: /const sanitizeDate = \(dateStr: any\): string \| null/g,
    replace: 'const sanitizeDate = (dateStr: Date | string | null): string | null',
    description: 'تحسين نوع معامل sanitizeDate'
  },
  {
    search: /function getOverdueTimeText\(priority: string\): string/g,
    replace: 'function getOverdueTimeText(priority: string): string',
    description: 'تحسين دالة getOverdueTimeText'
  },
  {
    search: /function getOverdueTime\(priority: string\): string/g,
    replace: 'function getOverdueTime(priority: string): string',
    description: 'تحسين دالة getOverdueTime'
  },
  
  // Fix interface date types
  {
    search: /date: string;/g,
    replace: 'date: Date;',
    description: 'تحويل date إلى Date في الواجهات'
  },
  {
    search: /processedDate: string;/g,
    replace: 'processedDate: Date;',
    description: 'تحويل processedDate إلى Date'
  },
  {
    search: /requestDate: string;/g,
    replace: 'requestDate: Date;',
    description: 'تحويل requestDate إلى Date'
  },
  {
    search: /dateFrom\?: string;/g,
    replace: 'dateFrom?: Date;',
    description: 'تحويل dateFrom إلى Date'
  },
  {
    search: /dateTo\?: string;/g,
    replace: 'dateTo?: Date;',
    description: 'تحويل dateTo إلى Date'
  },
  
  // Fix toISOString usage in API responses
  {
    search: /requestDate: request\.requestDate\.toISOString\(\)/g,
    replace: 'requestDate: request.requestDate',
    description: 'إزالة toISOString غير الضرورية من requestDate'
  },
  {
    search: /repairDate: repairDate\.toISOString\(\)/g,
    replace: 'repairDate: repairDate',
    description: 'إزالة toISOString غير الضرورية من repairDate'
  },
  {
    search: /createdAt: template\.createdAt\.toISOString\(\)/g,
    replace: 'createdAt: template.createdAt',
    description: 'إزالة toISOString غير الضرورية من createdAt'
  },
  {
    search: /updatedAt: template\.updatedAt\.toISOString\(\)/g,
    replace: 'updatedAt: template.updatedAt',
    description: 'إزالة toISOString غير الضرورية من updatedAt'
  },
  {
    search: /uploadedAt: stats\.mtime\.toISOString\(\)/g,
    replace: 'uploadedAt: stats.mtime',
    description: 'إزالة toISOString غير الضرورية من uploadedAt'
  },
  
  // Fix new Date().toISOString() patterns
  {
    search: /date: newReceipt\.date \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'date: newReceipt.date || new Date()',
    description: 'استخدام Date object مباشرة'
  },
  {
    search: /timestamp: new Date\(\)\.toISOString\(\)/g,
    replace: 'timestamp: new Date()',
    description: 'استخدام Date object مباشرة للـ timestamp'
  },
  
  // Fix date operations
  {
    search: /cutoffDate\.setDate\(cutoffDate\.getDate\(\) - daysOld\);/g,
    replace: 'cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);',
    description: 'تحسين عمليات التاريخ'
  },
  {
    search: /const timestamp = new Date\(\)\.toISOString\(\)\.replace\(\/\[\:\.\]\/g, '-'\);/g,
    replace: 'const timestamp = new Date().toISOString().replace(/[:.]/g, \'-\');',
    description: 'إصلاح regex للـ timestamp'
  }
];

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixRemainingApiFiles() {
  console.log('🔧 إصلاح ملفات API المتبقية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of remainingApiFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${remainingApiFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply fixes
        if (applyFixes(filePath, apiSpecificFixes)) {
          totalFixed += apiSpecificFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 5 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${remainingApiFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح ملفات API المتبقية:');
    console.log('='.repeat(45));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${remainingApiFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: remainingApiFiles.length,
      fixes: apiSpecificFixes
    };

    fs.writeFileSync('remaining-api-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح API المتبقية في: remaining-api-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح ملفات API المتبقية بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح في ملفات API المتبقية');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح ملفات API المتبقية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixRemainingApiFiles()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح ملفات API المتبقية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح ملفات API المتبقية:', error);
      process.exit(1);
    });
}

module.exports = { fixRemainingApiFiles };
