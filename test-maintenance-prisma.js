/**
 * Updated Test for Maintenance Order with Prisma Relations
 */

console.log('🧪 اختبار إنشاء أمر صيانة مع علاقات Prisma...');

const testOrderData = {
  employeeName: 'اختبار المطور',
  maintenanceEmployeeName: 'فني الصيانة',
  date: new Date().toISOString(),
  source: 'warehouse',
  status: 'wip',
  notes: 'اختبار إنشاء أمر صيانة بعد إصلاح Prisma',
  items: [
    {
      deviceId: 'test-device-1',
      model: 'جهاز اختبار 1',
      fault: 'خطأ اختباري 1',
      notes: 'ملاحظات الاختبار 1'
    },
    {
      deviceId: 'test-device-2', 
      model: 'جهاز اختبار 2',
      fault: 'خطأ اختباري 2',
      notes: 'ملاحظات الاختبار 2'
    }
  ]
};

async function testMaintenanceOrderWithPrisma() {
  try {
    console.log('📤 إرسال طلب إنشاء أمر صيانة...');
    console.log('📋 بيانات الاختبار:', JSON.stringify(testOrderData, null, 2));
    
    const response = await fetch('http://localhost:3000/api/maintenance-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testOrderData)
    });

    console.log('📥 استجابة الخادم:', response.status, response.statusText);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم إنشاء الأمر بنجاح!');
      console.log('📄 تفاصيل الأمر:', JSON.stringify(result, null, 2));
      
      if (result.items && Array.isArray(result.items)) {
        console.log(`📦 تم إنشاء ${result.items.length} عنصر في الأمر`);
      }
    } else {
      const errorData = await response.json().catch(() => ({}));
      console.log('❌ فشل في إنشاء الأمر');
      console.log('📄 تفاصيل الخطأ:', JSON.stringify(errorData, null, 2));
    }
    
  } catch (error) {
    console.log('❌ خطأ في الشبكة أو الاتصال:', error.message);
  }
}

testMaintenanceOrderWithPrisma();