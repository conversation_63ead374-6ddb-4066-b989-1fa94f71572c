/**
 * Fix Date Fields Script
 * Date: 2025-08-04
 * Description: Convert string date fields to proper DateTime before schema changes
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixDateFields() {
  console.log('🔧 إصلاح حقول التواريخ...\n');

  try {
    // 1. Fix DeliveryOrder dates
    console.log('1️⃣ إصلاح تواريخ أوامر التسليم...');
    const deliveryOrders = await prisma.$queryRaw`
      SELECT id, date FROM "DeliveryOrder" 
      WHERE date IS NOT NULL
    `;

    for (const order of deliveryOrders) {
      try {
        let dateValue = order.date;
        
        // If it's already a Date object, skip
        if (dateValue instanceof Date) {
          continue;
        }
        
        // If it's a string, try to parse it
        if (typeof dateValue === 'string') {
          const parsedDate = new Date(dateValue);
          if (!isNaN(parsedDate.getTime())) {
            await prisma.$executeRaw`
              UPDATE "DeliveryOrder" 
              SET date = ${parsedDate}::timestamp 
              WHERE id = ${order.id}
            `;
          } else {
            // Use current date as fallback
            await prisma.$executeRaw`
              UPDATE "DeliveryOrder" 
              SET date = NOW() 
              WHERE id = ${order.id}
            `;
          }
        }
      } catch (error) {
        console.warn(`خطأ في إصلاح تاريخ أمر التسليم ${order.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم إصلاح ${deliveryOrders.length} أمر تسليم\n`);

    // 2. Fix Return dates
    console.log('2️⃣ إصلاح تواريخ المرتجعات...');
    const returns = await prisma.$queryRaw`
      SELECT id, date FROM "Return" 
      WHERE date IS NOT NULL
    `;

    for (const returnItem of returns) {
      try {
        let dateValue = returnItem.date;
        
        // If it's already a Date object, skip
        if (dateValue instanceof Date) {
          continue;
        }
        
        // If it's a string, try to parse it
        if (typeof dateValue === 'string') {
          const parsedDate = new Date(dateValue);
          if (!isNaN(parsedDate.getTime())) {
            await prisma.$executeRaw`
              UPDATE "Return" 
              SET date = ${parsedDate}::timestamp 
              WHERE id = ${returnItem.id}
            `;
          } else {
            // Use current date as fallback
            await prisma.$executeRaw`
              UPDATE "Return" 
              SET date = NOW() 
              WHERE id = ${returnItem.id}
            `;
          }
        }
      } catch (error) {
        console.warn(`خطأ في إصلاح تاريخ المرتجع ${returnItem.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم إصلاح ${returns.length} مرتجع\n`);

    // 3. Update RequestComment fields
    console.log('3️⃣ تحديث حقول تعليقات الطلبات...');
    const comments = await prisma.requestComment.findMany({
      where: {
        OR: [
          { userName: null },
          { userRole: null }
        ]
      }
    });

    for (const comment of comments) {
      try {
        await prisma.requestComment.update({
          where: { id: comment.id },
          data: {
            userName: comment.userName || 'مستخدم غير معروف',
            userRole: comment.userRole || 'user'
          }
        });
      } catch (error) {
        console.warn(`خطأ في تحديث التعليق ${comment.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم تحديث ${comments.length} تعليق\n`);

    console.log('🎉 تم الانتهاء من إصلاح حقول التواريخ والبيانات!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run fix
if (require.main === module) {
  fixDateFields()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح البيانات');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح البيانات:', error);
      process.exit(1);
    });
}

module.exports = { fixDateFields };
