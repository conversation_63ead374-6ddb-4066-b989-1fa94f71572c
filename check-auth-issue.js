const { PrismaClient } = require('@prisma/client');

async function checkAuth() {
  const prisma = new PrismaClient();
  try {
    console.log('🔍 فحص قاعدة البيانات والمصادقة...');
    
    // فحص الاتصال بقاعدة البيانات
    await prisma.$connect();
    console.log('✅ الاتصال بقاعدة البيانات ناجح');
    
    // فحص وجود المستخدم admin
    const adminUser = await prisma.user.findFirst({
      where: { 
        OR: [
          { username: 'admin' },
          { email: '<EMAIL>' }
        ]
      }
    });
    
    if (adminUser) {
      console.log('✅ المستخدم admin موجود:', {
        id: adminUser.id,
        username: adminUser.username,
        email: adminUser.email,
        role: adminUser.role,
        status: adminUser.status
      });
    } else {
      console.log('❌ المستخدم admin غير موجود - سيتم إنشاؤه تلقائياً');
      
      // إنشاء المستخدم admin
      const newAdmin = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: { role: 'admin', username: 'admin' },
        create: {
          username: 'admin',
          email: '<EMAIL>', 
          name: 'System Administrator',
          role: 'admin',
          status: 'Active'
        }
      });
      console.log('✅ تم إنشاء المستخدم admin:', {
        id: newAdmin.id,
        username: newAdmin.username,
        email: newAdmin.email,
        role: newAdmin.role
      });
    }
    
    // اختبار تفكيك التوكن
    const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    const decoded = Buffer.from(token, 'base64').toString();
    console.log('🔑 Token المفكك:', decoded);
    
    // اختبار طلب مصادقة
    console.log('\n🧪 اختبار APIs مع مصادقة...');
    const apiClient = {
      get: async (url) => {
        const headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        };
        
        const response = await fetch(`http://localhost:9005${url}`, {
          method: 'GET',
          headers
        });
        
        return { status: response.status, ok: response.ok, response };
      }
    };
    
    // اختبار APIs الفاشلة
    const failingApis = [
      '/api/suppliers?view=simple',
      '/api/device-models?view=simple', 
      '/api/settings?view=simple'
    ];
    
    for (const api of failingApis) {
      console.log(`\n🔍 اختبار ${api}...`);
      try {
        const result = await apiClient.get(api);
        console.log(`   Status: ${result.status} (${result.ok ? 'نجح' : 'فشل'})`);
        
        if (!result.ok) {
          const errorText = await result.response.text();
          console.log(`   الخطأ: ${errorText}`);
        } else {
          const data = await result.response.json();
          console.log(`   البيانات: ${Array.isArray(data) ? data.length + ' عنصر' : 'object'}`);
        }
      } catch (error) {
        console.log(`   ❌ خطأ في الشبكة: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkAuth();
