// اختبار إنشاء فاتورة مبيعات
const testSaleCreation = async () => {
  try {
    console.log('🧪 بدء اختبار إنشاء فاتورة...');
    
    const saleData = {
      clientName: 'عميل اختبار',
      opNumber: '', // ترك فارغ لاختبار الإنشاء التلقائي
      warehouseName: 'المخزن الرئيسي', 
      notes: 'فاتورة اختبار',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [
        {
          deviceId: 'TEST001',
          model: 'Samsung Galaxy S23',
          price: 1500,
          condition: 'جديد'
        },
        {
          deviceId: 'TEST002', 
          model: 'iPhone 15',
          price: 2000,
          condition: 'جديد'
        }
      ]
    };

    console.log('📤 إرسال البيانات:', JSON.stringify(saleData, null, 2));

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // إضافة header للمصادقة إذا كان مطلوباً
        'Authorization': 'Bearer test-token' // قد تحتاج لتعديل هذا
      },
      body: JSON.stringify(saleData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ نجح إنشاء الفاتورة!');
      console.log('📋 تفاصيل الفاتورة:');
      console.log(`   رقم الفاتورة (SO): ${result.soNumber}`);
      console.log(`   رقم الأمر (OP): ${result.opNumber}`);
      console.log(`   العميل: ${result.clientName}`);
      console.log(`   التاريخ: ${new Date(result.date).toLocaleString('ar-SA')}`);
      console.log(`   عدد الأصناف: ${result.items ? result.items.length : 0}`);
      
      // اختبار إنشاء فاتورة أخرى للتحقق من تسلسل أرقام الأوامر
      console.log('\n🔄 اختبار فاتورة ثانية...');
      
      const secondSaleData = {
        ...saleData,
        clientName: 'عميل اختبار 2',
        items: [{
          deviceId: 'TEST003',
          model: 'Huawei P50',
          price: 1200,
          condition: 'جديد'
        }]
      };

      const secondResponse = await fetch('http://localhost:9005/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        body: JSON.stringify(secondSaleData)
      });

      const secondResult = await secondResponse.json();
      
      if (secondResponse.ok) {
        console.log('✅ نجح إنشاء الفاتورة الثانية!');
        console.log(`   رقم الفاتورة (SO): ${secondResult.soNumber}`);
        console.log(`   رقم الأمر (OP): ${secondResult.opNumber}`);
        
        // التحقق من تسلسل أرقام الأوامر
        const firstOpNumber = parseInt(result.opNumber);
        const secondOpNumber = parseInt(secondResult.opNumber);
        
        if (secondOpNumber === firstOpNumber + 1) {
          console.log('✅ أرقام الأوامر متسلسلة بشكل صحيح!');
        } else {
          console.log('⚠️  أرقام الأوامر غير متسلسلة كما هو متوقع');
          console.log(`   الأول: ${firstOpNumber}, الثاني: ${secondOpNumber}`);
        }
      } else {
        console.log('❌ فشل في إنشاء الفاتورة الثانية:', secondResult);
      }
      
    } else {
      console.log('❌ فشل في إنشاء الفاتورة:', result);
    }

  } catch (error) {
    console.log('❌ خطأ في الاختبار:', error.message);
    console.log('💡 تأكد من أن الخادم يعمل على http://localhost:9005');
  }
};

// تشغيل الاختبار
testSaleCreation();
