/**
 * أداة إصلاح مشاكل المصادقة 401 - Authentication Fix Tool
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixAuthenticationIssues() {
  console.log('🔧 أداة إصلاح مشاكل المصادقة 401...\n');
  
  try {
    // 1. فحص وإنشاء المستخدم admin إذا لزم الأمر
    console.log('1️⃣ فحص المستخدم admin...');
    
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { 
        role: 'admin', 
        username: 'admin',
        status: 'Active'
      },
      create: {
        username: 'admin',
        email: '<EMAIL>', 
        name: 'System Administrator',
        role: 'admin',
        status: 'Active'
      }
    });
    
    console.log(`✅ المستخدم admin: ID=${adminUser.id}, Role=${adminUser.role}`);
    
    // 2. اختبار تفكيك التوكن
    console.log('\n2️⃣ اختبار تفكيك التوكن...');
    
    const testTokens = [
      'dXNlcjphZG1pbjphZG1pbg==', // user:admin:admin
      btoa('user:admin:admin'),      // browser btoa
      Buffer.from('user:admin:admin').toString('base64') // node buffer
    ];
    
    testTokens.forEach((token, index) => {
      try {
        const decoded = Buffer.from(token, 'base64').toString();
        console.log(`  Token ${index + 1}: ${token} -> ${decoded}`);
      } catch (error) {
        console.log(`  ❌ Token ${index + 1}: فشل في فك الترميز`);
      }
    });
    
    // 3. اختبار APIs المتأثرة
    console.log('\n3️⃣ اختبار APIs المتأثرة...');
    
    const correctToken = 'dXNlcjphZG1pbjphZG1pbg==';
    const apiEndpoints = [
      '/api/suppliers?view=simple',
      '/api/device-models?view=simple',
      '/api/settings?view=simple'
    ];
    
    for (const endpoint of apiEndpoints) {
      console.log(`\n   🔍 ${endpoint}`);
      
      try {
        const response = await fetch(`http://localhost:9005${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${correctToken}`
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          const count = Array.isArray(data) ? data.length : 'object';
          console.log(`      ✅ نجح - البيانات: ${count}`);
        } else {
          const errorText = await response.text();
          console.log(`      ❌ فشل - Status: ${response.status}, Error: ${errorText}`);
        }
      } catch (error) {
        console.log(`      ❌ خطأ شبكة: ${error.message}`);
      }
    }
    
    // 4. إنشاء tokens جديدة للاختبار
    console.log('\n4️⃣ إنشاء tokens للاختبار...');
    
    const newTokens = {
      admin: Buffer.from('user:admin:admin').toString('base64'),
      manager: Buffer.from('user:manager:manager').toString('base64'),
      user: Buffer.from('user:user:user').toString('base64')
    };
    
    console.log('   Tokens للاستخدام:');
    Object.entries(newTokens).forEach(([role, token]) => {
      console.log(`   ${role}: Bearer ${token}`);
    });
    
    // 5. فحص حالة النظام
    console.log('\n5️⃣ فحص حالة النظام...');
    
    // فحص جداول أساسية
    const tables = [
      { name: 'User', model: prisma.user },
      { name: 'Device', model: prisma.device },
      { name: 'Supplier', model: prisma.supplier },
      { name: 'DeviceModel', model: prisma.deviceModel },
      { name: 'SystemSetting', model: prisma.systemSetting }
    ];
    
    for (const table of tables) {
      try {
        const count = await table.model.count();
        console.log(`   ${table.name}: ${count} سجل`);
      } catch (error) {
        console.log(`   ${table.name}: ❌ خطأ - ${error.message}`);
      }
    }
    
    // 6. إقتراحات الإصلاح
    console.log('\n🔧 إقتراحات الإصلاح:');
    console.log('================================================');
    console.log('1. تأكد من أن الخادم يعمل على http://localhost:9005');
    console.log('2. تأكد من أن التوكن مُرسل بشكل صحيح في الـ headers');
    console.log('3. تحقق من console الـ browser للأخطاء');
    console.log('4. جرب refresh الصفحة أو clear browser cache');
    console.log('5. تأكد من أن Next.js development server يعمل بشكل صحيح');
    
  } catch (error) {
    console.error('❌ خطأ في أداة الإصلاح:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل أداة الإصلاح
fixAuthenticationIssues();
