'use client';

import React from 'react';
import { SystemSettings } from '@/lib/types';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface DocumentHeaderProps {
  settings: SystemSettings;
  title?: string;
  subtitle?: string;
  language?: 'ar' | 'en' | 'both';
  showLogo?: boolean;
  className?: string;
}

export default function DocumentHeader({
  settings,
  title,
  subtitle,
  language = 'both',
  showLogo = true,
  className = ''
}: DocumentHeaderProps) {
  return (
    <div className={`document-header print-header ${className}`}>
      {/* الترويسة الرئيسية */}
      <div className="header-main flex items-start justify-between mb-6 pb-4 border-b-2 border-gray-300">
        {/* الشعار */}
        {showLogo && settings.logoUrl && (
          <div className="logo-container flex-shrink-0">
            <img 
              src={settings.logoUrl} 
              alt="Company Logo" 
              className="h-16 w-16 object-contain"
            />
          </div>
        )}

        {/* معلومات الشركة */}
        <div className="company-info flex-1 text-center">
          {/* أسماء الشركة */}
          <div className="company-names mb-2">
            {(language === 'ar' || language === 'both') && (
              <h1 className="text-xl font-bold text-gray-800 rtl">
                {settings.companyNameAr}
              </h1>
            )}
            {(language === 'en' || language === 'both') && (
              <h2 className={`text-lg font-semibold text-gray-600 ltr ${
                language === 'both' ? 'mt-1' : ''
              }`}>
                {settings.companyNameEn}
              </h2>
            )}
          </div>

          {/* العناوين */}
          <div className="addresses text-sm text-gray-600 mb-2">
            {(language === 'ar' || language === 'both') && (
              <p className="rtl">{settings.addressAr}</p>
            )}
            {(language === 'en' || language === 'both') && (
              <p className={`ltr ${language === 'both' ? 'mt-1' : ''}`}>
                {settings.addressEn}
              </p>
            )}
          </div>

          {/* معلومات الاتصال */}
          <div className="contact-info text-sm text-gray-600 flex justify-center gap-4 flex-wrap">
            {settings.phone && (
              <span className="flex items-center gap-1">
                📞 {settings.phone}
              </span>
            )}
            {settings.email && (
              <span className="flex items-center gap-1">
                ✉️ {settings.email}
              </span>
            )}
            {settings.website && (
              <span className="flex items-center gap-1">
                🌐 {settings.website}
              </span>
            )}
          </div>
        </div>

        {/* مساحة فارغة للتوازن */}
        <div className="flex-shrink-0 w-16"></div>
      </div>

      {/* عنوان التقرير */}
      {title && (
        <div className="report-title text-center mb-4">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">{title}</h2>
          {subtitle && (
            <p className="text-sm text-gray-600">{subtitle}</p>
          )}
        </div>
      )}
    </div>
  );
}

// مكون التذييل
interface DocumentFooterProps {
  settings: SystemSettings;
  language?: 'ar' | 'en' | 'both';
  showTimestamp?: boolean;
  className?: string;
}

export function DocumentFooter({
  settings,
  language = 'both',
  showTimestamp = true,
  className = ''
}: DocumentFooterProps) {
  const currentDate = new Date();
  const arabicDate = currentDate;
  const englishDate = currentDate;
  const time = currentDate.toLocaleTimeString('ar-EG', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <div className={`document-footer print-footer mt-8 pt-4 border-t-2 border-gray-300 ${className}`}>
      {/* نصوص التذييل */}
      <div className="footer-text text-center mb-3">
        {(language === 'ar' || language === 'both') && settings.footerTextAr && (
          <p className="text-sm text-gray-600 rtl mb-1">
            {settings.footerTextAr}
          </p>
        )}
        {(language === 'en' || language === 'both') && settings.footerTextEn && (
          <p className="text-sm text-gray-600 ltr">
            {settings.footerTextEn}
          </p>
        )}
      </div>

      {/* الطابع الزمني */}
      {showTimestamp && (
        <div className="timestamp text-center text-xs text-gray-500">
          <div className="flex justify-center gap-4 flex-wrap">
            {(language === 'ar' || language === 'both') && (
              <span className="rtl">
                تاريخ الطباعة: {arabicDate} - {time}
              </span>
            )}
            {(language === 'en' || language === 'both') && (
              <span className="ltr">
                Print Date: {englishDate} - {time}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Hook لجلب الإعدادات
export function useSystemSettings() {
  const [settings, setSettings] = React.useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        } else {
          throw new Error('فشل في جلب الإعدادات');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'خطأ غير معروف');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  return { settings, isLoading, error };
}
