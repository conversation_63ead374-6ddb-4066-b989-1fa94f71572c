import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// دالة لتنظيف البيانات من null bytes والأحرف غير الصالحة
function sanitizeString(str: string | null | undefined): string | null {
  if (!str) return null;
  if (typeof str !== 'string') return null;
  
  // إزالة null bytes وأحرف التحكم الأخرى
  let cleaned = str
    .replace(/\x00/g, '') // إزالة null bytes
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // إزالة أحرف التحكم
    .trim();
    
  return cleaned.length > 0 ? cleaned : null;
}

// دالة لتنظيف كائن البيانات
function sanitizeDeliveryOrderData(data: any) {
  return {
    ...data,
    deliveryOrderNumber: sanitizeString(data.deliveryOrderNumber),
    referenceNumber: sanitizeString(data.referenceNumber),
    warehouseName: sanitizeString(data.warehouseName),
    employeeName: sanitizeString(data.employeeName),
    notes: sanitizeString(data.notes),
    attachmentName: sanitizeString(data.attachmentName),
  };
}

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع أوامر التسليم مع include مختلف حسب النوع
    const includeOptions = view === 'simple' 
      ? { items: false } // لا نحتاج items في العرض البسيط
      : { items: true };

    const deliveryOrders = await prisma.deliveryOrder.findMany({
      include: includeOptions,
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(deliveryOrders);
  } catch (error) {
    console.error('Failed to fetch delivery orders:', error);
    return NextResponse.json({ error: 'Failed to fetch delivery orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.date || !newOrder.warehouseId) {
      return NextResponse.json(
        { error: 'Date and warehouse ID are required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // إنشاء رقم تسلسلي بنمط DELIV-
      let deliveryOrderNumber = newOrder.deliveryOrderNumber;

      if (!deliveryOrderNumber) {
        const existingOrders = await tx.deliveryOrder.findMany({
          select: { deliveryOrderNumber: true },
          orderBy: { id: 'desc' }
        });

        let maxNumber = 0;
        existingOrders.forEach(order => {
          const match = order.deliveryOrderNumber.match(/DELIV-(\d+)$/);
          if (match) {
            const num = parseInt(match[1]);
            if (!isNaN(num) && num > maxNumber) {
              maxNumber = num;
            }
          }
        });

        deliveryOrderNumber = `DELIV-${maxNumber + 1}`;
      } else {
        // التحقق من وجود الرقم المرسل
        const existingOrder = await tx.deliveryOrder.findUnique({
          where: { deliveryOrderNumber }
        });

        if (existingOrder) {
          // إذا كان الرقم موجوداً، نولد رقماً جديداً
          const existingOrders = await tx.deliveryOrder.findMany({
            select: { deliveryOrderNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxNumber = 0;
          existingOrders.forEach(order => {
            const match = order.deliveryOrderNumber.match(/DELIV-(\d+)$/);
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxNumber) {
                maxNumber = num;
              }
            }
          });

          deliveryOrderNumber = `DELIV-${maxNumber + 1}`;
        }
      }

      // الحصول على اسم المخزن إذا لم يكن موجوداً
      let warehouseName = newOrder.warehouseName;
      if (!warehouseName && newOrder.warehouseId) {
        const warehouse = await tx.warehouse.findUnique({
          where: { id: newOrder.warehouseId }
        });
        warehouseName = warehouse ? warehouse.name : 'مخزن غير محدد';
      }

      // تحويل التاريخ إلى Date object صحيح
      const orderDate = newOrder.date ? new Date(newOrder.date) : new Date();
      if (isNaN(orderDate.getTime())) {
        throw new Error('Invalid date format');
      }

      // إنشاء أمر التسليم باستخدام raw query لتجنب مشكلة null bytes
      const order = await tx.$queryRaw`
        INSERT INTO "DeliveryOrder" 
        ("deliveryOrderNumber", "referenceNumber", "date", "warehouseId", "warehouseName", "employeeName", "notes", "status", "attachmentName")
        VALUES 
        (${deliveryOrderNumber}, ${newOrder.referenceNumber || null}, ${orderDate}, ${newOrder.warehouseId}, ${warehouseName || 'مخزن غير محدد'}, ${newOrder.employeeName || ''}, ${newOrder.notes || null}, ${newOrder.status || 'completed'}, ${newOrder.attachmentName || null})
        RETURNING *
      `.then(result => Array.isArray(result) ? result[0] : result);

      // Update device statuses to "awaiting receipt in warehouse" first
      // The final status will be set when the devices are actually received in the warehouse
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          await tx.device.update({
            where: { id: item.deviceId },
            data: {
              status: 'بانتظار استلام في المخزن',
              warehouseId: newOrder.warehouseId
            }
          });

          // Create maintenance log entry with proper date handling
          await tx.maintenanceLog.create({
            data: {
              deviceId: sanitizeString(item.deviceId) || '',
              model: sanitizeString(item.model) || '',
              repairDate: orderDate,
              notes: sanitizeString(item.notes || item.fault || 'تم إنهاء الصيانة') || '',
              result: sanitizeString(item.result) || '',
              status: 'pending'
            }
          });
        }
      }

      
      // Create deliveryOrder items with sanitized data
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          const sanitizedItemData = {
            deliveryOrderId: order.id,
            deviceId: sanitizeString(item.deviceId || '') || '',
            model: sanitizeString(item.model || '') || '',
            result: sanitizeString(item.result || '') || '',
            fault: sanitizeString(item.fault || null),
            damage: sanitizeString(item.damage || null),
            notes: sanitizeString(item.notes || null)
          };

          await tx.deliveryOrderItem.create({
            data: sanitizedItemData
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: sanitizeString(`Created delivery order: ${deliveryOrderNumber}`) || `Created delivery order: ${deliveryOrderNumber}`
      });

      // Return order with items
      const orderWithItems = await tx.deliveryOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create delivery order:', error);

    if (error instanceof Error && error.message === 'Invalid date format') {
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create delivery order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: updatedOrder.id },
        include: { items: true }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Get existing items
      const oldItemsArray = existingOrder.items || [];

      // الحصول على اسم المخزن إذا لم يكن موجوداً
      let warehouseName = updatedOrder.warehouseName;
      if (!warehouseName && updatedOrder.warehouseId) {
        const warehouse = await tx.warehouse.findUnique({
          where: { id: updatedOrder.warehouseId }
        });
        warehouseName = warehouse ? warehouse.name : 'مخزن غير محدد';
      }

      // Update the delivery order
      const order = await tx.deliveryOrder.update({
        where: { id: updatedOrder.id },
        data: {
          deliveryOrderNumber: updatedOrder.deliveryOrderNumber,
          referenceNumber: updatedOrder.referenceNumber,
          date: updatedOrder.date,
          warehouseId: updatedOrder.warehouseId,
          warehouseName: warehouseName || existingOrder.warehouseName || 'مخزن غير محدد',
          employeeName: updatedOrder.employeeName,
          notes: updatedOrder.notes,
          status: updatedOrder.status,
          attachmentName: updatedOrder.attachmentName,
        }
      });

      // Update device statuses based on maintenance result
      if (updatedOrder.items && Array.isArray(updatedOrder.items)) {
        for (const item of updatedOrder.items) {
          let finalStatus;
          switch (item.result) {
            case 'Repaired':
              finalStatus = 'متاح للبيع';
              break;
            case 'Unrepairable-Defective':
              finalStatus = 'معيب';
              break;
            case 'Unrepairable-Damaged':
              finalStatus = 'تالف';
              break;
            default:
              finalStatus = 'متاح للبيع';
          }

          await tx.device.update({
            where: { id: item.deviceId },
            data: {
              status: finalStatus,
              warehouseId: updatedOrder.warehouseId
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: sanitizeString(`Updated delivery order: ${order.deliveryOrderNumber}`) || `Updated delivery order: ${order.deliveryOrderNumber}`
      });

      // Return order with items
      const orderWithItems = await tx.deliveryOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update delivery order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists and get its items
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: parseInt(id) },
        include: { items: true }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Get items to update device statuses back
      const items = existingOrder.items || [];

      // Update device statuses back to maintenance
      for (const item of items) {
        await tx.device.update({
          where: { id: item.deviceId },
          data: { status: 'قيد الإصلاح' }
        });
      }

      // Delete the delivery order
      await tx.deliveryOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: sanitizeString(`Deleted delivery order: ${existingOrder.deliveryOrderNumber}`) || `Deleted delivery order: ${existingOrder.deliveryOrderNumber}`
      });

      return { message: 'Delivery order deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete delivery order' }, { status: 500 });
  }
}