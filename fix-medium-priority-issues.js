/**
 * Fix Medium Priority Date Issues Script
 * Date: 2025-08-04
 * Description: Fix medium priority date type issues (261 issues)
 */

const fs = require('fs');
const path = require('path');

// Files with medium priority issues
const mediumPriorityFiles = [
  // Components
  'components/AttachmentsViewer.tsx',
  'components/database-management.tsx',
  'components/DeviceTrackingReport.tsx',
  'components/DocumentHeader.tsx',
  'components/evaluation-cleanup.tsx',
  'components/ReportPreview.tsx',
  'components/requests/AdvancedSearch.tsx',
  'components/requests/EscalationDashboard.tsx',
  'components/requests/RequestConversation.tsx',
  'components/requests/SmartTemplates.tsx',
  'components/ui/chart.tsx',
  'components/ui/print-export-buttons.tsx',
  
  // Context files
  'context/stocktake-store.tsx',
  
  // Lib files
  'lib/export-utils/canvas-pdf-enhanced.ts',
  'lib/export-utils/enhanced-html-export.ts',
  'lib/network.ts',
  'lib/notification-service.ts',
  'lib/print-templates/index.ts',
  'lib/search-service.ts',
  'lib/template-service.ts',
  
  // Track pages
  'app/(main)/track/DeviceDetailsSection.tsx',
  'app/(main)/track/DeviceHistoryTimeline.tsx',
  'app/(main)/track/DeviceOperationDetails.tsx',
  'app/(main)/track/DeviceTrackingFilters.tsx',
  'app/(main)/track/event-log/page.tsx',
  'app/(main)/track/simple-page.tsx',
  
  // Other main pages
  'app/(main)/audit-logs/page.tsx',
  'app/(main)/layout.tsx',
  'app/(main)/maintenance/MaintenanceMemo.tsx',
  
  // Additional API files
  'app/api/archive/route.ts',
  'app/api/database/backup/route.ts',
  'app/api/escalation/overdue/route.ts',
  'app/api/maintenance-receipts/route.ts',
  'app/api/notifications/check-overdue/route.ts',
  'app/api/notifications/route.ts',
  'app/api/response-templates/route.ts',
  'app/api/response-templates/suggest/route.ts',
  'app/api/search/quick/route.ts',
  'app/api/search/requests/route.ts'
];

// Medium priority fixes
const mediumFixes = [
  // toLocaleDateString fixes
  {
    search: /\.toLocaleDateString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString العربية'
  },
  {
    search: /\.toLocaleDateString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString السعودية'
  },
  {
    search: /\.toLocaleDateString\('en-US'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString الإنجليزية'
  },
  {
    search: /\.toLocaleString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleString العربية'
  },
  {
    search: /\.toLocaleString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleString السعودية'
  },
  
  // Manual date formatting fixes
  {
    search: /date\.getFullYear\(\)/g,
    replace: 'new Date(date).getFullYear()',
    description: 'تحسين getFullYear'
  },
  {
    search: /date\.getMonth\(\) \+ 1/g,
    replace: 'new Date(date).getMonth() + 1',
    description: 'تحسين getMonth'
  },
  {
    search: /date\.getDate\(\)/g,
    replace: 'new Date(date).getDate()',
    description: 'تحسين getDate'
  },
  {
    search: /date\.getHours\(\)/g,
    replace: 'new Date(date).getHours()',
    description: 'تحسين getHours'
  },
  {
    search: /date\.getMinutes\(\)/g,
    replace: 'new Date(date).getMinutes()',
    description: 'تحسين getMinutes'
  },
  
  // toISOString fixes for specific cases
  {
    search: /\.toISOString\(\)\.slice\(0, 10\)/g,
    replace: '.toISOString().slice(0, 10)',
    description: 'الاحتفاظ بـ slice للتواريخ'
  },
  {
    search: /\.toISOString\(\)\.slice\(0, 16\)/g,
    replace: '.toISOString().slice(0, 16)',
    description: 'الاحتفاظ بـ slice للتاريخ والوقت'
  },
  
  // String date type fixes in function parameters
  {
    search: /\(dateString: string\)/g,
    replace: '(date: Date | string)',
    description: 'تحسين نوع البيانات للتواريخ'
  },
  {
    search: /\(dateTimeString: string\)/g,
    replace: '(dateTime: Date | string)',
    description: 'تحسين نوع البيانات للتاريخ والوقت'
  }
];

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixMediumPriorityIssues() {
  console.log('🔧 إصلاح المشاكل متوسطة الأولوية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of mediumPriorityFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${mediumPriorityFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply fixes
        if (applyFixes(filePath, mediumFixes)) {
          totalFixed += mediumFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 10 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${mediumPriorityFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح المشاكل متوسطة الأولوية:');
    console.log('='.repeat(50));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${mediumPriorityFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 أول 10 ملفات معدلة:');
      filesModified.slice(0, 10).forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
      
      if (filesModified.length > 10) {
        console.log(`... و ${filesModified.length - 10} ملف آخر`);
      }
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: mediumPriorityFiles.length,
      fixes: mediumFixes
    };

    fs.writeFileSync('medium-priority-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير الإصلاحات في: medium-priority-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح المشاكل متوسطة الأولوية بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح المشاكل متوسطة الأولوية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixMediumPriorityIssues()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح المشاكل متوسطة الأولوية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح المشاكل متوسطة الأولوية:', error);
      process.exit(1);
    });
}

module.exports = { fixMediumPriorityIssues };
