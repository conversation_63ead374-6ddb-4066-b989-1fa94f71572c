<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء فاتورة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .item {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار إنشاء فاتورة مبيعات</h1>
        
        <form id="saleForm">
            <div class="form-group">
                <label for="clientName">اسم العميل:</label>
                <input type="text" id="clientName" name="clientName" value="عميل تجريبي" required>
            </div>
            
            <div class="form-group">
                <label for="opNumber">رقم الأمر (اختياري):</label>
                <input type="text" id="opNumber" name="opNumber" placeholder="اتركه فارغاً لإنشاء رقم تلقائي">
            </div>
            
            <div class="form-group">
                <label for="warehouseName">اسم المخزن:</label>
                <input type="text" id="warehouseName" name="warehouseName" value="المخزن الرئيسي">
            </div>
            
            <div class="form-group">
                <label for="notes">ملاحظات:</label>
                <textarea id="notes" name="notes" rows="3" placeholder="ملاحظات إضافية"></textarea>
            </div>
            
            <div class="form-group">
                <label for="warrantyPeriod">فترة الضمان:</label>
                <select id="warrantyPeriod" name="warrantyPeriod">
                    <option value="none">بدون ضمان</option>
                    <option value="3months">3 أشهر</option>
                    <option value="6months">6 أشهر</option>
                    <option value="1year">سنة واحدة</option>
                </select>
            </div>
            
            <h3>الأصناف:</h3>
            <div id="itemsContainer">
                <div class="item">
                    <div class="item-header">
                        <h4>الصنف 1</h4>
                        <button type="button" onclick="removeItem(this)">حذف</button>
                    </div>
                    <div class="form-group">
                        <label>رقم الجهاز:</label>
                        <input type="text" name="deviceId" value="DEV001">
                    </div>
                    <div class="form-group">
                        <label>الموديل:</label>
                        <input type="text" name="model" value="Samsung Galaxy S23" required>
                    </div>
                    <div class="form-group">
                        <label>السعر:</label>
                        <input type="number" name="price" value="1500" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label>الحالة:</label>
                        <select name="condition">
                            <option value="جديد">جديد</option>
                            <option value="مستعمل">مستعمل</option>
                            <option value="مجدد">مجدد</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <button type="button" onclick="addItem()">إضافة صنف</button>
            <button type="submit">إنشاء الفاتورة</button>
            <button type="button" onclick="testMultipleSales()">اختبار عدة فواتير</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        let itemCounter = 1;

        function addItem() {
            itemCounter++;
            const container = document.getElementById('itemsContainer');
            const itemDiv = document.createElement('div');
            itemDiv.className = 'item';
            itemDiv.innerHTML = `
                <div class="item-header">
                    <h4>الصنف ${itemCounter}</h4>
                    <button type="button" onclick="removeItem(this)">حذف</button>
                </div>
                <div class="form-group">
                    <label>رقم الجهاز:</label>
                    <input type="text" name="deviceId" value="DEV${String(itemCounter).padStart(3, '0')}">
                </div>
                <div class="form-group">
                    <label>الموديل:</label>
                    <input type="text" name="model" value="iPhone 15" required>
                </div>
                <div class="form-group">
                    <label>السعر:</label>
                    <input type="number" name="price" value="2000" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>الحالة:</label>
                    <select name="condition">
                        <option value="جديد">جديد</option>
                        <option value="مستعمل">مستعمل</option>
                        <option value="مجدد">مجدد</option>
                    </select>
                </div>
            `;
            container.appendChild(itemDiv);
        }

        function removeItem(button) {
            button.closest('.item').remove();
        }

        function collectFormData() {
            const form = document.getElementById('saleForm');
            const formData = new FormData(form);
            
            // جمع بيانات الأصناف
            const items = [];
            const itemDivs = document.querySelectorAll('.item');
            
            itemDivs.forEach((itemDiv, index) => {
                const deviceId = itemDiv.querySelector('input[name="deviceId"]').value;
                const model = itemDiv.querySelector('input[name="model"]').value;
                const price = itemDiv.querySelector('input[name="price"]').value;
                const condition = itemDiv.querySelector('select[name="condition"]').value;
                
                items.push({
                    deviceId: deviceId || '',
                    model: model || '',
                    price: parseFloat(price) || 0,
                    condition: condition || 'جديد'
                });
            });

            return {
                clientName: formData.get('clientName'),
                opNumber: formData.get('opNumber') || '',
                warehouseName: formData.get('warehouseName'),
                notes: formData.get('notes'),
                warrantyPeriod: formData.get('warrantyPeriod'),
                date: new Date().toISOString(),
                items: items
            };
        }

        async function createSale(saleData) {
            try {
                const response = await fetch('http://localhost:9005/api/sales', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(saleData)
                });

                const result = await response.json();
                return { success: response.ok, data: result };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(result, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            
            if (isSuccess && result.data) {
                resultDiv.textContent = `نجح إنشاء الفاتورة!
رقم الفاتورة (SO): ${result.data.soNumber}
رقم الأمر (OP): ${result.data.opNumber}
العميل: ${result.data.clientName}
التاريخ: ${new Date(result.data.date).toLocaleString('ar-SA')}
عدد الأصناف: ${result.data.items ? result.data.items.length : 0}`;
            } else {
                resultDiv.textContent = `خطأ: ${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        document.getElementById('saleForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const saleData = collectFormData();
            console.log('بيانات الفاتورة:', saleData);
            
            const result = await createSale(saleData);
            displayResult(result, result.success);
        });

        async function testMultipleSales() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result';
            resultDiv.textContent = 'جاري إنشاء عدة فواتير للاختبار...';

            const results = [];
            
            for (let i = 1; i <= 5; i++) {
                const saleData = {
                    clientName: `عميل تجريبي ${i}`,
                    opNumber: '', // ترك فارغ لإنشاء رقم تلقائي
                    warehouseName: 'المخزن الرئيسي',
                    notes: `فاتورة اختبار رقم ${i}`,
                    warrantyPeriod: 'none',
                    date: new Date().toISOString(),
                    items: [{
                        deviceId: `TEST${i}`,
                        model: `جهاز اختبار ${i}`,
                        price: 100 * i,
                        condition: 'جديد'
                    }]
                };

                const result = await createSale(saleData);
                results.push({
                    index: i,
                    success: result.success,
                    soNumber: result.success ? result.data.soNumber : 'فشل',
                    opNumber: result.success ? result.data.opNumber : 'فشل',
                    error: result.success ? null : result.error || result.data
                });

                // انتظار قصير بين الطلبات
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // عرض النتائج
            let resultText = 'نتائج الاختبار:\n\n';
            results.forEach(result => {
                resultText += `الفاتورة ${result.index}: `;
                if (result.success) {
                    resultText += `✅ نجح - SO: ${result.soNumber}, OP: ${result.opNumber}\n`;
                } else {
                    resultText += `❌ فشل - خطأ: ${result.error}\n`;
                }
            });

            resultDiv.className = 'result success';
            resultDiv.textContent = resultText;
        }
    </script>
</body>
</html>
