/**
 * Test 403 Fix Script
 * Tests all API endpoints to verify 403 errors are resolved
 */

const endpoints = [
  '/api/users',
  '/api/devices',
  '/api/warehouses',
  '/api/clients',
  '/api/returns',
  '/api/supply',
  '/api/sales',
  '/api/suppliers',
  '/api/evaluations',
  '/api/device-models',
  '/api/maintenance-receipts',
  '/api/delivery-orders',
  '/api/maintenance-orders',
  '/api/internal-messages',
  '/api/settings'
];

async function testEndpoint(endpoint) {
  try {
    const response = await fetch(`http://localhost:3000${endpoint}`);
    console.log(`${endpoint}: ${response.status} ${response.statusText}`);
    return response.status !== 403;
  } catch (error) {
    console.log(`${endpoint}: ERROR - ${error.message}`);
    return false;
  }
}

async function testAllEndpoints() {
  console.log('🧪 اختبار جميع نقاط النهاية...');
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
  }
}

// تشغيل الاختبار
testAllEndpoints();