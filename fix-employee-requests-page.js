/**
 * إصلاح مشاكل صفحة طلبات الموظفين - Employee Requests Page Fix
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixEmployeeRequestsPage() {
  console.log('🔧 إصلاح مشاكل صفحة طلبات الموظفين...\n');
  
  try {
    // 1. إنشاء جدول request_comments المفقود
    console.log('1️⃣ إنشاء جدول request_comments...');
    
    try {
      await prisma.$executeRawUnsafe(`
        CREATE TABLE IF NOT EXISTS "request_comments" (
          "id" SERIAL NOT NULL,
          "requestId" INTEGER NOT NULL,
          "userId" INTEGER NOT NULL,
          "comment" TEXT NOT NULL,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "isInternal" BOOLEAN NOT NULL DEFAULT false,
          "attachments" JSONB,
          
          CONSTRAINT "request_comments_pkey" PRIMARY KEY ("id")
        );
      `);
      console.log('   ✅ تم إنشاء جدول request_comments');
    } catch (error) {
      console.log('   ⚠️ جدول request_comments موجود بالفعل أو خطأ:', error.message);
    }
    
    // 2. إنشاء جدول notifications المفقود
    console.log('\n2️⃣ إنشاء جدول notifications...');
    
    try {
      await prisma.$executeRawUnsafe(`
        CREATE TABLE IF NOT EXISTS "notifications" (
          "id" SERIAL NOT NULL,
          "userId" INTEGER NOT NULL,
          "requestId" INTEGER,
          "type" TEXT NOT NULL,
          "title" TEXT NOT NULL,
          "message" TEXT NOT NULL,
          "read" BOOLEAN NOT NULL DEFAULT false,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "readAt" TIMESTAMP(3),
          "actionUrl" TEXT,
          "priority" TEXT NOT NULL DEFAULT 'normal',
          
          CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
        );
      `);
      console.log('   ✅ تم إنشاء جدول notifications');
    } catch (error) {
      console.log('   ⚠️ جدول notifications موجود بالفعل أو خطأ:', error.message);
    }
    
    // 3. إنشاء جداول إضافية قد تكون مطلوبة
    console.log('\n3️⃣ إنشاء جداول إضافية...');
    
    const additionalTables = [
      {
        name: 'response_templates',
        sql: `
          CREATE TABLE IF NOT EXISTS "response_templates" (
            "id" SERIAL NOT NULL,
            "name" TEXT NOT NULL,
            "category" TEXT NOT NULL,
            "title" TEXT NOT NULL,
            "content" TEXT NOT NULL,
            "isSystem" BOOLEAN NOT NULL DEFAULT false,
            "isActive" BOOLEAN NOT NULL DEFAULT true,
            "createdBy" INTEGER,
            "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            CONSTRAINT "response_templates_pkey" PRIMARY KEY ("id")
          );
        `
      },
      {
        name: 'request_attachments',
        sql: `
          CREATE TABLE IF NOT EXISTS "request_attachments" (
            "id" SERIAL NOT NULL,
            "requestId" INTEGER NOT NULL,
            "fileName" TEXT NOT NULL,
            "fileType" TEXT NOT NULL,
            "fileSize" INTEGER NOT NULL,
            "filePath" TEXT NOT NULL,
            "fileUrl" TEXT,
            "mimeType" TEXT NOT NULL,
            "uploadedBy" INTEGER NOT NULL,
            "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "isDeleted" BOOLEAN NOT NULL DEFAULT false,
            "deletedAt" TIMESTAMP(3),
            
            CONSTRAINT "request_attachments_pkey" PRIMARY KEY ("id")
          );
        `
      }
    ];
    
    for (const table of additionalTables) {
      try {
        await prisma.$executeRawUnsafe(table.sql);
        console.log(`   ✅ تم إنشاء جدول ${table.name}`);
      } catch (error) {
        console.log(`   ⚠️ جدول ${table.name} موجود بالفعل أو خطأ: ${error.message}`);
      }
    }
    
    // 4. إضافة فهارس للأداء
    console.log('\n4️⃣ إضافة فهارس للأداء...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS "request_comments_requestId_idx" ON "request_comments"("requestId");',
      'CREATE INDEX IF NOT EXISTS "request_comments_userId_idx" ON "request_comments"("userId");',
      'CREATE INDEX IF NOT EXISTS "notifications_userId_idx" ON "notifications"("userId");',
      'CREATE INDEX IF NOT EXISTS "notifications_read_idx" ON "notifications"("read");',
      'CREATE INDEX IF NOT EXISTS "notifications_requestId_idx" ON "notifications"("requestId");'
    ];
    
    for (const indexSql of indexes) {
      try {
        await prisma.$executeRawUnsafe(indexSql);
        console.log('   ✅ تم إضافة فهرس');
      } catch (error) {
        console.log('   ⚠️ الفهرس موجود بالفعل أو خطأ:', error.message.substring(0, 50));
      }
    }
    
    // 5. إضافة علاقات خارجية إذا أمكن
    console.log('\n5️⃣ إضافة علاقات خارجية...');
    
    const foreignKeys = [
      'ALTER TABLE "request_comments" ADD CONSTRAINT "request_comments_requestId_fkey" FOREIGN KEY ("requestId") REFERENCES "employee_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;',
      'ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;'
    ];
    
    for (const fkSql of foreignKeys) {
      try {
        await prisma.$executeRawUnsafe(fkSql);
        console.log('   ✅ تم إضافة علاقة خارجية');
      } catch (error) {
        console.log('   ⚠️ العلاقة موجودة بالفعل أو خطأ:', error.message.substring(0, 50));
      }
    }
    
    // 6. اختبار الجداول الجديدة
    console.log('\n6️⃣ اختبار الجداول الجديدة...');
    
    try {
      // اختبار request_comments
      const commentsCount = await prisma.$queryRaw`SELECT COUNT(*) as count FROM "request_comments"`;
      console.log(`   ✅ جدول request_comments: ${commentsCount[0].count} تعليق`);
      
      // اختبار notifications
      const notificationsCount = await prisma.$queryRaw`SELECT COUNT(*) as count FROM "notifications"`;
      console.log(`   ✅ جدول notifications: ${notificationsCount[0].count} إشعار`);
      
    } catch (error) {
      console.log('   ❌ خطأ في اختبار الجداول:', error.message);
    }
    
    // 7. اختبار APIs المتعلقة
    console.log('\n7️⃣ اختبار APIs المتعلقة...');
    
    const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    
    // اختبار notifications API
    try {
      const response = await fetch('http://localhost:9005/api/notifications?userId=1', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ notifications API: ${Array.isArray(data) ? data.length : 'object'} إشعار`);
      } else {
        console.log(`   ❌ notifications API: Status ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ في notifications API: ${error.message}`);
    }
    
    // 8. إنشاء بيانات تجريبية
    console.log('\n8️⃣ إنشاء بيانات تجريبية...');
    
    try {
      // إنشاء تعليق تجريبي
      const testComment = await prisma.$queryRaw`
        INSERT INTO "request_comments" ("requestId", "userId", "comment", "isInternal")
        VALUES (1, 1, 'تعليق تجريبي للاختبار', false)
        ON CONFLICT DO NOTHING
        RETURNING id;
      `;
      
      if (testComment.length > 0) {
        console.log(`   ✅ تم إنشاء تعليق تجريبي: ID=${testComment[0].id}`);
        
        // حذف التعليق التجريبي
        await prisma.$queryRaw`DELETE FROM "request_comments" WHERE id = ${testComment[0].id}`;
        console.log('   ✅ تم حذف التعليق التجريبي');
      }
      
    } catch (error) {
      console.log('   ⚠️ خطأ في إنشاء البيانات التجريبية:', error.message);
    }
    
    console.log('\n🎉 تم إصلاح مشاكل صفحة طلبات الموظفين!');
    console.log('\n📋 ملخص الإصلاحات:');
    console.log('✅ إنشاء جدول request_comments');
    console.log('✅ إنشاء جدول notifications');
    console.log('✅ إنشاء جداول إضافية (response_templates, request_attachments)');
    console.log('✅ إضافة فهارس للأداء');
    console.log('✅ إضافة علاقات خارجية');
    
    console.log('\n🔄 الخطوات التالية:');
    console.log('1. إنشاء أو تحقق من وجود API route للـ notifications');
    console.log('2. أعد تشغيل خادم Next.js');
    console.log('3. جرب زيارة صفحة طلبات الموظفين');
    
  } catch (error) {
    console.error('❌ خطأ عام في إصلاح صفحة طلبات الموظفين:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الإصلاح
fixEmployeeRequestsPage();
