/**
 * Fix Remaining API Files Script
 * Date: 2025-08-04
 * Description: Fix date type issues in remaining API files
 */

const fs = require('fs');
const path = require('path');

// API files to fix
const apiFiles = [
  'app/api/sales/route.ts',
  'app/api/supply/route.ts',
  'app/api/maintenance-orders/route.ts',
  'app/api/delivery-orders/route.ts',
  'app/api/evaluations/route.ts',
  'app/api/maintenance-logs/route.ts',
  'app/api/devices/route.ts',
  'app/api/supply-batch/route.ts',
  'app/api/upload/route.ts'
];

// Common API fixes
const apiFixes = [
  {
    search: /new Date\(\)\.toISOString\(\)/g,
    replace: 'new Date()',
    description: 'إزالة toISOString() غير الضرورية'
  },
  {
    search: /date: new Date\(\)\.toISOString\(\)/g,
    replace: 'date: new Date()',
    description: 'استخدام Date object مباشرة للتاريخ'
  },
  {
    search: /createdAt: new Date\(\)\.toISOString\(\)/g,
    replace: 'createdAt: new Date()',
    description: 'استخدام Date object مباشرة للـ createdAt'
  },
  {
    search: /requestDate: new Date\(\)\.toISOString\(\)/g,
    replace: 'requestDate: new Date()',
    description: 'استخدام Date object مباشرة للـ requestDate'
  },
  {
    search: /processedDate: new Date\(\)\.toISOString\(\)/g,
    replace: 'processedDate: new Date()',
    description: 'استخدام Date object مباشرة للـ processedDate'
  },
  {
    search: /sentDate: new Date\(\)\.toISOString\(\)/g,
    replace: 'sentDate: new Date()',
    description: 'استخدام Date object مباشرة للـ sentDate'
  },
  {
    search: /repairDate: new Date\(\)\.toISOString\(\)/g,
    replace: 'repairDate: new Date()',
    description: 'استخدام Date object مباشرة للـ repairDate'
  },
  {
    search: /acknowledgedDate: new Date\(\)\.toISOString\(\)/g,
    replace: 'acknowledgedDate: new Date()',
    description: 'استخدام Date object مباشرة للـ acknowledgedDate'
  },
  {
    search: /timestamp: new Date\(\)\.toISOString\(\)/g,
    replace: 'timestamp: new Date()',
    description: 'استخدام Date object مباشرة للـ timestamp'
  },
  {
    search: /uploadedAt: new Date\(\)\.toISOString\(\)/g,
    replace: 'uploadedAt: new Date()',
    description: 'استخدام Date object مباشرة للـ uploadedAt'
  }
];

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${filePath}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${filePath}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    console.log(`   📁 نسخة احتياطية: ${backupPath}\n`);
    
    return true;
  }

  return false;
}

async function fixRemainingApiFiles() {
  console.log('🔧 إصلاح ملفات API المتبقية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    apiFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file);
      
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply fixes
        if (applyFixes(filePath, apiFixes)) {
          totalFixed += apiFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
    });

    // Generate summary
    console.log('📊 ملخص إصلاح ملفات API:');
    console.log('='.repeat(35));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 ملفات API المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      fixes: apiFixes
    };

    fs.writeFileSync('api-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح API في: api-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح ملفات API بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح في ملفات API');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح ملفات API:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixRemainingApiFiles()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح ملفات API');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح ملفات API:', error);
      process.exit(1);
    });
}

module.exports = { fixRemainingApiFiles };
