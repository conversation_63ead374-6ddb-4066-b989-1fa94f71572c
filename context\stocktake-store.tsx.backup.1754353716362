'use client';

import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { Stocktake, StocktakeHistory, StocktakeDraft, User, StocktakeScope, StocktakeResult, StocktakeItem, StocktakeV1, ActivityLog } from '@/lib/types';
import { useStore } from './store'; // Access to global state like devices
import { useToast } from '@/hooks/use-toast';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// Mock data for new V2 stocktake system
const initialStocktakeHistory: StocktakeHistory[] = [];
const initialDrafts: StocktakeDraft[] = [];

type StocktakeStoreContextType = {
  history: StocktakeHistory[];
  drafts: StocktakeDraft[];
  activeStocktake: Stocktake | null;
  loading: boolean;
  error: string | null;
  
  createStocktake: (params: { 
    scope: StocktakeScope; 
    notes?: string; 
    userId: number; 
    orderNumber?: string;
    responsibleUser?: string;
    scheduledDate?: string;
    predefinedSerials?: string[];
  }) => Promise<Stocktake | null>;
  resumeStocktake: (draftId: string) => Promise<void>;
  completeStocktake: (stocktakeId: string) => Promise<void>;
  
  loadDrafts: () => void;
  saveDraft: (stocktakeId: string) => Promise<void>;
  deleteDraft: (draftId: string) => void;
  
  loadHistory: () => void;
  
  processInputs: (serialNumbers: string[]) => Promise<void>;
};

const StocktakeContext = createContext<StocktakeStoreContextType | undefined>(
  undefined,
);

export function StocktakeStoreProvider({ children }: { children: ReactNode }) {
  const { devices, users, warehouses, addActivity } = useStore();
  const { toast } = useToast();
  const [history, setHistory] = useState<StocktakeHistory[]>(initialStocktakeHistory);
  const [drafts, setDrafts] = useState<StocktakeDraft[]>(initialDrafts);
  const [activeStocktake, setActiveStocktake] = useState<Stocktake | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const createStocktake = useCallback(async (params: { 
    scope: StocktakeScope; 
    notes?: string; 
    userId: number;
    orderNumber?: string;
    responsibleUser?: string;
    scheduledDate?: string;
    predefinedSerials?: string[];
  }): Promise<Stocktake | null> => {
    setLoading(true);
    setError(null);
    try {
      const user = users.find(u => u.id === params.userId);
      const newStocktake: Stocktake = {
        id: `STK-${Date.now()}`,
        scope: params.scope,
        notes: params.notes,
        userId: params.userId,
        userName: user?.name || 'Unknown',
        startTime: new Date(),
        endTime: null,
        status: 'active',
        processedSerialNumbers: params.predefinedSerials || [],
        orderNumber: params.orderNumber,
        responsibleUser: params.responsibleUser,
        scheduledDate: params.scheduledDate,
        result: {
          matching: [],
          missing: [],
          extra: [],
          soldButFound: [],
          inMaintenance: [],
        },
      };
      
      await new Promise(res => setTimeout(res, 500));
      
      setActiveStocktake(newStocktake);
      if (addActivity) {
        addActivity({
            type: 'stocktake_started',
            description: `بدء عملية جرد جديدة رقم ${newStocktake.id}`,
        } as Omit<ActivityLog, 'id' | 'date' | 'username'>);
      }
      return newStocktake;
    } catch (e) {
      setError('Failed to create stocktake.');
      return null;
    } finally {
      setLoading(false);
    }
  }, [users, addActivity]);

  const processInputs = useCallback(async (serialNumbers: string[]) => {
    if (!activeStocktake) {
      setError("No active stocktake to process inputs for.");
      return;
    }
    setLoading(true);

    const { validSerials, duplicates, invalidLengthSerials } = serialNumbers.reduce(
      (acc, serial) => {
        const trimmedSerial = serial.trim();
        if (trimmedSerial.length > 0) {
            if (trimmedSerial.length !== 15) {
              acc.invalidLengthSerials.add(trimmedSerial);
            } else {
              if (acc.seen.has(trimmedSerial)) {
                acc.duplicates.add(trimmedSerial);
              }
              acc.seen.add(trimmedSerial);
              acc.validSerials.add(trimmedSerial);
            }
        }
        return acc;
      },
      { validSerials: new Set<string>(), duplicates: new Set<string>(), invalidLengthSerials: new Set<string>(), seen: new Set<string>() }
    );
    
    if (invalidLengthSerials.size > 0) {
        toast({
            title: "أرقام تسلسلية غير صالحة",
            description: `تم تجاهل ${invalidLengthSerials.size} رقم تسلسلي لأنها لا تتكون من 15 رقمًا.`,
            variant: "destructive"
        });
    }

    const allProcessedSerials = new Set([...activeStocktake.processedSerialNumbers, ...Array.from(validSerials)]);

    const expectedDevices = devices.filter(device => {
        const inWarehouse = activeStocktake.scope.warehouseIds.length === 0 || activeStocktake.scope.warehouseIds.includes(device.warehouseId ?? -1);
        const inModel = activeStocktake.scope.deviceModelIds.length === 0 || activeStocktake.scope.deviceModelIds.includes(parseInt(device.model.replace(/\D/g, '')));
        const isMaintenance = device.status.includes('صيانة');
        
        if (activeStocktake.scope.includeMaintenance) {
            return (inWarehouse || isMaintenance) && inModel;
        }
        return inWarehouse && inModel && !isMaintenance;
    });
    
    const expectedDeviceSerials = new Set(expectedDevices.map(d => d.id));

    const newResult: StocktakeResult = {
      matching: [],
      missing: [],
      extra: [],
      soldButFound: [],
      inMaintenance: [],
    };

    expectedDevices.forEach(device => {
      if (!allProcessedSerials.has(device.id)) {
        newResult.missing.push({ deviceId: device.id, model: device.model, status: device.status, warehouseName: warehouses.find(w => w.id === device.warehouseId)?.name });
      }
    });

    allProcessedSerials.forEach(serial => {
      const device = devices.find(d => d.id === serial);
      if (device) {
        if (device.status === 'مباع') {
          newResult.soldButFound.push({ deviceId: serial, model: device.model, status: device.status, warehouseName: warehouses.find(w => w.id === device.warehouseId)?.name });
        } else if (['قيد الإصلاح', 'تحتاج صيانة'].includes(device.status)) {
          newResult.inMaintenance.push({ deviceId: serial, model: device.model, status: device.status, warehouseName: warehouses.find(w => w.id === device.warehouseId)?.name });
        } else if (expectedDeviceSerials.has(serial)) {
          newResult.matching.push({ deviceId: serial, model: device.model, status: device.status, warehouseName: warehouses.find(w => w.id === device.warehouseId)?.name });
        } else {
          newResult.extra.push({ deviceId: serial, model: device.model, status: device.status, warehouseName: warehouses.find(w => w.id === device.warehouseId)?.name, note: 'موجود في موقع غير متوقع' });
        }
      } else {
        newResult.extra.push({ deviceId: serial, model: 'جهاز غير مسجل', status: 'غير معروف', note: 'غير مسجل بالنظام' });
      }
    });

    setActiveStocktake(prev => prev ? {
      ...prev,
      processedSerialNumbers: Array.from(allProcessedSerials),
      result: newResult,
    } : null);

    setLoading(false);
  }, [activeStocktake, devices, warehouses, toast]);

  const saveDraft = useCallback(async (stocktakeId: string) => {
    if (!activeStocktake || activeStocktake.id !== stocktakeId) return;
    setLoading(true);
    const user = users.find(u => u.id === activeStocktake.userId);
    const draft: StocktakeDraft = {
      ...activeStocktake,
      lastSavedAt: new Date(),
    };
    setDrafts(prev => {
      const existing = prev.find(d => d.id === draft.id);
      if (existing) {
        return prev.map(d => d.id === draft.id ? draft : d);
      }
      return [...prev, draft];
    });
    setActiveStocktake(null);
    if(addActivity) {
        addActivity({
            type: 'stocktake_draft_saved',
            description: `حفظ مسودة الجرد رقم ${stocktakeId}`,
        } as Omit<ActivityLog, 'id' | 'date' | 'username'>);
    }
    setLoading(false);
  }, [activeStocktake, users, addActivity]);

  const completeStocktake = useCallback(async (stocktakeId: string) => {
    if (!activeStocktake || activeStocktake.id !== stocktakeId) return;
    setLoading(true);
    const user = users.find(u => u.id === activeStocktake.userId);
    const historyEntry: StocktakeHistory = {
      id: activeStocktake.id,
      completedAt: new Date(),
      userId: activeStocktake.userId,
      userName: activeStocktake.userName,
      scope: activeStocktake.scope,
      summary: {
        expected: activeStocktake.result?.expectedCount || 0,
        matched: activeStocktake.result?.matching.length || 0,
        missing: activeStocktake.result?.missing.length || 0,
        extra: activeStocktake.result?.extra.length || 0,
        misplaced: activeStocktake.result?.extra.filter(d => d.note !== 'غير مسجل بالنظام').length || 0,
        sold: activeStocktake.result?.soldButFound.length || 0,
        inMaintenance: activeStocktake.result?.inMaintenance.length || 0
      },
      result: activeStocktake.result,
    };
    setHistory(prev => [historyEntry, ...prev]);
    setDrafts(prev => prev.filter(d => d.id !== stocktakeId));
    setActiveStocktake(null);
    if(addActivity) {
        addActivity({
            type: 'stocktake_completed',
            description: `إكمال عملية الجرد رقم ${stocktakeId}`,
        } as Omit<ActivityLog, 'id' | 'date' | 'username'>);
    }
    setLoading(false);
  }, [activeStocktake, users, addActivity]);

  const resumeStocktake = useCallback(async (draftId: string) => {
    setLoading(true);
    const draft = drafts.find(d => d.id === draftId);
    if (draft) {
      setActiveStocktake(draft);
      setDrafts(prev => prev.filter(d => d.id !== draftId));
    } else {
      setError("Draft not found.");
    }
    setLoading(false);
  }, [drafts]);

  const deleteDraft = useCallback((draftId: string) => {
    setDrafts(prev => prev.filter(d => d.id !== draftId));
  }, []);

  const loadDrafts = useCallback(() => {
    setDrafts(initialDrafts);
  }, []);

  const loadHistory = useCallback(() => {
    setHistory(initialStocktakeHistory);
  }, []);
  
  const value = {
    history,
    drafts,
    activeStocktake,
    loading,
    error,
    createStocktake,
    resumeStocktake,
    completeStocktake,
    loadDrafts,
    saveDraft,
    deleteDraft,
    loadHistory,
    processInputs,
  };

  return (
    <StocktakeContext.Provider value={value as StocktakeStoreContextType}>
      {children}
    </StocktakeContext.Provider>
  );
}

export function useStocktakeStore() {
  const context = useContext(StocktakeContext);
  if (context === undefined) {
    throw new Error(
      'useStocktakeStore must be used within a StocktakeStoreProvider',
    );
  }
  return context;
}
