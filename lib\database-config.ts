/**
 * إعدادات قاعدة البيانات المحسنة للتعامل مع العمليات الكبيرة
 */

// إعدادات المعاملات
export const TRANSACTION_CONFIG = {
  // المعاملات العادية
  DEFAULT: {
    maxWait: 10000, // 10 ثوان
    timeout: 30000, // 30 ثانية
  },
  
  // المعاملات الطويلة (للعمليات الكبيرة)
  LONG: {
    maxWait: 30000, // 30 ثانية
    timeout: 300000, // 5 دقائق
  },
  
  // المعاملات الكبيرة جداً (للعمليات الضخمة)
  BULK: {
    maxWait: 60000, // دقيقة واحدة
    timeout: 600000, // 10 دقائق
  }
};

// إعدادات الدفعات
export const BATCH_CONFIG = {
  // حجم الدفعة الافتراضي للأجهزة
  DEVICE_BATCH_SIZE: 100,
  
  // حجم الدفعة للعمليات الصغيرة
  SMALL_BATCH_SIZE: 50,
  
  // حجم الدفعة للعمليات الكبيرة
  LARGE_BATCH_SIZE: 200,
  
  // الحد الأدنى لاستخدام المعالجة بالدفعات
  MIN_ITEMS_FOR_BATCH: 100,
};

// إعدادات الأداء
export const PERFORMANCE_CONFIG = {
  // الحد الأقصى للاستعلامات المتزامنة
  MAX_CONCURRENT_QUERIES: 10,
  
  // مهلة الاستعلام الافتراضية
  DEFAULT_QUERY_TIMEOUT: 30000, // 30 ثانية
  
  // مهلة الاستعلام للعمليات الكبيرة
  BULK_QUERY_TIMEOUT: 120000, // دقيقتان
  
  // حجم pool الاتصالات
  CONNECTION_POOL_SIZE: 20,
};

// إعدادات المراقبة
export const MONITORING_CONFIG = {
  // الحد الأدنى لتسجيل الاستعلامات البطيئة (بالميلي ثانية)
  SLOW_QUERY_THRESHOLD: 1000,
  
  // تفعيل تسجيل الاستعلامات في بيئة التطوير
  ENABLE_QUERY_LOGGING: process.env.NODE_ENV === 'development',
  
  // تفعيل مراقبة الأداء
  ENABLE_PERFORMANCE_MONITORING: true,
};

/**
 * دالة للحصول على إعدادات المعاملة المناسبة حسب حجم البيانات
 */
export function getTransactionConfig(itemCount: number) {
  if (itemCount < 50) {
    return TRANSACTION_CONFIG.DEFAULT;
  } else if (itemCount < 500) {
    return TRANSACTION_CONFIG.LONG;
  } else {
    return TRANSACTION_CONFIG.BULK;
  }
}

/**
 * دالة للحصول على حجم الدفعة المناسب حسب حجم البيانات
 */
export function getBatchSize(itemCount: number): number {
  if (itemCount < 100) {
    return BATCH_CONFIG.SMALL_BATCH_SIZE;
  } else if (itemCount < 1000) {
    return BATCH_CONFIG.DEVICE_BATCH_SIZE;
  } else {
    return BATCH_CONFIG.LARGE_BATCH_SIZE;
  }
}

/**
 * دالة للتحقق من ضرورة استخدام المعالجة بالدفعات
 */
export function shouldUseBatchProcessing(itemCount: number): boolean {
  return itemCount >= BATCH_CONFIG.MIN_ITEMS_FOR_BATCH;
}

/**
 * دالة لحساب عدد الدفعات المطلوبة
 */
export function calculateBatchCount(itemCount: number, batchSize?: number): number {
  const actualBatchSize = batchSize || getBatchSize(itemCount);
  return Math.ceil(itemCount / actualBatchSize);
}

/**
 * دالة لتقسيم المصفوفة إلى دفعات
 */
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * دالة لتسجيل معلومات الأداء
 */
export function logPerformanceInfo(operation: string, itemCount: number, duration: number) {
  if (MONITORING_CONFIG.ENABLE_PERFORMANCE_MONITORING) {
    console.log(`Performance Info: ${operation}`, {
      itemCount,
      duration: `${duration}ms`,
      itemsPerSecond: Math.round((itemCount / duration) * 1000),
      timestamp: new Date(),
    });
  }
}

/**
 * دالة لإنشاء رسالة تقدم للمستخدم
 */
export function createProgressMessage(
  currentBatch: number, 
  totalBatches: number, 
  operation: string = 'معالجة'
): string {
  const percentage = Math.round((currentBatch / totalBatches) * 100);
  return `${operation} الدفعة ${currentBatch} من ${totalBatches} (${percentage}%)`;
}
