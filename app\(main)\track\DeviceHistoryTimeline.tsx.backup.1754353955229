'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Package,
  Wrench,
  ShoppingCart,
  Undo2,
  ClipboardCheck,
  Shuffle,
  User,
  PackageCheck,
  Replace,
  Calendar,
  Filter,
  Search,
  ChevronDown,
  ChevronUp,
  Clock,
  FileText,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface TimelineEvent {
  icon: React.ReactNode;
  title: string;
  description: string;
  date: Date;
  color: string;
  user?: string;
  formattedDate?: string; // يبقى string للعرض // يبقى string للعرض
  type: string;
  details?: any;
}

interface DeviceHistoryTimelineProps {
  events: TimelineEvent[];
  searchedImei: string;
  deviceModel: string;
}

// دالة مساعدة لتنسيق التاريخ بالعربية
function formatArabicDate(date: Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// دالة للحصول على نوع الحدث
function getEventType(title: string): string {
  if (title.includes('توريد')) return 'supply';
  if (title.includes('فحص') || title.includes('تقييم')) return 'evaluation';
  if (title.includes('صيانة') || title.includes('إصلاح')) return 'maintenance';
  if (title.includes('تحويل')) return 'transfer';
  if (title.includes('بيع')) return 'sale';
  if (title.includes('إرجاع')) return 'return';
  if (title.includes('بديل') || title.includes('استبدال')) return 'replacement';
  if (title.includes('استلام')) return 'receipt';
  return 'default';
}

// دالة للحصول على لون النوع
function getTypeColor(type: string): string {
  switch (type) {
    case 'supply': return 'bg-cyan-100 text-cyan-800 border-cyan-200';
    case 'evaluation': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    case 'maintenance': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'transfer': return 'bg-gray-100 text-gray-800 border-gray-200';
    case 'sale': return 'bg-green-100 text-green-800 border-green-200';
    case 'return': return 'bg-red-100 text-red-800 border-red-200';
    case 'replacement': return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'receipt': return 'bg-purple-100 text-purple-800 border-purple-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

// دالة للحصول على اسم النوع بالعربية
function getTypeNameArabic(type: string): string {
  switch (type) {
    case 'supply': return 'توريد';
    case 'evaluation': return 'فحص وتقييم';
    case 'maintenance': return 'صيانة';
    case 'transfer': return 'نقل مخزني';
    case 'sale': return 'بيع';
    case 'return': return 'إرجاع';
    case 'replacement': return 'استبدال';
    case 'receipt': return 'استلام';
    default: return 'عام';
  }
}

export default function DeviceHistoryTimeline({ 
  events, 
  searchedImei, 
  deviceModel 
}: DeviceHistoryTimelineProps) {
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [expandedEvents, setExpandedEvents] = useState<Set<number>>(new Set());

  // تصفية وترتيب الأحداث
  const filteredEvents = events
    .filter(event => {
      const matchesType = filterType === 'all' || getEventType(event.title) === filterType;
      const matchesSearch = searchTerm === '' || 
        event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (event.user && event.user.toLowerCase().includes(searchTerm.toLowerCase()));
      return matchesType && matchesSearch;
    })
    .sort((a, b) => {
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });

  // تبديل توسيع الحدث
  const toggleEventExpansion = (index: number) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedEvents(newExpanded);
  };

  // الحصول على إحصائيات الأحداث
  const eventStats = events.reduce((acc, event) => {
    const type = getEventType(event.title);
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Card className="device-history-timeline">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-600" />
              السجل التاريخي الشامل
            </CardTitle>
            <p className="text-gray-600 text-sm mt-1">
              جميع العمليات التي مر بها الجهاز مرتبة زمنياً - إجمالي {events.length} عملية
            </p>
          </div>
          
          {/* إحصائيات سريعة */}
          <div className="flex flex-wrap gap-2">
            {Object.entries(eventStats).map(([type, count]) => (
              <Badge key={type} className={`${getTypeColor(type)} border text-xs`}>
                {getTypeNameArabic(type)}: {count}
              </Badge>
            ))}
          </div>
        </div>

        {/* أدوات التصفية والبحث */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="البحث في الأحداث..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </div>
          
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="تصفية حسب النوع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الأنواع</SelectItem>
              <SelectItem value="supply">توريد</SelectItem>
              <SelectItem value="evaluation">فحص وتقييم</SelectItem>
              <SelectItem value="maintenance">صيانة</SelectItem>
              <SelectItem value="transfer">نقل مخزني</SelectItem>
              <SelectItem value="sale">بيع</SelectItem>
              <SelectItem value="return">إرجاع</SelectItem>
              <SelectItem value="replacement">استبدال</SelectItem>
              <SelectItem value="receipt">استلام</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
            className="flex items-center gap-2"
          >
            <Calendar className="h-4 w-4" />
            {sortOrder === 'desc' ? 'الأحدث أولاً' : 'الأقدم أولاً'}
            {sortOrder === 'desc' ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {filteredEvents.length > 0 ? (
          <div className="space-y-4">
            {filteredEvents.map((event, index) => {
              const eventType = getEventType(event.title);
              const isExpanded = expandedEvents.has(index);
              
              return (
                <div
                  key={index}
                  className={`timeline-event-card border rounded-lg p-4 transition-all duration-200 hover:shadow-md ${
                    isExpanded ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
                  }`}
                >
                  <div className="flex items-start gap-4">
                    {/* أيقونة الحدث */}
                    <div className={`flex h-12 w-12 items-center justify-center rounded-full ${event.color} shadow-sm flex-shrink-0`}>
                      {event.icon}
                    </div>

                    {/* محتوى الحدث */}
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                        <div className="flex items-center gap-3">
                          <h4 className="font-bold text-lg text-gray-800">{event.title}</h4>
                          <Badge className={`${getTypeColor(eventType)} border text-xs`}>
                            {getTypeNameArabic(eventType)}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <time className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                            {formatArabicDate(event.date)}
                          </time>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleEventExpansion(index)}
                            className="h-8 w-8 p-0"
                          >
                            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                          </Button>
                        </div>
                      </div>

                      {/* وصف الحدث */}
                      <p className="text-gray-600 leading-relaxed mb-3">
                        {event.description}
                      </p>

                      {/* معلومات المستخدم */}
                      {event.user && (
                        <div className="flex items-center gap-2 mb-3">
                          <div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full text-sm text-gray-600">
                            <User className="h-3 w-3" />
                            <span>بواسطة: {event.user}</span>
                          </div>
                        </div>
                      )}

                      {/* تفاصيل إضافية عند التوسيع */}
                      {isExpanded && (
                        <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                          <h5 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            تفاصيل إضافية
                          </h5>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">نوع العملية:</span>
                              <span className="mr-2 text-gray-600">{getTypeNameArabic(eventType)}</span>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">التاريخ والوقت:</span>
                              <span className="mr-2 text-gray-600">{formatArabicDate(event.date)}</span>
                            </div>
                            {event.user && (
                              <div>
                                <span className="font-medium text-gray-700">المسؤول:</span>
                                <span className="mr-2 text-gray-600">{event.user}</span>
                              </div>
                            )}
                            <div>
                              <span className="font-medium text-gray-700">الرقم التسلسلي:</span>
                              <span className="mr-2 text-gray-600 font-mono text-xs">{searchedImei}</span>
                            </div>
                          </div>
                          
                          {/* تفاصيل خاصة بنوع العملية */}
                          {event.details && (
                            <div className="mt-3 p-3 bg-gray-50 rounded border">
                              <h6 className="font-medium text-gray-700 mb-2">معلومات تفصيلية:</h6>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                {/* تفاصيل التوريد */}
                                {event.details.supplyOrderId && (
                                  <>
                                    <div><span className="font-medium text-gray-700">أمر التوريد:</span> <span className="text-gray-600">{event.details.supplyOrderId}</span></div>
                                    <div><span className="font-medium text-gray-700">المورد:</span> <span className="text-gray-600">{event.details.supplierName}</span></div>
                                    {event.details.purchasePrice && <div><span className="font-medium text-gray-700">سعر الشراء:</span> <span className="text-gray-600">{event.details.purchasePrice}</span></div>}
                                  </>
                                )}
                                
                                {/* تفاصيل التقييم */}
                                {event.details.finalGrade && (
                                  <>
                                    <div><span className="font-medium text-gray-700">التقييم النهائي:</span> <span className="text-gray-600">{event.details.finalGrade}</span></div>
                                    <div><span className="font-medium text-gray-700">الخارجي:</span> <span className="text-gray-600">{event.details.externalGrade}</span></div>
                                    <div><span className="font-medium text-gray-700">الشاشة:</span> <span className="text-gray-600">{event.details.screenGrade}</span></div>
                                    <div><span className="font-medium text-gray-700">الشبكة:</span> <span className="text-gray-600">{event.details.networkGrade}</span></div>
                                  </>
                                )}
                                
                                {/* تفاصيل الصيانة */}
                                {event.details.orderNumber && (
                                  <>
                                    <div><span className="font-medium text-gray-700">رقم الأمر:</span> <span className="text-gray-600">{event.details.orderNumber}</span></div>
                                    {event.details.fault && <div><span className="font-medium text-gray-700">العطل:</span> <span className="text-gray-600">{event.details.fault}</span></div>}
                                    {event.details.priority && <div><span className="font-medium text-gray-700">الأولوية:</span> <span className="text-gray-600">{event.details.priority}</span></div>}
                                    {event.details.expectedCost && <div><span className="font-medium text-gray-700">التكلفة المتوقعة:</span> <span className="text-gray-600">{event.details.expectedCost}</span></div>}
                                  </>
                                )}
                                
                                {/* تفاصيل استلام الصيانة */}
                                {event.details.receiptNumber && (
                                  <>
                                    <div><span className="font-medium text-gray-700">أمر الاستلام:</span> <span className="text-gray-600">{event.details.receiptNumber}</span></div>
                                    <div><span className="font-medium text-gray-700">النتيجة:</span> <span className="text-gray-600">{event.details.resultText}</span></div>
                                    {event.details.repairCost && <div><span className="font-medium text-gray-700">تكلفة الإصلاح:</span> <span className="text-gray-600">{event.details.repairCost}</span></div>}
                                    {event.details.duration && <div><span className="font-medium text-gray-700">مدة الإصلاح:</span> <span className="text-gray-600">{event.details.duration}</span></div>}
                                  </>
                                )}
                                
                                {/* تفاصيل التحويل المخزني */}
                                {event.details.transferNumber && (
                                  <>
                                    <div><span className="font-medium text-gray-700">أمر التحويل:</span> <span className="text-gray-600">{event.details.transferNumber}</span></div>
                                    <div><span className="font-medium text-gray-700">من:</span> <span className="text-gray-600">{event.details.fromWarehouse}</span></div>
                                    <div><span className="font-medium text-gray-700">إلى:</span> <span className="text-gray-600">{event.details.toWarehouse}</span></div>
                                    {event.details.urgency && <div><span className="font-medium text-gray-700">الأولوية:</span> <span className="text-gray-600">{event.details.urgency}</span></div>}
                                  </>
                                )}
                                
                                {/* تفاصيل البيع */}
                                {event.details.soNumber && (
                                  <>
                                    <div><span className="font-medium text-gray-700">فاتورة البيع:</span> <span className="text-gray-600">{event.details.soNumber}</span></div>
                                    <div><span className="font-medium text-gray-700">العميل:</span> <span className="text-gray-600">{event.details.clientName}</span></div>
                                    {event.details.salePrice && <div><span className="font-medium text-gray-700">سعر البيع:</span> <span className="text-gray-600">{event.details.salePrice}</span></div>}
                                    {event.details.profit && <div><span className="font-medium text-gray-700">الربح:</span> <span className="text-gray-600">{event.details.profit}</span></div>}
                                    <div><span className="font-medium text-gray-700">الضمان:</span> <span className="text-gray-600">{event.details.warrantyText}</span></div>
                                  </>
                                )}
                                
                                {/* تفاصيل الإرجاع */}
                                {event.details.returnOrderNumber && (
                                  <>
                                    <div><span className="font-medium text-gray-700">أمر الإرجاع:</span> <span className="text-gray-600">{event.details.returnOrderNumber}</span></div>
                                    <div><span className="font-medium text-gray-700">سبب الإرجاع:</span> <span className="text-gray-600">{event.details.returnReason}</span></div>
                                    {event.details.daysBetweenSaleAndReturn && <div><span className="font-medium text-gray-700">أيام منذ البيع:</span> <span className="text-gray-600">{event.details.daysBetweenSaleAndReturn}</span></div>}
                                    {event.details.refundAmount && <div><span className="font-medium text-gray-700">قيمة الاسترداد:</span> <span className="text-gray-600">{event.details.refundAmount}</span></div>}
                                  </>
                                )}

                                {/* معلومات عامة */}
                                {event.details.notes && <div className="md:col-span-2"><span className="font-medium text-gray-700">ملاحظات:</span> <span className="text-gray-600">{event.details.notes}</span></div>}
                                {event.details.attachments && <div className="md:col-span-2"><span className="font-medium text-gray-700">مرفقات:</span> <span className="text-gray-600">{event.details.attachments}</span></div>}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">لا توجد أحداث مطابقة</h3>
            <p className="text-gray-500">
              {searchTerm || filterType !== 'all' 
                ? 'جرب تغيير معايير البحث أو التصفية'
                : 'لا يوجد سجل تاريخ مسجل لهذا الجهاز'
              }
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
