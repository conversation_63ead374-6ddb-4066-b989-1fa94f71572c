/**
 * Find Undefined Variables Script
 * Date: 2025-08-04
 * Description: Find undefined variables like dateTimeString, dateString, etc.
 */

const fs = require('fs');
const path = require('path');

// Variables to search for that might be undefined
const undefinedVariables = [
  'dateTimeString',
  'dateString', 
  'year',
  'month',
  'day'
];

function searchInFile(filePath) {
  if (!fs.existsSync(filePath)) return [];
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    undefinedVariables.forEach(variable => {
      // Check if variable is used but not defined in the same function
      if (line.includes(variable) && !line.includes(`const ${variable}`) && !line.includes(`let ${variable}`) && !line.includes(`var ${variable}`)) {
        // Skip comments
        if (line.trim().startsWith('//') || line.trim().startsWith('*')) return;
        
        issues.push({
          file: filePath,
          line: lineNumber,
          variable: variable,
          content: line.trim()
        });
      }
    });
  });
  
  return issues;
}

function searchInDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const issues = [];
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (item !== 'node_modules' && item !== '.next' && item !== '.git') {
          walkDir(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          const fileIssues = searchInFile(fullPath);
          issues.push(...fileIssues);
        }
      }
    }
  }
  
  walkDir(dirPath);
  return issues;
}

async function findUndefinedVariables() {
  console.log('🔍 البحث عن المتغيرات غير المعرفة...\n');
  
  try {
    const issues = searchInDirectory(process.cwd());
    
    if (issues.length === 0) {
      console.log('✅ لم يتم العثور على متغيرات غير معرفة');
      return;
    }
    
    console.log(`🚨 تم العثور على ${issues.length} مشكلة محتملة:\n`);
    
    // Group by file
    const groupedIssues = {};
    issues.forEach(issue => {
      if (!groupedIssues[issue.file]) {
        groupedIssues[issue.file] = [];
      }
      groupedIssues[issue.file].push(issue);
    });
    
    // Display issues
    Object.keys(groupedIssues).forEach(file => {
      console.log(`📁 ${path.relative(process.cwd(), file)}`);
      console.log('----------------------------------------');
      
      groupedIssues[file].forEach(issue => {
        console.log(`   🔴 السطر ${issue.line}: متغير غير معرف '${issue.variable}'`);
        console.log(`      الكود: ${issue.content}`);
        console.log('');
      });
    });
    
    // Save report
    const report = {
      timestamp: new Date().toISOString(),
      totalIssues: issues.length,
      issues: issues
    };
    
    fs.writeFileSync('undefined-variables-report.json', JSON.stringify(report, null, 2));
    console.log('📄 تم حفظ التقرير في: undefined-variables-report.json');
    
  } catch (error) {
    console.error('❌ خطأ في البحث:', error);
    throw error;
  }
}

// Run search
if (require.main === module) {
  findUndefinedVariables()
    .then(() => {
      console.log('\n✅ تم الانتهاء من البحث');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في البحث:', error);
      process.exit(1);
    });
}

module.exports = { findUndefinedVariables };
