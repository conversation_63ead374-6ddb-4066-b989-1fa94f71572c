# سجل أحداث الجهاز المفصل (Device Event Log)

## نظرة عامة
توفر هذه الصفحة سجلاً مفصلاً لجميع الأحداث والعمليات المرتبطة بجهاز محدد، بما في ذلك:

- توريد المبيعات
- المرتجعات
- الفحص والتقييم
- الإرسال للصيانة
- الاستلام من الصيانة
- التسليم للمخزن
- الاستلام من المخزن
- تحويلات المخزن

تعرض الصفحة جميع الأحداث مرتبة حسب التاريخ والوقت مع معلومات عن المستخدمين وأرقام الأوامر.

## المميزات
- عرض مفصل للعمليات والأحداث
- إمكانية التصفية حسب نوع العملية
- إمكانية التصفية حسب المستخدم
- ترتيب الأحداث تصاعدياً أو تنازلياً
- عرض إحصائيات سريعة عن أنواع العمليات
- خيار عرض البيانات كجدول أو كجدول زمني

## الاستخدام
1. أدخل الرقم التسلسلي للجهاز (IMEI) في حقل البحث
2. اضغط على زر "بحث"
3. استخدم أدوات التصفية والترتيب لتخصيص العرض
4. اضغط على زر "طباعة السجل" لطباعة التقرير

## المكونات
- صفحة `/track/event-log/page.tsx`: الصفحة الرئيسية لعرض سجل الأحداث
- رابط في صفحة `/track/page.tsx` للوصول إلى سجل الأحداث المفصل

## نماذج البيانات
تستخدم الصفحة نموذج `DeviceEvent` لتمثيل الأحداث:

```typescript
interface DeviceEvent {
  id: string;          // معرف الحدث
  type: string;        // نوع الحدث (توريد، مبيعات، إلخ)
  typeName: string;    // الاسم العربي للنوع
  title: string;       // عنوان الحدث
  description: string; // وصف الحدث
  date: string;        // تاريخ الحدث
  timestamp: number;   // الطابع الزمني للحدث
  user: string;        // المستخدم المسؤول
  orderNumber: string; // رقم الأمر
  status: string;      // حالة الحدث
  details?: any;       // تفاصيل إضافية
}
```

## التحسينات المستقبلية
- إضافة خيارات تصدير التقرير بتنسيقات مختلفة (PDF, Excel)
- إضافة إحصائيات متقدمة وتحليلات للبيانات
- إضافة رسوم بيانية لتوزيع الأحداث
- دعم البحث عن أجهزة متعددة في نفس الوقت
