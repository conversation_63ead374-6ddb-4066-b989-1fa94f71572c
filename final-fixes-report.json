{"timestamp": "2025-08-05T00:03:23.859Z", "totalFixes": 4, "filesModified": ["app/api/maintenance-orders/route.ts", "app/api/maintenance-receipts/route.ts"], "fixes": [{"search": {}, "replace": "JSON.stringify($1, (key, value) => value instanceof Date ? value.toISOString() : value)", "description": "إصلاح JSON.stringify للتواريخ في items"}, {"search": {}, "replace": "JSON.stringify($1, (key, value) => value instanceof Date ? value.toISOString() : value)", "description": "إصلاح JSON.stringify للتواريخ في attachments"}]}