/**
 * سكريبت تنظيف قاعدة البيانات وإنشاء المستخدم الافتراضي
 * Reset Database and Create Default Admin User
 * تاريخ: 5 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetDatabaseAndCreateAdmin() {
  console.log('🗑️ بدء تنظيف قاعدة البيانات...\n');

  try {
    // حذف جميع البيانات بالترتيب الصحيح (من الجداول التابعة إلى الرئيسية)
    console.log('🔥 حذف جميع البيانات...');
    
    // حذف الجداول التابعة أولاً (بالترتيب الصحيح لتجنب خطأ العلاقات)
    await prisma.auditLog.deleteMany({});
    console.log('  ✅ تم حذف سجلات المراجعة');
    
    await prisma.internalMessage.deleteMany({});
    console.log('  ✅ تم حذف الرسائل الداخلية');
    
    await prisma.userPermission.deleteMany({});
    console.log('  ✅ تم حذف صلاحيات المستخدمين');
    
    await prisma.userWarehouseAccess.deleteMany({});
    console.log('  ✅ تم حذف صلاحيات المخازن للمستخدمين');
    
    await prisma.warehouseTransferAttachment.deleteMany({});
    console.log('  ✅ تم حذف مرفقات تحويلات المخازن');
    
    await prisma.warehouseTransfer.deleteMany({});
    console.log('  ✅ تم حذف تحويلات المخازن');
    
    await prisma.maintenanceReceiptOrderItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر إيصالات الصيانة');
    
    await prisma.maintenanceReceiptOrder.deleteMany({});
    console.log('  ✅ تم حذف إيصالات الصيانة');
    
    await prisma.maintenanceOrderItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر أوامر الصيانة');
    
    await prisma.maintenanceOrder.deleteMany({});
    console.log('  ✅ تم حذف أوامر الصيانة');
    
    await prisma.maintenanceLog.deleteMany({});
    console.log('  ✅ تم حذف سجلات الصيانة');
    
    await prisma.deliveryOrderItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر أوامر التسليم');
    
    await prisma.deliveryOrder.deleteMany({});
    console.log('  ✅ تم حذف أوامر التسليم');
    
    await prisma.evaluationOrderItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر أوامر التقييم');
    
    await prisma.evaluationOrder.deleteMany({});
    console.log('  ✅ تم حذف أوامر التقييم');
    
    await prisma.returnAttachment.deleteMany({});
    console.log('  ✅ تم حذف مرفقات المرتجعات');
    
    await prisma.returnItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر المرتجعات');
    
    await prisma.return.deleteMany({});
    console.log('  ✅ تم حذف المرتجعات');
    
    await prisma.saleAttachment.deleteMany({});
    console.log('  ✅ تم حذف مرفقات المبيعات');
    
    await prisma.saleItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر المبيعات');
    
    await prisma.sale.deleteMany({});
    console.log('  ✅ تم حذف المبيعات');
    
    await prisma.supplyOrderDraftAttachment.deleteMany({});
    console.log('  ✅ تم حذف مرفقات مسودات التوريد');
    
    await prisma.supplyOrderDraftItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر مسودات التوريد');
    
    await prisma.supplyOrderDraft.deleteMany({});
    console.log('  ✅ تم حذف مسودات التوريد');
    
    await prisma.supplyOrderItem.deleteMany({});
    console.log('  ✅ تم حذف عناصر أوامر التوريد');
    
    await prisma.supplyOrder.deleteMany({});
    console.log('  ✅ تم حذف أوامر التوريد');
    
    await prisma.deviceReplacement.deleteMany({});
    console.log('  ✅ تم حذف استبدال الأجهزة');
    
    await prisma.device.deleteMany({});
    console.log('  ✅ تم حذف الأجهزة');
    
    await prisma.deviceModel.deleteMany({});
    console.log('  ✅ تم حذف موديلات الأجهزة');
    
    await prisma.client.deleteMany({});
    console.log('  ✅ تم حذف العملاء');
    
    await prisma.supplier.deleteMany({});
    console.log('  ✅ تم حذف الموردين');
    
    await prisma.warehouse.deleteMany({});
    console.log('  ✅ تم حذف المخازن');
    
    // حذف طلبات الموظفين والملحقات
    await prisma.commentAttachment.deleteMany({});
    console.log('  ✅ تم حذف مرفقات التعليقات');
    
    await prisma.requestComment.deleteMany({});
    console.log('  ✅ تم حذف تعليقات الطلبات');
    
    await prisma.requestTag.deleteMany({});
    console.log('  ✅ تم حذف علامات الطلبات');
    
    await prisma.requestAttachment.deleteMany({});
    console.log('  ✅ تم حذف مرفقات الطلبات');
    
    await prisma.employeeRequest.deleteMany({});
    console.log('  ✅ تم حذف طلبات الموظفين');
    
    await prisma.responseTemplate.deleteMany({});
    console.log('  ✅ تم حذف قوالب الردود');
    
    await prisma.notification.deleteMany({});
    console.log('  ✅ تم حذف الإشعارات');
    
    await prisma.messageRecipient.deleteMany({});
    console.log('  ✅ تم حذف مستقبلي الرسائل');
    
    await prisma.post.deleteMany({});
    console.log('  ✅ تم حذف المنشورات');
    
    await prisma.systemSetting.deleteMany({});
    console.log('  ✅ تم حذف إعدادات النظام');
    
    await prisma.databaseBackup.deleteMany({});
    console.log('  ✅ تم حذف نسخ احتياطية من قاعدة البيانات');
    
    await prisma.databaseConnection.deleteMany({});
    console.log('  ✅ تم حذف اتصالات قاعدة البيانات');
    
    await prisma.database.deleteMany({});
    console.log('  ✅ تم حذف قواعد البيانات');
    
    await prisma.user.deleteMany({});
    console.log('  ✅ تم حذف جميع المستخدمين');
    
    await prisma.permission.deleteMany({});
    console.log('  ✅ تم حذف الصلاحيات');

    console.log('\n✨ تم تنظيف قاعدة البيانات بنجاح!\n');

    // إنشاء الصلاحيات الأساسية
    console.log('🔐 إنشاء الصلاحيات الأساسية...');
    
    const permissions = [
      { name: 'users', displayName: 'إدارة المستخدمين', category: 'إدارة' },
      { name: 'devices', displayName: 'إدارة الأجهزة', category: 'مخازن' },
      { name: 'warehouses', displayName: 'إدارة المخازن', category: 'مخازن' },
      { name: 'sales', displayName: 'إدارة المبيعات', category: 'مبيعات' },
      { name: 'returns', displayName: 'إدارة المرتجعات', category: 'مبيعات' },
      { name: 'supply', displayName: 'إدارة التوريد', category: 'مشتريات' },
      { name: 'suppliers', displayName: 'إدارة الموردين', category: 'مشتريات' },
      { name: 'clients', displayName: 'إدارة العملاء', category: 'مبيعات' },
      { name: 'maintenance', displayName: 'إدارة الصيانة', category: 'صيانة' },
      { name: 'evaluations', displayName: 'إدارة التقييمات', category: 'تقييم' },
      { name: 'delivery', displayName: 'إدارة التسليم', category: 'لوجستيات' },
      { name: 'reports', displayName: 'التقارير', category: 'تقارير' },
      { name: 'system', displayName: 'إدارة النظام', category: 'إدارة' }
    ];

    for (const permission of permissions) {
      await prisma.permission.create({
        data: permission
      });
      console.log(`  ✅ تم إنشاء صلاحية: ${permission.displayName}`);
    }

    // إنشاء المستخدم الافتراضي (admin)
    console.log('\n👤 إنشاء المستخدم الافتراضي...');
    
    const adminUser = await prisma.user.create({
      data: {
        username: 'admin',
        email: '<EMAIL>',
        name: 'مدير النظام',
        role: 'admin',
        status: 'Active',
        phone: '+966500000000',
        branchLocation: 'المقر الرئيسي',
        lastLogin: new Date()
      }
    });

    console.log(`  ✅ تم إنشاء المستخدم: ${adminUser.name} (ID: ${adminUser.id})`);

    // منح جميع الصلاحيات للمستخدم الافتراضي
    console.log('\n🔑 منح جميع الصلاحيات للمستخدم الافتراضي...');
    
    const allPermissions = await prisma.permission.findMany();
    
    for (const permission of allPermissions) {
      await prisma.userPermission.create({
        data: {
          userId: adminUser.id,
          permissionId: permission.id,
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
          canViewAll: true,
          canManage: true
        }
      });
      console.log(`  ✅ تم منح صلاحية: ${permission.displayName}`);
    }

    // إنشاء مخزن افتراضي
    console.log('\n🏪 إنشاء مخزن افتراضي...');
    
    const defaultWarehouse = await prisma.warehouse.create({
      data: {
        name: 'المخزن الرئيسي',
        type: 'رئيسي',
        location: 'المقر الرئيسي'
      }
    });

    console.log(`  ✅ تم إنشاء المخزن: ${defaultWarehouse.name} (ID: ${defaultWarehouse.id})`);

    // منح صلاحية الوصول للمخزن الافتراضي
    await prisma.userWarehouseAccess.create({
      data: {
        userId: adminUser.id,
        warehouseId: defaultWarehouse.id,
        accessType: 'admin',
        canTransfer: true,
        canAudit: true
      }
    });

    console.log('  ✅ تم منح صلاحية إدارة المخزن للمستخدم الافتراضي');

    console.log('\n🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('\n📋 معلومات المستخدم الافتراضي:');
    console.log(`   - اسم المستخدم: admin`);
    console.log(`   - الاسم: مدير النظام`);
    console.log(`   - الدور: admin`);
    console.log(`   - البريد الإلكتروني: <EMAIL>`);
    console.log(`   - الصلاحيات: جميع الصلاحيات متاحة`);
    console.log(`   - الوصول للمخازن: صلاحية إدارية كاملة`);
    
    console.log('\n🔑 للدخول إلى النظام، استخدم:');
    console.log('   Token: dXNlcjphZG1pbjphZG1pbg== (مُضمن بالفعل في api-client.ts)');
    
  } catch (error) {
    console.error('❌ حدث خطأ أثناء تنظيف قاعدة البيانات:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل السكريبت
resetDatabaseAndCreateAdmin()
  .then(() => {
    console.log('\n✅ تم إكمال عملية تنظيف قاعدة البيانات بنجاح!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ فشل في تنظيف قاعدة البيانات:', error);
    process.exit(1);
  });
