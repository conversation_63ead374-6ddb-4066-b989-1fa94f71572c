/**
 * فحص الأخطاء النحوية في messaging/page.tsx
 * تاريخ: 4 أغسطس 2025
 */

console.log('🔍 فحص الأخطاء النحوية في messaging/page.tsx...\n');

const fs = require('fs');

try {
  // قراءة الملف
  const filePath = './app/(main)/messaging/page.tsx';
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log('📄 تم قراءة الملف بنجاح');
  console.log(`📊 حجم الملف: ${content.length} حرف`);
  console.log(`📊 عدد الأسطر: ${content.split('\n').length} سطر`);
  
  // فحص الأخطاء الشائعة
  const lines = content.split('\n');
  const errors = [];
  
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    
    // فحص arrow functions مكسورة
    if (line.includes('=>)')) {
      errors.push({
        line: lineNum,
        type: 'Arrow Function Error',
        text: line.trim(),
        issue: 'Extra ) after =>'
      });
    }
    
    // فحص أقواس غير متطابقة
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    
    if (Math.abs(openParens - closeParens) > 2) { // السماح ببعض التفاوت للأقواس المتداخلة
      errors.push({
        line: lineNum,
        type: 'Parentheses Mismatch',
        text: line.trim(),
        issue: `Open: ${openParens}, Close: ${closeParens}`
      });
    }
    
    // فحص semicolons مفقودة في نهاية statements مهمة
    if ((line.includes('const ') || line.includes('let ') || line.includes('var ')) && 
        !line.trim().endsWith(';') && 
        !line.trim().endsWith('{') && 
        !line.trim().endsWith('(') &&
        line.trim().length > 0) {
      // التحقق من أن السطر التالي لا يبدأ بـ . أو [
      const nextLine = lines[index + 1];
      if (nextLine && !nextLine.trim().startsWith('.') && !nextLine.trim().startsWith('[')) {
        errors.push({
          line: lineNum,
          type: 'Missing Semicolon',
          text: line.trim(),
          issue: 'Statement may need semicolon'
        });
      }
    }
  });
  
  console.log('\n🔍 نتائج الفحص:');
  console.log('================');
  
  if (errors.length === 0) {
    console.log('✅ لم يتم العثور على أخطاء نحوية واضحة');
  } else {
    console.log(`⚠️ تم العثور على ${errors.length} مشكلة محتملة:`);
    
    errors.forEach((error, index) => {
      console.log(`\n${index + 1}. ${error.type} (سطر ${error.line})`);
      console.log(`   المشكلة: ${error.issue}`);
      console.log(`   النص: ${error.text}`);
    });
  }
  
  // فحص إضافي للكلمات المفتاحية
  console.log('\n📋 فحص إضافي:');
  console.log('===============');
  
  const keywords = {
    'export default': content.match(/export default/g)?.length || 0,
    'function': content.match(/function /g)?.length || 0,
    'const': content.match(/const /g)?.length || 0,
    'let': content.match(/let /g)?.length || 0,
    'useState': content.match(/useState/g)?.length || 0,
    'useEffect': content.match(/useEffect/g)?.length || 0,
    'filter': content.match(/\.filter\(/g)?.length || 0,
    'map': content.match(/\.map\(/g)?.length || 0,
    'forEach': content.match(/\.forEach\(/g)?.length || 0,
  };
  
  Object.entries(keywords).forEach(([keyword, count]) => {
    console.log(`   ${keyword}: ${count} مرة`);
  });
  
  console.log('\n💡 إذا كانت المشكلة لا تزال موجودة:');
  console.log('1. تحقق من console المتصفح للأخطاء الدقيقة');
  console.log('2. أعد تشغيل خادم Next.js');
  console.log('3. تحقق من أقواس الأكواد المفتوحة/المغلقة');
  console.log('4. تأكد من وجود جميع import statements');
  
} catch (error) {
  console.error('❌ خطأ في قراءة الملف:', error.message);
}
