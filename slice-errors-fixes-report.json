{"timestamp": "2025-08-05T01:06:29.411Z", "totalFixes": 70, "filesModified": ["app/(main)/grading/page.tsx", "app/(main)/inventory/page.tsx", "app/(main)/maintenance/page.tsx", "app/(main)/maintenance-transfer/page.tsx", "app/(main)/returns/page.tsx"], "totalFilesProcessed": 5, "fixes": [{"search": {}, "replace": "new Date().toISOString().slice(0, 16)", "description": "إصلاح new Date().slice(0, 16) إلى new Date().toISOString().slice(0, 16)"}, {"search": {}, "replace": "new Date().toISOString().slice(0, 10)", "description": "إصلاح new Date().slice(0, 10) إلى new Date().toISOString().slice(0, 10)"}, {"search": {}, "replace": "defaultValue={new Date().toISOString().slice(0, 16)}", "description": "إصلاح defaultValue مع slice"}, {"search": {}, "replace": "value={formState.date || new Date().toISOString().slice(0, 16)}", "description": "إصلاح value مع slice"}, {"search": {}, "replace": "date: new Date().toISOString().slice(0, 16)", "description": "إصلاح date property مع slice"}, {"search": {}, "replace": "setOrderDate(new Date().toISOString().slice(0, 16))", "description": "إصلاح setOrderDate مع slice"}, {"search": {}, "replace": "setDeliveryOrderDate(new Date().toISOString().slice(0, 16))", "description": "إصلاح setDeliveryOrderDate مع slice"}, {"search": {}, "replace": "setOrderDate(draft.orderDate || new Date().toISOString().slice(0, 16))", "description": "إصلاح setOrderDate مع draft"}, {"search": {}, "replace": "setDeliveryOrderDate(draft.deliveryOrderDate || new Date().toISOString().slice(0, 16))", "description": "إصلاح setDeliveryOrderDate مع draft"}, {"search": {}, "replace": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`)", "description": "إصلاح doc.save مع slice"}, {"search": {}, "replace": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "description": "إصلاح اسم ملف Excel مع slice"}, {"search": {}, "replace": "doc.save(`${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.pdf`)", "description": "إصلاح doc.save للجهاز مع slice"}, {"search": {}, "replace": "`${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`", "description": "إصلاح اسم ملف Excel للجهاز مع slice"}, {"search": {}, "replace": "onClick={() => setFormState(s => ({ ...s, date: new Date().toISOString().slice(0, 16) }))}", "description": "إصلاح onClick handler مع slice"}]}