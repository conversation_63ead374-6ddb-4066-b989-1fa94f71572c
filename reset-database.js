/**
 * Database Reset Script
 * Date: 2025-08-04
 * Description: Manually reset database to apply new schema
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetDatabase() {
  console.log('🗑️ إعادة تعيين قاعدة البيانات...\n');

  try {
    // Drop all tables in correct order (respecting foreign keys)
    console.log('1️⃣ حذف الجداول...');
    
    const dropQueries = [
      // Drop tables with foreign keys first
      'DROP TABLE IF EXISTS "comment_attachments" CASCADE;',
      'DROP TABLE IF EXISTS "request_tags" CASCADE;',
      'DROP TABLE IF EXISTS "sale_attachments" CASCADE;',
      'DROP TABLE IF EXISTS "return_attachments" CASCADE;',
      'DROP TABLE IF EXISTS "supply_order_draft_items" CASCADE;',
      'DROP TABLE IF EXISTS "supply_order_draft_attachments" CASCADE;',
      'DROP TABLE IF EXISTS "warehouse_transfer_attachments" CASCADE;',
      'DROP TABLE IF EXISTS "message_recipients" CASCADE;',
      'DROP TABLE IF EXISTS "user_permissions" CASCADE;',
      'DROP TABLE IF EXISTS "user_warehouse_access" CASCADE;',
      'DROP TABLE IF EXISTS "request_attachments" CASCADE;',
      'DROP TABLE IF EXISTS "request_comments" CASCADE;',
      'DROP TABLE IF EXISTS "maintenance_order_items" CASCADE;',
      'DROP TABLE IF EXISTS "maintenance_receipt_order_items" CASCADE;',
      'DROP TABLE IF EXISTS "delivery_order_items" CASCADE;',
      'DROP TABLE IF EXISTS "return_items" CASCADE;',
      'DROP TABLE IF EXISTS "sale_items" CASCADE;',
      'DROP TABLE IF EXISTS "supply_order_items" CASCADE;',
      'DROP TABLE IF EXISTS "evaluation_order_items" CASCADE;',
      'DROP TABLE IF EXISTS "device_replacements" CASCADE;',
      
      // Drop main tables
      'DROP TABLE IF EXISTS "employee_requests" CASCADE;',
      'DROP TABLE IF EXISTS "internal_messages" CASCADE;',
      'DROP TABLE IF EXISTS "MaintenanceOrder" CASCADE;',
      'DROP TABLE IF EXISTS "MaintenanceReceiptOrder" CASCADE;',
      'DROP TABLE IF EXISTS "DeliveryOrder" CASCADE;',
      'DROP TABLE IF EXISTS "Return" CASCADE;',
      'DROP TABLE IF EXISTS "Sale" CASCADE;',
      'DROP TABLE IF EXISTS "SupplyOrder" CASCADE;',
      'DROP TABLE IF EXISTS "evaluation_orders" CASCADE;',
      'DROP TABLE IF EXISTS "supply_order_drafts" CASCADE;',
      'DROP TABLE IF EXISTS "warehouse_transfers" CASCADE;',
      'DROP TABLE IF EXISTS "notifications" CASCADE;',
      'DROP TABLE IF EXISTS "response_templates" CASCADE;',
      'DROP TABLE IF EXISTS "maintenance_logs" CASCADE;',
      'DROP TABLE IF EXISTS "AuditLog" CASCADE;',
      'DROP TABLE IF EXISTS "database_backups" CASCADE;',
      'DROP TABLE IF EXISTS "databases" CASCADE;',
      'DROP TABLE IF EXISTS "database_connections" CASCADE;',
      'DROP TABLE IF EXISTS "Post" CASCADE;',
      'DROP TABLE IF EXISTS "users" CASCADE;',
      'DROP TABLE IF EXISTS "permissions" CASCADE;',
      'DROP TABLE IF EXISTS "Warehouse" CASCADE;',
      'DROP TABLE IF EXISTS "Device" CASCADE;',
      'DROP TABLE IF EXISTS "DeviceModel" CASCADE;',
      'DROP TABLE IF EXISTS "Supplier" CASCADE;',
      'DROP TABLE IF EXISTS "Client" CASCADE;',
      'DROP TABLE IF EXISTS "SystemSetting" CASCADE;',
      
      // Drop any remaining tables
      'DROP TABLE IF EXISTS "sales_order_drafts" CASCADE;',
      'DROP TABLE IF EXISTS "returns_order_drafts" CASCADE;',
    ];

    for (const query of dropQueries) {
      try {
        await prisma.$executeRawUnsafe(query);
        console.log(`   ✅ ${query}`);
      } catch (error) {
        console.log(`   ⚠️ ${query} - ${error.message}`);
      }
    }

    console.log('\n2️⃣ تم حذف جميع الجداول بنجاح!');
    console.log('\n🎉 يمكنك الآن تشغيل: npx prisma db push');

  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين قاعدة البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run reset
if (require.main === module) {
  resetDatabase()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إعادة تعيين قاعدة البيانات');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إعادة تعيين قاعدة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { resetDatabase };
