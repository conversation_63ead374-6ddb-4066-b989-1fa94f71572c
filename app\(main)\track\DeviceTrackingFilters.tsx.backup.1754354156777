'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Filter,
  Calendar,
  Search,
  X,
  Download,
  BarChart3,
  Clock,
  User
} from 'lucide-react';
import { format } from 'date-fns';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface TimelineEvent {
  icon: React.ReactNode;
  title: string;
  description: string;
  date: Date;
  color: string;
  user?: string;
  formattedDate?: string; // يبقى string للعرض // يبقى string للعرض
  type: string;
  details?: any;
}

interface DeviceTrackingFiltersProps {
  events: TimelineEvent[];
  onFilterChange: (filteredEvents: TimelineEvent[]) => void;
  deviceInfo: {
    model: string;
    id: string;
    status: string;
  };
}

export default function DeviceTrackingFilters({ 
  events, 
  onFilterChange, 
  deviceInfo 
}: DeviceTrackingFiltersProps) {
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    eventTypes: [] as string[],
    searchTerm: '',
    users: [] as string[],
    showDetailsOnly: false
  });

  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  // الحصول على أنواع الأحداث المتاحة
  const eventTypes = [...new Set(events.map(event => {
    if (event.title.includes('توريد')) return 'supply';
    if (event.title.includes('فحص') || event.title.includes('تقييم')) return 'evaluation';
    if (event.title.includes('صيانة') || event.title.includes('إصلاح')) return 'maintenance';
    if (event.title.includes('تحويل')) return 'transfer';
    if (event.title.includes('بيع')) return 'sale';
    if (event.title.includes('إرجاع')) return 'return';
    if (event.title.includes('بديل') || event.title.includes('استبدال')) return 'replacement';
    return 'other';
  }))];

  // الحصول على المستخدمين المتاحين
  const users = [...new Set(events.map(event => event.user).filter(Boolean))];

  // دالة تطبيق الفلاتر
  const applyFilters = () => {
    let filteredEvents = [...events];

    // فلترة بالتاريخ
    if (filters.dateFrom) {
      filteredEvents = filteredEvents.filter(event => 
        new Date(event.date) >= new Date(filters.dateFrom)
      );
    }
    if (filters.dateTo) {
      filteredEvents = filteredEvents.filter(event => 
        new Date(event.date) <= new Date(filters.dateTo)
      );
    }

    // فلترة بنوع الحدث
    if (filters.eventTypes.length > 0) {
      filteredEvents = filteredEvents.filter(event => {
        const eventType = event.title.includes('توريد') ? 'supply' :
                         event.title.includes('فحص') || event.title.includes('تقييم') ? 'evaluation' :
                         event.title.includes('صيانة') || event.title.includes('إصلاح') ? 'maintenance' :
                         event.title.includes('تحويل') ? 'transfer' :
                         event.title.includes('بيع') ? 'sale' :
                         event.title.includes('إرجاع') ? 'return' :
                         event.title.includes('بديل') || event.title.includes('استبدال') ? 'replacement' :
                         'other';
        return filters.eventTypes.includes(eventType);
      });
    }

    // فلترة بالبحث النصي
    if (filters.searchTerm) {
      filteredEvents = filteredEvents.filter(event =>
        event.title.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        event.description.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        (event.user && event.user.toLowerCase().includes(filters.searchTerm.toLowerCase()))
      );
    }

    // فلترة بالمستخدمين
    if (filters.users.length > 0) {
      filteredEvents = filteredEvents.filter(event =>
        event.user && filters.users.includes(event.user)
      );
    }

    // فلترة الأحداث التي تحتوي على تفاصيل فقط
    if (filters.showDetailsOnly) {
      filteredEvents = filteredEvents.filter(event => event.details && Object.keys(event.details).length > 0);
    }

    onFilterChange(filteredEvents);
  };

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      eventTypes: [],
      searchTerm: '',
      users: [],
      showDetailsOnly: false
    });
    onFilterChange(events);
  };

  // تحديث فلتر معين
  const updateFilter = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  // إضافة/إزالة نوع حدث
  const toggleEventType = (type: string) => {
    const newTypes = filters.eventTypes.includes(type)
      ? filters.eventTypes.filter(t => t !== type)
      : [...filters.eventTypes, type];
    updateFilter('eventTypes', newTypes);
  };

  // إضافة/إزالة مستخدم
  const toggleUser = (user: string) => {
    const newUsers = filters.users.includes(user)
      ? filters.users.filter(u => u !== user)
      : [...filters.users, user];
    updateFilter('users', newUsers);
  };

  // تصدير البيانات المفلترة
  const exportFilteredData = () => {
    // تحضير البيانات للتصدير
    const dataToExport = events.map(event => ({
      'التاريخ': format(new Date(event.date), 'yyyy-MM-dd HH:mm'),
      'نوع العملية': event.title,
      'الوصف': event.description,
      'المسؤول': event.user || '',
      'تفاصيل إضافية': event.details ? JSON.stringify(event.details, null, 2) : ''
    }));

    // تحويل إلى CSV
    const headers = Object.keys(dataToExport[0] || {});
    const csvContent = [
      headers.join(','),
      ...dataToExport.map(row => 
        headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
      )
    ].join('\n');

    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `device_history_${deviceInfo.id}_${format(new Date(), 'yyyy-MM-dd')}.csv`;
    link.click();
  };

  const getEventTypeNameArabic = (type: string): string => {
    switch (type) {
      case 'supply': return 'توريد';
      case 'evaluation': return 'فحص وتقييم';
      case 'maintenance': return 'صيانة';
      case 'transfer': return 'نقل مخزني';
      case 'sale': return 'بيع';
      case 'return': return 'إرجاع';
      case 'replacement': return 'استبدال';
      default: return 'أخرى';
    }
  };

  React.useEffect(() => {
    applyFilters();
  }, [filters]);

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            فلاتر متقدمة للتتبع
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportFilteredData}
              disabled={events.length === 0}
            >
              <Download className="h-4 w-4 ml-1" />
              تصدير
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsFiltersOpen(!isFiltersOpen)}
            >
              <BarChart3 className="h-4 w-4 ml-1" />
              {isFiltersOpen ? 'إخفاء' : 'إظهار'} الفلاتر
            </Button>
          </div>
        </div>
      </CardHeader>

      {isFiltersOpen && (
        <CardContent className="space-y-6">
          {/* فلاتر التاريخ */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dateFrom" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                من تاريخ
              </Label>
              <Input
                id="dateFrom"
                type="datetime-local"
                value={filters.dateFrom}
                onChange={(e) => updateFilter('dateFrom', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dateTo" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                إلى تاريخ
              </Label>
              <Input
                id="dateTo"
                type="datetime-local"
                value={filters.dateTo}
                onChange={(e) => updateFilter('dateTo', e.target.value)}
              />
            </div>
          </div>

          {/* البحث النصي */}
          <div className="space-y-2">
            <Label htmlFor="searchTerm" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              البحث في الأحداث
            </Label>
            <Input
              id="searchTerm"
              placeholder="ابحث في العناوين والأوصاف..."
              value={filters.searchTerm}
              onChange={(e) => updateFilter('searchTerm', e.target.value)}
            />
          </div>

          {/* فلترة أنواع الأحداث */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold">أنواع الأحداث</Label>
            <div className="flex flex-wrap gap-2">
              {eventTypes.map(type => (
                <div key={type} className="flex items-center">
                  <Badge
                    variant={filters.eventTypes.includes(type) ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => toggleEventType(type)}
                  >
                    {getEventTypeNameArabic(type)}
                    {filters.eventTypes.includes(type) && (
                      <X className="h-3 w-3 ml-1" />
                    )}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* فلترة المستخدمين */}
          {users.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-semibold flex items-center gap-2">
                <User className="h-4 w-4" />
                المستخدمون
              </Label>
              <div className="flex flex-wrap gap-2">
                {users.map(user => (
                  <div key={user} className="flex items-center">
                    <Badge
                      variant={filters.users.includes(user) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => toggleUser(user)}
                    >
                      {user}
                      {filters.users.includes(user) && (
                        <X className="h-3 w-3 ml-1" />
                      )}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* خيارات إضافية */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="showDetailsOnly"
                checked={filters.showDetailsOnly}
                onCheckedChange={(checked) => updateFilter('showDetailsOnly', !!checked)}
              />
              <Label htmlFor="showDetailsOnly" className="text-sm">
                إظهار الأحداث التي تحتوي على تفاصيل إضافية فقط
              </Label>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={applyFilters} size="sm">
              تطبيق الفلاتر
            </Button>
            <Button variant="outline" onClick={resetFilters} size="sm">
              <X className="h-4 w-4 ml-1" />
              إعادة تعيين
            </Button>
          </div>

          {/* معلومات النتائج */}
          <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>النتائج:</strong> يتم عرض {events.length} حدث من أصل {events.length} حدث إجمالي للجهاز {deviceInfo.model} ({deviceInfo.id})
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
