/**
 * فحص نهائي لحل مشاكل 401 - Final 401 Fix Verification
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalVerification() {
  console.log('🔍 فحص نهائي لحل مشاكل 401...\n');
  
  try {
    // 1. اختبار جميع APIs التي كانت تفشل
    console.log('1️⃣ اختبار APIs التي كانت تواجه مشاكل 401...');
    
    const correctToken = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    const problemAPIs = [
      '/api/suppliers?view=simple',
      '/api/device-models?view=simple',
      '/api/settings?view=simple',
      '/api/employee-requests?view=simple'
    ];
    
    const results = [];
    
    for (const endpoint of problemAPIs) {
      console.log(`\n   🔍 ${endpoint}`);
      
      try {
        const response = await fetch(`http://localhost:9005${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${correctToken}`
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          const count = Array.isArray(data) ? data.length : 'object';
          console.log(`      ✅ نجح - البيانات: ${count}`);
          results.push({ endpoint, status: 'نجح', count });
        } else {
          const errorText = await response.text();
          console.log(`      ❌ فشل - Status: ${response.status}`);
          console.log(`      الخطأ: ${errorText.substring(0, 100)}...`);
          results.push({ endpoint, status: 'فشل', error: response.status });
        }
      } catch (error) {
        console.log(`      ❌ خطأ شبكة: ${error.message}`);
        results.push({ endpoint, status: 'خطأ شبكة', error: error.message });
      }
    }
    
    // 2. اختبار محاكاة طلبات المتصفح
    console.log('\n2️⃣ محاكاة طلبات المتصفح...');
    
    const browserSimulation = [
      { name: 'صفحة المخزون - الأجهزة', url: '/api/devices?view=simple' },
      { name: 'صفحة المخزون - المخازن', url: '/api/warehouses?view=simple' },
      { name: 'صفحة المخزون - المبيعات', url: '/api/sales?view=simple' },
      { name: 'صفحة المخزون - المرتجعات', url: '/api/returns?view=simple' },
      { name: 'صفحة المخزون - الموردين', url: '/api/suppliers?view=simple' },
      { name: 'صفحة المخزون - موديلات الأجهزة', url: '/api/device-models?view=simple' },
      { name: 'صفحة المخزون - الإعدادات', url: '/api/settings?view=simple' }
    ];
    
    for (const test of browserSimulation) {
      try {
        const response = await fetch(`http://localhost:9005${test.url}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${correctToken}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          const count = Array.isArray(data) ? data.length : 'نوع object';
          console.log(`   ✅ ${test.name}: ${count}`);
        } else {
          console.log(`   ❌ ${test.name}: Status ${response.status}`);
        }
      } catch (error) {
        console.log(`   ❌ ${test.name}: خطأ - ${error.message}`);
      }
    }
    
    // 3. فحص إعدادات المصادقة المحدثة
    console.log('\n3️⃣ فحص إعدادات المصادقة...');
    
    // فحص ملف api-client.ts
    const fs = require('fs');
    try {
      const apiClientContent = fs.readFileSync('./lib/api-client.ts', 'utf8');
      const hasFixedToken = apiClientContent.includes('dXNlcjphZG1pbjphZG1pbg==');
      const usesBtoa = apiClientContent.includes('btoa(');
      
      console.log(`   📄 ملف api-client.ts:`);
      console.log(`      - يستخدم التوكن الثابت: ${hasFixedToken ? '✅ نعم' : '❌ لا'}`);
      console.log(`      - يستخدم btoa(): ${usesBtoa ? '⚠️ نعم (قد يسبب مشاكل)' : '✅ لا'}`);
      
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف api-client.ts:', error.message);
    }
    
    // 4. فحص ملفات الصفحات المحدثة
    console.log('\n4️⃣ فحص ملفات الصفحات المحدثة...');
    
    try {
      const inventoryContent = fs.readFileSync('./app/(main)/inventory/page.tsx', 'utf8');
      const usesFetch = inventoryContent.includes('await fetch(');
      const usesApiClient = inventoryContent.includes('await apiClient.get(');
      
      console.log(`   📄 صفحة المخزون:`);
      console.log(`      - يستخدم fetch() مباشرة: ${usesFetch ? '⚠️ نعم' : '✅ لا'}`);
      console.log(`      - يستخدم apiClient: ${usesApiClient ? '✅ نعم' : '❌ لا'}`);
      
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف صفحة المخزون:', error.message);
    }
    
    // 5. تلخيص النتائج
    console.log('\n📊 ملخص النتائج النهائية:');
    console.log('================================');
    
    const successful = results.filter(r => r.status === 'نجح');
    const failed = results.filter(r => r.status !== 'نجح');
    
    console.log(`✅ APIs تعمل بنجاح: ${successful.length}/${results.length}`);
    console.log(`❌ APIs لا تزال تفشل: ${failed.length}/${results.length}`);
    
    if (failed.length === 0) {
      console.log('\n🎉 تم حل جميع مشاكل 401 بنجاح!');
      console.log('\n✨ الإصلاحات التي تمت:');
      console.log('   1. ✅ إصلاح العمود attachments في employee_requests');
      console.log('   2. ✅ توحيد استخدام apiClient في جميع الصفحات');
      console.log('   3. ✅ إصلاح التوكن في api-client.ts');
      console.log('   4. ✅ التأكد من وجود المستخدم admin');
      
      console.log('\n💡 نصائح للحفاظ على الاستقرار:');
      console.log('   - استخدم دائماً apiClient بدلاً من fetch() المباشرة');
      console.log('   - تأكد من تحديث التوكن عند التغيير إلى نظام JWT');
      console.log('   - راقب logs الخادم للتأكد من عدم ظهور أخطاء جديدة');
      
    } else {
      console.log('\n⚠️ لا تزال هناك بعض المشاكل:');
      failed.forEach(api => {
        console.log(`   - ${api.endpoint}: ${api.status}`);
      });
      
      console.log('\n🔧 خطوات إضافية مقترحة:');
      console.log('   1. أعد تشغيل خادم Next.js');
      console.log('   2. امسح cache المتصفح');
      console.log('   3. تحقق من console المتصفح للأخطاء');
    }
    
    console.log('\n🚀 الخطوة التالية: جرب فتح صفحة المخزون في المتصفح');
    
  } catch (error) {
    console.error('❌ خطأ في الفحص النهائي:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الفحص النهائي
finalVerification();
