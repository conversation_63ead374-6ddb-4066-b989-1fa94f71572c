const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixReturnsDateIssue() {
  try {
    console.log('🔍 Checking returns table for date issues...');
    
    // Try to get all returns with raw query first to see problematic data
    const problematicReturns = await prisma.$queryRaw`
      SELECT id, "returnNumber", date, "clientName", status 
      FROM "Return" 
      WHERE date::text LIKE '%+00'
      LIMIT 10
    `;
    
    console.log('Problematic returns found:', problematicReturns?.length || 0);
    
    if (problematicReturns && problematicReturns.length > 0) {
      console.log('Sample problematic entries:', problematicReturns.slice(0, 3));
      
      // Try to fix the date format
      for (const returnRecord of problematicReturns) {
        try {
          // Convert the problematic date to proper format
          const dateStr = returnRecord.date;
          const fixedDate = new Date(dateStr);
          
          if (!isNaN(fixedDate.getTime())) {
            await prisma.$queryRaw`
              UPDATE "Return" 
              SET date = ${fixedDate}
              WHERE id = ${returnRecord.id}
            `;
            console.log(`✅ Fixed date for return ID ${returnRecord.id}`);
          }
        } catch (error) {
          console.log(`❌ Could not fix return ID ${returnRecord.id}:`, error.message);
        }
      }
    }
    
    // Test if we can now query normally
    const testQuery = await prisma.return.findFirst({
      orderBy: { id: 'desc' }
    });
    
    console.log('✅ Test query successful:', testQuery ? 'Found data' : 'No data');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

fixReturnsDateIssue();
