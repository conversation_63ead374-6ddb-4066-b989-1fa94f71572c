/**
 * إصلاح مشاكل التواريخ في استعلامات $queryRaw
 * Fix Date Issues in $queryRaw Queries
 * تاريخ: 5 أغسطس 2025
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 إصلاح مشاكل التواريخ في استعلامات $queryRaw...\n');

// الملفات التي تحتاج إصلاح
const filesToFix = [
  'app/api/sales/route.ts',
  'app/api/supply/route.ts',
  'app/api/evaluations/route.ts',
  'app/api/maintenance-receipts/route.ts'
];

// دالة لإصلاح معالجة التواريخ في ملف
function fixDateHandlingInFile(filePath) {
  console.log(`🔍 فحص ملف: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // إصلاح 1: إضافة معالجة صحيحة للتواريخ قبل استعلامات $queryRaw
  const dateValidationPattern = /const\s+(\w+Date)\s*=\s*new\s+Date\([^)]+\);/g;
  
  // البحث عن استعلامات $queryRaw التي تحتوي على تواريخ
  if (content.includes('$queryRaw') && !content.includes('if (isNaN(') && content.includes('new Date(')) {
    console.log(`  📝 إضافة معالجة صحيحة للتواريخ...`);
    
    // إضافة دالة مساعدة لمعالجة التواريخ في بداية الملف بعد الـ imports
    const helperFunction = `
// دالة مساعدة لمعالجة التواريخ بشكل آمن
function createSafeDate(dateInput: any): Date {
  if (!dateInput) return new Date();
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date format: ' + dateInput);
  }
  return date;
}

`;

    // إدراج الدالة المساعدة بعد آخر import
    const lastImportIndex = content.lastIndexOf("import");
    if (lastImportIndex !== -1) {
      const nextLineIndex = content.indexOf('\n', lastImportIndex);
      if (nextLineIndex !== -1) {
        const nextNewLineIndex = content.indexOf('\n', nextLineIndex + 1);
        if (nextNewLineIndex !== -1) {
          content = content.slice(0, nextNewLineIndex + 1) + helperFunction + content.slice(nextNewLineIndex + 1);
          modified = true;
        }
      }
    }

    // إصلاح استخدامات new Date() في $queryRaw
    content = content.replace(
      /new\s+Date\(([^)]+)\)(?=.*\$queryRaw)/g,
      'createSafeDate($1)'
    );
    modified = true;
  }

  // إصلاح 2: إصلاح دوال sanitize للحفاظ على Date objects
  if (content.includes('function sanitize') && !content.includes('value instanceof Date')) {
    console.log(`  🧹 إصلاح دوال التنظيف للحفاظ على Date objects...`);
    
    content = content.replace(
      /function\s+sanitize\w*Data\s*\([^}]+\{\s*const\s+sanitized[^}]+for\s*\([^}]+\)\s*\{\s*if\s*\(\s*typeof\s+value\s*===\s*['"]string['"][^}]+\}\s*else\s*\{[^}]+\}\s*\}/,
      function(match) {
        return match.replace(
          /if\s*\(\s*typeof\s+value\s*===\s*['"]string['"][^}]+\}\s*else\s*\{/,
          `if (value instanceof Date) {
      // الحفاظ على Date objects كما هي
      sanitized[key] = value;
    } else if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else {`
        );
      }
    );
    modified = true;
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ تم إصلاح الملف: ${filePath}`);
  } else {
    console.log(`  ℹ️ لا توجد تغييرات مطلوبة في: ${filePath}`);
  }
}

// إصلاح جميع الملفات
filesToFix.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  fixDateHandlingInFile(fullPath);
});

console.log('\n✅ تم إكمال إصلاح مشاكل التواريخ في استعلامات $queryRaw!');

// إصلاح إضافي: فحص ملفات draft أيضاً
console.log('\n🔍 فحص ملفات draft للتأكد من عدم وجود مشاكل مشابهة...');

const draftFiles = [
  'app/api/sales-draft/route.ts',
  'app/api/returns-draft/route.ts',
  'app/api/supply-draft/route.ts'
];

draftFiles.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes('$queryRaw') && content.includes('date')) {
      console.log(`⚠️ قد يحتاج الملف للمراجعة: ${file}`);
    } else {
      console.log(`✅ الملف سليم: ${file}`);
    }
  }
});

console.log('\n🎉 تم إنهاء فحص وإصلاح جميع الملفات!');
