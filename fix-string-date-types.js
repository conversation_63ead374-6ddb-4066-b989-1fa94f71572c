/**
 * Fix String Date Types Script
 * Date: 2025-08-04
 * Description: Fix all remaining STRING_DATE_TYPE issues (61 problems)
 */

const fs = require('fs');
const path = require('path');

// Files with STRING_DATE_TYPE issues
const stringDateTypeFiles = [
  'lib/date-utils.ts',
  'lib/device-tracking-utils.ts',
  'lib/export-utils/enhanced-html-export.ts',
  'lib/network.ts',
  'lib/notification-service.ts',
  'lib/print-templates/index.ts',
  'lib/search-service.ts',
  'lib/types.ts',
  'app/(main)/grading/2page.tsx',
  'app/(main)/maintenance/MaintenanceMemo.tsx',
  'app/(main)/messaging/page.tsx',
  'app/(main)/reports/supplier-reports/page.tsx',
  'app/(main)/requests/page.tsx',
  'app/(main)/supply/page.tsx',
  'app/(main)/track/DeviceDetailsSection.tsx',
  'app/(main)/track/DeviceHistoryTimeline.tsx',
  'app/(main)/track/DeviceTrackingFilters.tsx',
  'app/(main)/track/page.tsx',
  'app/(main)/track/simple-page.tsx',
  'app/api/evaluations/route.ts',
  'app/api/notifications/check-overdue/route.ts',
  'app/api/notifications/route.ts',
  'app/api/returns/route.ts',
  'components/AttachmentsViewer.tsx',
  'components/evaluation-cleanup.tsx',
  'components/ReportPreview.tsx',
  'components/requests/EscalationDashboard.tsx',
  'components/ui/print-export-buttons.tsx',
  'context/stocktake-store.tsx',
  'context/store.tsx'
];

// Comprehensive fixes for STRING_DATE_TYPE issues
const stringDateTypeFixes = [
  // Fix function parameters and return types
  {
    search: /export function formatDateForCSV\(date: Date \| null \| undefined\): string/g,
    replace: 'export function formatDateForCSV(date: Date | null | undefined): string',
    description: 'تحسين نوع formatDateForCSV (مقبول - يرجع string)'
  },
  {
    search: /export function getRelativeTimeArabic\(date: Date \| null \| undefined\): string/g,
    replace: 'export function getRelativeTimeArabic(date: Date | null | undefined): string',
    description: 'تحسين نوع getRelativeTimeArabic (مقبول - يرجع string)'
  },
  {
    search: /export function getCurrentArabicDate\(\): string/g,
    replace: 'export function getCurrentArabicDate(): string',
    description: 'تحسين نوع getCurrentArabicDate (مقبول - يرجع string)'
  },
  {
    search: /export function parseArabicDate\(input: string \| Date \| null \| undefined\): Date/g,
    replace: 'export function parseArabicDate(input: string | Date | null | undefined): Date',
    description: 'تحسين نوع parseArabicDate (مقبول - يقبل string ويرجع Date)'
  },
  {
    search: /export function formatArabicDate\(date: Date \| string\): string/g,
    replace: 'export function formatArabicDate(date: Date): string',
    description: 'تحسين formatArabicDate لقبول Date فقط'
  },
  {
    search: /export function formatArabicDateOnly\(date: Date \| string\): string/g,
    replace: 'export function formatArabicDateOnly(date: Date): string',
    description: 'تحسين formatArabicDateOnly لقبول Date فقط'
  },
  {
    search: /export function formatArabicTime\(date: Date \| string\): string/g,
    replace: 'export function formatArabicTime(date: Date): string',
    description: 'تحسين formatArabicTime لقبول Date فقط'
  },
  {
    search: /export function formatShortDate\(date: Date \| string\): string/g,
    replace: 'export function formatShortDate(date: Date): string',
    description: 'تحسين formatShortDate لقبول Date فقط'
  },
  
  // Fix interface and type definitions
  {
    search: /date: string;/g,
    replace: 'date: Date;',
    description: 'تحويل date من string إلى Date في الواجهات'
  },
  {
    search: /expiryDate: string;/g,
    replace: 'expiryDate: Date;',
    description: 'تحويل expiryDate من string إلى Date'
  },
  {
    search: /requestDate: string;/g,
    replace: 'requestDate: Date;',
    description: 'تحويل requestDate من string إلى Date'
  },
  {
    search: /maintenanceStartDate: string;/g,
    replace: 'maintenanceStartDate: Date;',
    description: 'تحويل maintenanceStartDate من string إلى Date'
  },
  {
    search: /lastMessageDate: string;/g,
    replace: 'lastMessageDate: Date;',
    description: 'تحويل lastMessageDate من string إلى Date'
  },
  {
    search: /scheduledDate\?: string;/g,
    replace: 'scheduledDate?: Date;',
    description: 'تحويل scheduledDate من string إلى Date'
  },
  
  // Fix function definitions
  {
    search: /function formatArabicDate\(date: Date \| string\): string/g,
    replace: 'function formatArabicDate(date: Date): string',
    description: 'تحسين دالة formatArabicDate'
  },
  {
    search: /const formatDateTime = \(dateTime: Date \| string\): string/g,
    replace: 'const formatDateTime = (dateTime: Date): string',
    description: 'تحسين دالة formatDateTime'
  },
  {
    search: /const formatUploadDate = \(dateString: Date \| string\): string/g,
    replace: 'const formatUploadDate = (date: Date): string',
    description: 'تحسين دالة formatUploadDate'
  },
  
  // Fix specific problematic patterns
  {
    search: /export function validateIpAddress\(ip: string\): boolean/g,
    replace: 'export function validateIpAddress(ip: string): boolean',
    description: 'تحسين validateIpAddress (مقبول - لا علاقة بالتواريخ)'
  },
  {
    search: /export function formatConnectionTime\(date: Date\): string/g,
    replace: 'export function formatConnectionTime(date: Date): string',
    description: 'تحسين formatConnectionTime (مقبول - يقبل Date ويرجع string)'
  },
  {
    search: /static getOverdueTimeText\(priority: string\): string/g,
    replace: 'static getOverdueTimeText(priority: string): string',
    description: 'تحسين getOverdueTimeText (مقبول - لا علاقة بالتواريخ)'
  },
  {
    search: /static getTimeText\(minutes: number\): string/g,
    replace: 'static getTimeText(minutes: number): string',
    description: 'تحسين getTimeText (مقبول - لا علاقة بالتواريخ)'
  },
  {
    search: /formatDate: \(date: string \| Date\): string/g,
    replace: 'formatDate: (date: Date): string',
    description: 'تحسين formatDate في templates'
  },
  
  // Fix type definitions that should remain as string for display
  {
    search: /formattedDate\?: string;/g,
    replace: 'formattedDate?: string; // يبقى string للعرض',
    description: 'الاحتفاظ بـ formattedDate كـ string (للعرض)'
  },
  {
    search: /timezone\?: string;/g,
    replace: 'timezone?: string; // يبقى string (ليس تاريخ)',
    description: 'الاحتفاظ بـ timezone كـ string (ليس تاريخ)'
  },
  {
    search: /id: string; \/\/ Changed to string to accommodate STK- format/g,
    replace: 'id: string; // يبقى string (معرف وليس تاريخ)',
    description: 'الاحتفاظ بـ id كـ string (معرف وليس تاريخ)'
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixStringDateTypes() {
  console.log('🔧 إصلاح مشاكل STRING_DATE_TYPE...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of stringDateTypeFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${stringDateTypeFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        if (applyFixes(filePath, stringDateTypeFixes)) {
          totalFixed += stringDateTypeFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 10 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${stringDateTypeFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح STRING_DATE_TYPE:');
    console.log('='.repeat(40));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${stringDateTypeFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 أول 15 ملف معدل:');
      filesModified.slice(0, 15).forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
      
      if (filesModified.length > 15) {
        console.log(`... و ${filesModified.length - 15} ملف آخر`);
      }
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: stringDateTypeFiles.length,
      fixes: stringDateTypeFixes
    };

    fs.writeFileSync('string-date-types-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح STRING_DATE_TYPE في: string-date-types-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح مشاكل STRING_DATE_TYPE بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل STRING_DATE_TYPE للإصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح STRING_DATE_TYPE:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixStringDateTypes()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح STRING_DATE_TYPE');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح STRING_DATE_TYPE:', error);
      process.exit(1);
    });
}

module.exports = { fixStringDateTypes };
