/**
 * Fix TO_ISO_STRING_USAGE Script
 * Date: 2025-08-04
 * Description: Fix all remaining TO_ISO_STRING_USAGE issues (64 problems)
 */

const fs = require('fs');
const path = require('path');

// Files with TO_ISO_STRING_USAGE issues
const toIsoStringFiles = [
  'lib/database-config.ts',
  'app/(main)/grading/2page.tsx',
  'app/(main)/inventory/page_backup.tsx',
  'app/(main)/maintenance/page.tsx',
  'app/(main)/maintenance-transfer/page.tsx',
  'app/(main)/messaging/page.tsx',
  'app/(main)/reports/employee-reports/page.tsx',
  'app/(main)/reports/grading-reports/page.tsx',
  'app/(main)/returns/page.tsx',
  'app/(main)/sales/page.tsx',
  'app/(main)/settings/appearance-settings.tsx',
  'app/(main)/stocktaking/page.tsx',
  'app/(main)/supply/page.tsx',
  'app/(main)/test-export/page.tsx',
  'app/api/database/backup/route.ts',
  'app/api/evaluations/route.ts',
  'app/api/maintenance-orders/route.ts',
  'app/api/maintenance-receipts/route.ts',
  'app/api/returns/route.ts',
  'components/requests/AdvancedSearch.tsx',
  'context/store.tsx'
];

// Fixes for TO_ISO_STRING_USAGE issues
const toIsoStringFixes = [
  // Fix database config timestamp
  {
    search: /timestamp: new Date\(\)\.toISOString\(\)/g,
    replace: 'timestamp: new Date()',
    description: 'استخدام Date object للـ timestamp'
  },
  
  // Fix console.log statements (keep toISOString for logging)
  {
    search: /console\.log\(`📅 تاريخ الأمر: \$\{orderDate\.toISOString\(\)\}`\);/g,
    replace: 'console.log(`📅 تاريخ الأمر: ${formatDateTime(orderDate)}`);',
    description: 'تحسين console.log للتواريخ'
  },
  {
    search: /console\.log\(`.*\$\{.*\.toISOString\(\)\}.*`\);/g,
    replace: function(match) {
      return match.replace(/\.toISOString\(\)/g, '');
    },
    description: 'تحسين console.log statements'
  },
  
  // Fix form data assignments (keep for form inputs)
  {
    search: /date: new Date\(orderDate\)\.toISOString\(\)/g,
    replace: 'date: new Date(orderDate)',
    description: 'استخدام Date object في البيانات'
  },
  {
    search: /date: new Date\(formState\.date\)\.toISOString\(\)/g,
    replace: 'date: new Date(formState.date)',
    description: 'استخدام Date object في form state'
  },
  {
    search: /date: new Date\(receiptFormState\.date\)\.toISOString\(\)/g,
    replace: 'date: new Date(receiptFormState.date)',
    description: 'استخدام Date object في receipt form'
  },
  {
    search: /date: new Date\(deliveryOrderDate\)\.toISOString\(\)/g,
    replace: 'date: new Date(deliveryOrderDate)',
    description: 'استخدام Date object في delivery order'
  },
  {
    search: /date: new Date\(returnOrder\.date\)\.toISOString\(\)/g,
    replace: 'date: new Date(returnOrder.date)',
    description: 'استخدام Date object في return order'
  },
  
  // Fix API responses and data processing
  {
    search: /uploadedAt: sale\.createdAt \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'uploadedAt: sale.createdAt || new Date()',
    description: 'استخدام Date object للـ uploadedAt'
  },
  {
    search: /createdAt: loadedSale\.createdAt \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'createdAt: loadedSale.createdAt || new Date()',
    description: 'استخدام Date object للـ createdAt'
  },
  {
    search: /uploadedAt: order\.createdAt \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'uploadedAt: order.createdAt || new Date()',
    description: 'استخدام Date object للـ uploadedAt في orders'
  },
  {
    search: /createdAt: originalOrder\?\.createdAt \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'createdAt: originalOrder?.createdAt || new Date()',
    description: 'استخدام Date object للـ createdAt في original order'
  },
  
  // Fix date assignments in objects
  {
    search: /date: new Date\(\)\.toISOString\(\)/g,
    replace: 'date: new Date()',
    description: 'استخدام Date object للتاريخ الحالي'
  },
  {
    search: /expiryDate: new Date\(Date\.now\(\) \+ 365 \* 24 \* 60 \* 60 \* 1000\)\.toISOString\(\)/g,
    replace: 'expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)',
    description: 'استخدام Date object للـ expiryDate'
  },
  {
    search: /lastSale: \{ clientName: '.*', soNumber: '.*', date: new Date\(\)\.toISOString\(\) \}/g,
    replace: 'lastSale: { clientName: \'عميل تجريبي\', soNumber: \'SO-001\', date: new Date() }',
    description: 'استخدام Date object في lastSale'
  },
  {
    search: /warrantyInfo: \{ status: '.*', expiryDate: new Date\(\)\.toISOString\(\), remaining: '.*' \}/g,
    replace: 'warrantyInfo: { status: \'ضمان ساري\', expiryDate: new Date(), remaining: \'12 شهراً\' }',
    description: 'استخدام Date object في warrantyInfo'
  },
  
  // Fix specific API patterns
  {
    search: /return date\.toISOString\(\);/g,
    replace: 'return date;',
    description: 'إرجاع Date object مباشرة'
  },
  {
    search: /lastMessageDate: new Date\(0\)\.toISOString\(\)/g,
    replace: 'lastMessageDate: new Date(0)',
    description: 'استخدام Date object للـ lastMessageDate'
  },
  
  // Fix test data
  {
    search: /date: new Date\(Date\.now\(\) - \d+\)\.toISOString\(\)/g,
    replace: function(match) {
      return match.replace(/\.toISOString\(\)/, '');
    },
    description: 'استخدام Date object في test data'
  },
  
  // Fix form setters (keep slice for form inputs)
  {
    search: /setOrderDate\(new Date\(order\.date\)\.toISOString\(\)\.slice\(0, 16\)\)/g,
    replace: 'setOrderDate(new Date(order.date).toISOString().slice(0, 16))',
    description: 'الاحتفاظ بـ slice للـ form inputs'
  },
  {
    search: /setDeliveryOrderDate\(new Date\(order\.date\)\.toISOString\(\)\.slice\(0, 16\)\)/g,
    replace: 'setDeliveryOrderDate(new Date(order.date).toISOString().slice(0, 16))',
    description: 'الاحتفاظ بـ slice للـ form inputs'
  },
  
  // Fix advanced search
  {
    search: /onSelect=\{\(date\) => updateFilter\('dateFrom', date\?\.toISOString\(\)\.split\('T'\)\[0\]\)\}/g,
    replace: 'onSelect={(date) => updateFilter(\'dateFrom\', date)}',
    description: 'تمرير Date object مباشرة في updateFilter'
  },
  {
    search: /onSelect=\{\(date\) => updateFilter\('dateTo', date\?\.toISOString\(\)\.split\('T'\)\[0\]\)\}/g,
    replace: 'onSelect={(date) => updateFilter(\'dateTo\', date)}',
    description: 'تمرير Date object مباشرة في updateFilter'
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    
    if (typeof fix.search === 'function') {
      // Handle function-based replacements
      content = content.replace(fix.search, fix.replace);
    } else {
      content = content.replace(fix.search, fix.replace);
    }
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixToIsoStringUsage() {
  console.log('🔧 إصلاح مشاكل TO_ISO_STRING_USAGE...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of toIsoStringFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${toIsoStringFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        if (applyFixes(filePath, toIsoStringFixes)) {
          totalFixed += toIsoStringFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 10 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${toIsoStringFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح TO_ISO_STRING_USAGE:');
    console.log('='.repeat(45));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${toIsoStringFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: toIsoStringFiles.length,
      fixes: toIsoStringFixes
    };

    fs.writeFileSync('to-iso-string-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح TO_ISO_STRING_USAGE في: to-iso-string-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح مشاكل TO_ISO_STRING_USAGE بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل TO_ISO_STRING_USAGE للإصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح TO_ISO_STRING_USAGE:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixToIsoStringUsage()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح TO_ISO_STRING_USAGE');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح TO_ISO_STRING_USAGE:', error);
      process.exit(1);
    });
}

module.exports = { fixToIsoStringUsage };
