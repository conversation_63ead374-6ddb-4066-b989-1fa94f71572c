/**
 * Basic Data Seeding Script
 * Date: 2025-08-04
 * Description: Create basic data after schema changes
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedBasicData() {
  console.log('🌱 إنشاء البيانات الأساسية...\n');

  try {
    // 1. Create basic permissions
    console.log('1️⃣ إنشاء الصلاحيات الأساسية...');
    const permissions = [
      { name: 'messaging.view', displayName: 'عرض الرسائل', category: 'messaging', description: 'عرض الرسائل' },
      { name: 'messaging.create', displayName: 'إنشاء رسائل', category: 'messaging', description: 'إنشاء رسائل' },
      { name: 'messaging.edit', displayName: 'تعديل الرسائل', category: 'messaging', description: 'تعديل الرسائل' },
      { name: 'messaging.delete', displayName: 'حذف الرسائل', category: 'messaging', description: 'حذف الرسائل' },
      { name: 'messaging.viewAll', displayName: 'عرض جميع الرسائل', category: 'messaging', description: 'عرض جميع الرسائل' },
      { name: 'requests.view', displayName: 'عرض الطلبات', category: 'requests', description: 'عرض الطلبات' },
      { name: 'requests.create', displayName: 'إنشاء طلبات', category: 'requests', description: 'إنشاء طلبات' },
      { name: 'requests.edit', displayName: 'تعديل الطلبات', category: 'requests', description: 'تعديل الطلبات' },
      { name: 'requests.delete', displayName: 'حذف الطلبات', category: 'requests', description: 'حذف الطلبات' },
      { name: 'admin.full', displayName: 'صلاحيات إدارية كاملة', category: 'admin', description: 'صلاحيات إدارية كاملة' }
    ];

    for (const permission of permissions) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {},
        create: permission
      });
    }
    console.log(`   ✅ تم إنشاء ${permissions.length} صلاحية\n`);

    // 2. Create basic warehouses
    console.log('2️⃣ إنشاء المخازن الأساسية...');
    const warehouses = [
      { name: 'المخزن الرئيسي', type: 'main', location: 'الرياض' },
      { name: 'مخزن الفرع الشرقي', type: 'branch', location: 'الدمام' },
      { name: 'مخزن الفرع الغربي', type: 'branch', location: 'جدة' }
    ];

    for (const warehouse of warehouses) {
      const existing = await prisma.warehouse.findFirst({
        where: { name: warehouse.name }
      });

      if (!existing) {
        await prisma.warehouse.create({
          data: warehouse
        });
      }
    }
    console.log(`   ✅ تم إنشاء ${warehouses.length} مخزن\n`);

    // 3. Create admin user
    console.log('3️⃣ إنشاء المستخدم الإداري...');
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'مدير النظام',
        username: 'admin',
        role: 'admin',
        status: 'Active',
        branchLocation: 'الرياض'
      }
    });

    // 4. Assign permissions to admin
    console.log('4️⃣ تعيين الصلاحيات للمدير...');
    const allPermissions = await prisma.permission.findMany();

    for (const permission of allPermissions) {
      const existing = await prisma.userPermission.findFirst({
        where: {
          userId: adminUser.id,
          permissionId: permission.id
        }
      });

      if (!existing) {
        await prisma.userPermission.create({
          data: {
            userId: adminUser.id,
            permissionId: permission.id
          }
        });
      }
    }

    // 5. Assign warehouse access to admin
    console.log('5️⃣ تعيين صلاحيات المخازن للمدير...');
    const allWarehouses = await prisma.warehouse.findMany();

    for (const warehouse of allWarehouses) {
      const existing = await prisma.userWarehouseAccess.findFirst({
        where: {
          userId: adminUser.id,
          warehouseId: warehouse.id
        }
      });

      if (!existing) {
        await prisma.userWarehouseAccess.create({
          data: {
            userId: adminUser.id,
            warehouseId: warehouse.id,
            accessType: 'admin',
            canTransfer: true,
            canAudit: true
          }
        });
      }
    }

    // 6. Create system settings
    console.log('6️⃣ إنشاء إعدادات النظام...');
    await prisma.systemSetting.upsert({
      where: { id: 1 },
      update: {},
      create: {
        id: 1,
        companyNameAr: 'DeviceFlow',
        companyNameEn: 'DeviceFlow',
        addressAr: 'الشارع الرئيسي، الرياض، السعودية',
        addressEn: 'Main Street, Riyadh, Saudi Arabia',
        phone: '+966501234567',
        email: '<EMAIL>',
        website: 'www.deviceflow.com',
        footerTextAr: 'شكراً لتعاملكم معنا',
        footerTextEn: 'Thank you for your business',
        reportSettings: {
          showLogo: true,
          logoPosition: 'center',
          headerStyle: 'corporate',
          footerStyle: 'detailed',
          colorScheme: 'default',
          fontSize: 'medium',
          pageOrientation: 'portrait',
          language: 'both',
          includeTimestamp: true,
          includeWatermark: false
        }
      }
    });

    console.log('🎉 تم إنشاء البيانات الأساسية بنجاح!');
    console.log('\n📊 ملخص البيانات المنشأة:');
    console.log(`- الصلاحيات: ${permissions.length}`);
    console.log(`- المخازن: ${warehouses.length}`);
    console.log(`- المستخدمين: 1 (admin)`);
    console.log(`- إعدادات النظام: 1`);

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeding
if (require.main === module) {
  seedBasicData()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إنشاء البيانات الأساسية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إنشاء البيانات الأساسية:', error);
      process.exit(1);
    });
}

module.exports = { seedBasicData };
