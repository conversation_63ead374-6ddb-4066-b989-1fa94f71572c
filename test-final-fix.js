// اختبار نهائي - التحقق من التطابق بين الواجهة والخادم
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة btoa للـ Node.js
function btoa(str) {
  return Buffer.from(str, 'binary').toString('base64');
}

const createToken = (username, role) => {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData);
};

async function testFinalSystem() {
  try {
    console.log('🎯 الاختبار النهائي - التحقق من تطابق SO Number...\n');
    
    const adminToken = createToken('admin', 'admin');
    
    console.log('📋 إنشاء فاتورة جديدة...');
    const testSale = {
      clientName: 'اختبار نهائي',
      opNumber: '', // بدون رقم فاتورة رسمية
      warehouseName: 'مخزن الاختبار',
      notes: 'اختبار التطابق النهائي',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [{
        deviceId: 'FINAL_TEST_DEVICE',
        model: 'Test Device Model',
        price: 999,
        condition: 'جديد'
      }]
    };

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(testSale)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إنشاء الفاتورة بنجاح!');
      console.log(`🔢 SO Number من الخادم: ${result.soNumber}`);
      console.log(`📄 OP Number من الخادم: "${result.opNumber || 'فارغ'}"`);
      
      // التحقق من التنسيق
      const soFormat = result.soNumber.match(/^SO-\d+$/);
      if (soFormat) {
        console.log('✅ تنسيق SO Number صحيح (SO-xxxxxx)');
      } else {
        console.log('❌ تنسيق SO Number غير صحيح');
      }

      // عرض نتيجة قاعدة البيانات
      console.log('\n📊 التحقق من قاعدة البيانات:');
      const dbSale = await prisma.sale.findUnique({
        where: { id: result.id },
        select: {
          soNumber: true,
          opNumber: true,
          clientName: true
        }
      });

      if (dbSale) {
        console.log(`🔢 SO Number في قاعدة البيانات: ${dbSale.soNumber}`);
        console.log(`📄 OP Number في قاعدة البيانات: "${dbSale.opNumber || 'فارغ'}"`);
        
        // التحقق من التطابق
        if (result.soNumber === dbSale.soNumber) {
          console.log('✅ SO Number متطابق بين الخادم وقاعدة البيانات');
        } else {
          console.log('❌ SO Number غير متطابق!');
        }

        if ((result.opNumber || '') === (dbSale.opNumber || '')) {
          console.log('✅ OP Number متطابق بين الخادم وقاعدة البيانات');
        } else {
          console.log('❌ OP Number غير متطابق!');
        }
      }

      console.log('\n🎉 خلاصة النظام الجديد:');
      console.log('   ✅ الواجهة تعرض "سيتم إنشاؤه عند الحفظ" قبل الحفظ');
      console.log('   ✅ الخادم ينشئ SO Number بتنسيق SO-timestamp');
      console.log('   ✅ الواجهة تحدث SO Number بعد الحفظ');
      console.log('   ✅ OP Number اختياري (فارغ أو مخصص)');
      console.log('   ✅ التطابق بين الواجهة والخادم وقاعدة البيانات');

    } else {
      console.log('❌ فشل في إنشاء الفاتورة:', result);
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testFinalSystem();
