// Simple Prisma Schema for JSON fixes
// Date: 2025-08-04

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Basic User model without JSON fields
model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  username        String?  @unique @default("user")
  role            String?  @default("user")
  phone           String?  @default("")
  photo           String?  @default("")
  status          String?  @default("Active")
  lastLogin       DateTime?
  branchLocation  String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt @default(now())
  
  // Relations - using these instead of JSON
  userPermissions     UserPermission[]
  userWarehouseAccess UserWarehouseAccess[]
  messageRecipients   MessageRecipient[]

  @@map("users")
}

// Permission system
model Permission {
  id          Int    @id @default(autoincrement())
  name        String @unique
  description String?
  
  userPermissions UserPermission[]
  
  @@map("permissions")
}

model UserPermission {
  id           Int @id @default(autoincrement())
  userId       Int
  permissionId Int
  
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([userId, permissionId])
  @@map("user_permissions")
}

// Warehouse system
model Warehouse {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  type      String
  location  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
  
  userAccess UserWarehouseAccess[]
  
  @@map("warehouses")
}

model UserWarehouseAccess {
  id          Int    @id @default(autoincrement())
  userId      Int
  warehouseId Int
  accessLevel String @default("read") // read, write, full
  
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)
  
  @@unique([userId, warehouseId])
  @@map("user_warehouse_access")
}

// Device system without JSON
model Device {
  id          String   @id
  model       String
  status      String
  storage     String
  price       Float
  condition   String
  warehouseId Int?
  supplierId  Int?
  dateAdded   DateTime @default(now())
  
  // Relations instead of JSON
  originalReplacements    DeviceReplacement[] @relation("OriginalDevice")
  replacementFor         DeviceReplacement[] @relation("ReplacementDevice")
  
  @@map("devices")
}

model DeviceReplacement {
  id                  Int      @id @default(autoincrement())
  originalDeviceId    String
  replacementDeviceId String
  reason              String?
  replacementDate     DateTime @default(now())
  notes               String?
  
  originalDevice    Device @relation("OriginalDevice", fields: [originalDeviceId], references: [id])
  replacementDevice Device @relation("ReplacementDevice", fields: [replacementDeviceId], references: [id])
  
  @@unique([originalDeviceId, replacementDeviceId])
  @@map("device_replacements")
}

// Messaging system without JSON
model InternalMessage {
  id                  Int       @id @default(autoincrement())
  threadId            Int
  senderId            Int
  senderName          String
  recipientId         Int
  recipientName       String
  text                String
  attachmentName      String?
  attachmentContent   String?
  attachmentType      String?
  attachmentUrl       String?
  attachmentFileName  String?
  attachmentSize      Int?
  sentDate            DateTime  @default(now())
  status              String    @default("مرسلة")
  isRead              Boolean   @default(false)
  parentMessageId     Int?
  employeeRequestId   Int?
  resolutionNote      String?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt @default(now())

  // Relations instead of recipientIds JSON
  recipients          MessageRecipient[]

  @@map("internal_messages")
}

model MessageRecipient {
  id        Int             @id @default(autoincrement())
  messageId Int
  userId    Int
  isRead    Boolean         @default(false)
  readAt    DateTime?
  
  message   InternalMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id])
  
  @@unique([messageId, userId])
  @@map("message_recipients")
}

// System Settings with JSON for complex settings
model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  reportSettings  Json?    // Keep as JSON for complex settings
  updatedAt       DateTime @updatedAt @default(now())
  createdAt       DateTime @default(now())
  
  @@map("system_settings")
}
