// اختبار التغييرات الجديدة - SO = رقم بسيط، OP = اختياري
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة btoa للـ Node.js
function btoa(str) {
  return Buffer.from(str, 'binary').toString('base64');
}

const createToken = (username, role) => {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData);
};

async function testNewSystem() {
  try {
    console.log('🧪 اختبار النظام الجديد...\n');
    
    const adminToken = createToken('admin', 'admin');
    
    console.log('📋 اختبار 1: فاتورة بدون رقم فاتورة رسمية');
    const saleWithoutOP = {
      clientName: 'عميل بدون رقم فاتورة رسمية',
      opNumber: '', // فارغ
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار بدون رقم فاتورة رسمية',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [{
        deviceId: 'TEST_NO_OP',
        model: 'Samsung A54',
        price: 800,
        condition: 'جديد'
      }]
    };

    const response1 = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(saleWithoutOP)
    });

    const result1 = await response1.json();
    
    if (response1.ok) {
      console.log('✅ نجح الاختبار الأول!');
      console.log(`   SO Number (رقم أمر البيع): ${result1.soNumber}`);
      console.log(`   OP Number (رقم فاتورة رسمية): "${result1.opNumber || 'فارغ'}"`);
    } else {
      console.log('❌ فشل الاختبار الأول:', result1);
      return;
    }

    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('\n📋 اختبار 2: فاتورة مع رقم فاتورة رسمية');
    const saleWithOP = {
      clientName: 'عميل مع رقم فاتورة رسمية',
      opNumber: 'INV-2025-001', // رقم مخصص
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار مع رقم فاتورة رسمية',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [{
        deviceId: 'TEST_WITH_OP',
        model: 'iPhone 14',
        price: 2000,
        condition: 'جديد'
      }]
    };

    const response2 = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(saleWithOP)
    });

    const result2 = await response2.json();
    
    if (response2.ok) {
      console.log('✅ نجح الاختبار الثاني!');
      console.log(`   SO Number (رقم أمر البيع): ${result2.soNumber}`);
      console.log(`   OP Number (رقم فاتورة رسمية): "${result2.opNumber}"`);
    } else {
      console.log('❌ فشل الاختبار الثاني:', result2);
      return;
    }

    // عرض النتائج من قاعدة البيانات
    console.log('\n📊 النتائج النهائية من قاعدة البيانات:');
    console.log('═══════════════════════════════════════════════════════════════════════════════════');
    console.log('رقم أمر البيع (SO)    | رقم فاتورة رسمية (OP)      | العميل');
    console.log('───────────────────────────────────────────────────────────────────────────────────');

    const sales = await prisma.sale.findMany({
      orderBy: { id: 'desc' },
      take: 3,
      select: {
        soNumber: true,
        opNumber: true,
        clientName: true
      }
    });

    sales.forEach(sale => {
      const soNumber = sale.soNumber.padEnd(20);
      const opNumber = (sale.opNumber || 'فارغ').padEnd(25);
      const clientName = sale.clientName;
      
      console.log(`${soNumber} | ${opNumber} | ${clientName}`);
    });

    console.log('═══════════════════════════════════════════════════════════════════════════════════\n');
    
    console.log('🎉 النظام الآن يعمل كما طلبت:');
    console.log('   - رقم أمر البيع (SO): رقم بسيط متسلسل للعمليات الداخلية');
    console.log('   - رقم فاتورة رسمية (OP): اختياري للمستخدم');

    // التحقق من التسلسل
    const firstSO = parseInt(result1.soNumber);
    const secondSO = parseInt(result2.soNumber);
    
    if (secondSO === firstSO + 1) {
      console.log('✅ أرقام أوامر البيع متسلسلة بشكل صحيح!');
    } else {
      console.log('⚠️  قد تكون هناك مشكلة في التسلسل');
    }

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testNewSystem();
