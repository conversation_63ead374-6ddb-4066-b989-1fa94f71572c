import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInLongTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';
import {
  getBatchSize,
  shouldUseBatchProcessing,
  calculateBatchCount,
  chunkArray,
  logPerformanceInfo,
  createProgressMessage
} from '@/lib/database-config';

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.supplierId || !newOrder.items || !Array.isArray(newOrder.items) || newOrder.items.length === 0) {
      return NextResponse.json(
        { error: 'Supplier ID and items are required' },
        { status: 400 }
      );
    }

    // التحقق من ضرورة استخدام المعالجة بالدفعات
    if (!shouldUseBatchProcessing(newOrder.items.length)) {
      return NextResponse.json(
        {
          message: 'Use regular supply API for small quantities',
          shouldUseRegularAPI: true,
          itemCount: newOrder.items.length
        },
        { status: 200 }
      );
    }

    console.log(`Starting batch supply order processing for ${newOrder.items.length} devices`);

    // تنفيذ العملية داخل معاملة طويلة
    const result = await executeInLongTransaction(async (tx) => {
      // استخدام رقم الأمر المُرسل من الواجهة الأمامية، أو إنشاء رقم جديد إذا لم يكن متوفراً
      let supplyOrderId = newOrder.supplyOrderId;
      if (!supplyOrderId) {
        supplyOrderId = await generateUniqueId(tx, 'supplyOrder', 'SUP-');
      } else {
        // التحقق من عدم وجود رقم مكرر
        const existingOrder = await tx.supplyOrder.findUnique({
          where: { supplyOrderId }
        });
        if (existingOrder) {
          supplyOrderId = await generateUniqueId(tx, 'supplyOrder', 'SUP-');
        }
      }

      // Create the order in the database
      const order = await tx.supplyOrder.create({
        data: {
          supplyOrderId,
          supplierId: newOrder.supplierId,
          invoiceNumber: newOrder.invoiceNumber || null,
          supplyDate: newOrder.supplyDate,
          warehouseId: newOrder.warehouseId || null,
          employeeName: newOrder.employeeName || authResult.user!.username,
          notes: newOrder.notes || '',
          invoiceFileName: newOrder.invoiceFileName || null,
          referenceNumber: newOrder.referenceNumber || null,
          status: newOrder.status || 'completed'
        }
      });

      // Create supply order items
      if (newOrder.items && Array.isArray(newOrder.items)) {
        try {
          const itemsData = newOrder.items.map((item: any) => ({
            supplyOrderId: order.id,
            imei: item.imei || '',
            manufacturer: item.manufacturer || '',
            model: item.model || '',
            condition: item.condition || 'جديد'
          }));

          console.log(`Creating ${itemsData.length} supply order items...`);

          await tx.supplyOrderItem.createMany({
            data: itemsData,
            skipDuplicates: true
          });

          console.log(`Successfully created supply order items`);
        } catch (itemsError) {
          console.error('Failed to create supply order items:', itemsError);
          throw new Error(`Failed to create supply order items: ${itemsError instanceof Error ? itemsError.message : String(itemsError)}`);
        }
      }

      // معالجة الأجهزة بالدفعات المحسنة
      const processedDevices = await processBatchDevices(tx, newOrder);

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE_SUPPLY_ORDER_BATCH',
        details: `Created supply order ${supplyOrderId} with ${newOrder.items.length} devices (${processedDevices.created} created, ${processedDevices.existing} existing)`
      });

      // إضافة عناصر الأمر للاستجابة
      const orderWithItems = await tx.supplyOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return {
        order: orderWithItems,
        processedDevices,
        supplyOrderId
      };
    });

    console.log(`Batch supply order completed: ${result.processedDevices.created} devices created, ${result.processedDevices.existing} existing`);

    return NextResponse.json({
      success: true,
      order: result.order,
      processedDevices: result.processedDevices,
      message: `Supply order created successfully with ${result.processedDevices.created} new devices`
    });

  } catch (error) {
    console.error('Failed to create batch supply order:', error);

    // تسجيل تفصيلي للخطأ
    let errorMessage = 'Failed to create supply order';
    let errorDetails = '';

    if (error instanceof Error) {
      errorDetails = error.message;
      console.error('Error details:', error.stack);
    } else {
      errorDetails = String(error);
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        timestamp: new Date(),
        apiType: 'batch'
      },
      { status: 500 }
    );
  }
}

// دالة معالجة الأجهزة بالدفعات المحسنة
async function processBatchDevices(tx: any, newOrder: any) {
  const validItems = newOrder.items.filter((item: any) => item.imei);
  const batchSize = getBatchSize(validItems.length);
  const totalBatches = calculateBatchCount(validItems.length, batchSize);

  let totalCreated = 0;
  let totalExisting = 0;
  let totalErrors = 0;
  const startTime = Date.now();

  console.log(`Processing ${validItems.length} devices in ${totalBatches} batches of ${batchSize}`);
  
  // تقسيم الأجهزة إلى دفعات باستخدام الدالة المحسنة
  const batches = chunkArray(validItems, batchSize);

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    const batchNumber = batchIndex + 1;

    console.log(createProgressMessage(batchNumber, totalBatches, 'معالجة دفعة الأجهزة'));
    
    try {
      // الحصول على الأجهزة الموجودة في هذه الدفعة
      const existingDevices = await tx.device.findMany({
        where: {
          id: {
            in: batch.map((item: any) => item.imei)
          }
        },
        select: { id: true }
      });
      
      const existingDeviceIds = new Set(existingDevices.map((d: any) => d.id));
      totalExisting += existingDevices.length;
      
      // إنشاء الأجهزة الجديدة فقط
      const newDevices = batch
        .filter((item: any) => !existingDeviceIds.has(item.imei))
        .map((item: any) => ({
          id: item.imei,
          model: `${item.manufacturer} ${item.model}`,
          status: 'متاح للبيع',
          storage: 'N/A',
          price: 0,
          condition: item.condition || 'جديد',
          warehouseId: newOrder.warehouseId,
          supplierId: newOrder.supplierId
        }));
      
      if (newDevices.length > 0) {
        await tx.device.createMany({
          data: newDevices,
          skipDuplicates: true
        });
        totalCreated += newDevices.length;
        console.log(`Batch ${batchNumber}: Created ${newDevices.length} new devices, ${existingDevices.length} already existed`);
      } else {
        console.log(`Batch ${batchNumber}: All ${batch.length} devices already exist`);
      }
      
    } catch (batchError) {
      console.error(`Failed to process batch ${batchNumber}:`, batchError);
      totalErrors += batch.length;
      
      // في حالة فشل دفعة، نحاول معالجة الأجهزة واحد تلو الآخر
      for (const item of batch) {
        try {
          const existingDevice = await tx.device.findUnique({
            where: { id: item.imei }
          });

          if (!existingDevice) {
            await tx.device.create({
              data: {
                id: item.imei,
                model: `${item.manufacturer} ${item.model}`,
                status: 'متاح للبيع',
                storage: 'N/A',
                price: 0,
                condition: item.condition || 'جديد',
                warehouseId: newOrder.warehouseId,
                supplierId: newOrder.supplierId
              }
            });
            totalCreated++;
            totalErrors--; // تقليل العدد لأن الجهاز تم إنشاؤه بنجاح
          } else {
            totalExisting++;
            totalErrors--; // تقليل العدد لأن الجهاز موجود
          }
        } catch (deviceError) {
          console.error(`Failed to create device ${item.imei}:`, deviceError);
          // العدد يبقى في totalErrors
        }
      }
    }
  }

  // تسجيل معلومات الأداء
  const duration = Date.now() - startTime;
  logPerformanceInfo('Batch Device Processing', validItems.length, duration);

  console.log(`Batch processing completed: ${totalCreated} created, ${totalExisting} existing, ${totalErrors} errors in ${duration}ms`);

  return {
    created: totalCreated,
    existing: totalExisting,
    errors: totalErrors,
    total: validItems.length,
    duration,
    batchSize,
    totalBatches
  };
}
