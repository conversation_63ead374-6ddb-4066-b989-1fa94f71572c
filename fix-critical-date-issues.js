/**
 * Fix Critical Date Type Issues Script
 * Date: 2025-08-04
 * Description: Fix the most critical date type mismatches in the system
 */

const fs = require('fs');
const path = require('path');

// Critical fixes to apply
const criticalFixes = [
  {
    file: 'lib/types.ts',
    fixes: [
      {
        search: /dateAdded: string;.*$/gm,
        replace: 'dateAdded: Date;',
        description: 'تحويل dateAdded إلى Date'
      },
      {
        search: /date: string;.*$/gm,
        replace: 'date: Date;',
        description: 'تحويل date إلى Date'
      },
      {
        search: /createdAt: string;.*$/gm,
        replace: 'createdAt: Date;',
        description: 'تحويل createdAt إلى Date'
      },
      {
        search: /sentDate: string;.*$/gm,
        replace: 'sentDate: Date;',
        description: 'تحويل sentDate إلى Date'
      },
      {
        search: /requestDate: string;.*$/gm,
        replace: 'requestDate: Date;',
        description: 'تحويل requestDate إلى Date'
      },
      {
        search: /processedDate\?: string;.*$/gm,
        replace: 'processedDate?: Date;',
        description: 'تحويل processedDate إلى Date'
      },
      {
        search: /lastLogin\?: string;.*$/gm,
        replace: 'lastLogin?: Date;',
        description: 'تحويل lastLogin إلى Date'
      },
      {
        search: /replacementDate\?: string;.*$/gm,
        replace: 'replacementDate?: Date;',
        description: 'تحويل replacementDate إلى Date'
      }
    ]
  },
  {
    file: 'lib/date-utils.ts',
    fixes: [
      {
        search: /date: Date \| string \| null \| undefined/g,
        replace: 'date: Date | null | undefined',
        description: 'إزالة string من أنواع البيانات'
      }
    ]
  }
];

// Common patterns to fix across all files
const commonFixes = [
  {
    search: /new Date\(\)\.toISOString\(\)/g,
    replace: 'new Date()',
    description: 'إزالة toISOString() غير الضرورية'
  },
  {
    search: /\.toLocaleDateString\('ar-EG'\)/g,
    replace: "formatDate(date, { arabic: true })",
    description: 'استخدام formatDate الموحدة'
  },
  {
    search: /\.toLocaleDateString\('ar-SA'\)/g,
    replace: "formatDate(date, { arabic: true })",
    description: 'استخدام formatDate الموحدة'
  },
  {
    search: /\.toLocaleString\('ar-EG'\)/g,
    replace: "formatDateTime(date, { arabic: true })",
    description: 'استخدام formatDateTime الموحدة'
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${filePath}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    console.log(`   📁 نسخة احتياطية: ${backupPath}\n`);
    
    return true;
  }

  return false;
}

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${filePath}`);
    return true;
  }

  return false;
}

async function fixCriticalIssues() {
  console.log('🚀 بدء إصلاح المشاكل الحرجة في أنواع البيانات للتواريخ...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    // Apply critical fixes to specific files
    console.log('1️⃣ إصلاح الملفات الحرجة...');
    criticalFixes.forEach(({ file, fixes }) => {
      const filePath = path.join(process.cwd(), file);
      if (applyFixes(filePath, fixes)) {
        totalFixed += fixes.length;
        filesModified.push(file);
      }
    });

    // Apply common fixes to key files
    console.log('2️⃣ إصلاح الأنماط الشائعة...');
    const keyFiles = [
      'context/store.tsx',
      'app/api/internal-messages/route.ts',
      'app/api/employee-requests/route.ts',
      'app/api/returns/route.ts',
      'app/(main)/messaging/page.tsx'
    ];

    keyFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply common fixes
        if (applyFixes(filePath, commonFixes)) {
          totalFixed += commonFixes.length;
          if (!filesModified.includes(file)) {
            filesModified.push(file);
          }
        }
      }
    });

    // Generate summary
    console.log('📊 ملخص الإصلاحات:');
    console.log('='.repeat(30));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
      });
    }

    // Create fix report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      criticalFixes: criticalFixes,
      commonFixes: commonFixes
    };

    fs.writeFileSync('critical-date-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير الإصلاحات في: critical-date-fixes-report.json');

    // Next steps
    console.log('\n🔄 الخطوات التالية:');
    console.log('1. مراجعة الملفات المعدلة للتأكد من صحة الإصلاحات');
    console.log('2. تشغيل اختبارات النظام');
    console.log('3. تطبيق الإصلاحات على باقي الملفات');
    console.log('4. تحديث الوثائق');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح المشاكل الحرجة بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح أو تم إصلاحها مسبقاً');
    }

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixCriticalIssues()
    .then(() => {
      console.log('\n✅ تم الانتهاء من الإصلاحات الحرجة');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في الإصلاحات:', error);
      process.exit(1);
    });
}

module.exports = { fixCriticalIssues, applyFixes, addDateUtilsImport };
