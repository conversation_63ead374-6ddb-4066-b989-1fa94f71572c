'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  FileText,
  User,
  Clock,
  Star,
  Archive,
  Download,
  RefreshCw
} from 'lucide-react';
import { format } from 'date-fns';
import { SearchFilters, SearchResult } from '@/lib/search-service';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface AdvancedSearchProps {
  onSearchResults: (results: SearchResult[], total: number) => void;
  onClearResults: () => void;
}

export default function AdvancedSearch({ onSearchResults, onClearResults }: AdvancedSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  const [isSearching, setIsSearching] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [quickSuggestions, setQuickSuggestions] = useState<SearchResult[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { toast } = useToast();

  // البحث السريع للاقتراحات
  const fetchQuickSuggestions = async (query: string) => {
    if (query.length < 2) {
      setQuickSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await fetch(`/api/search/quick?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        const suggestions = await response.json();
        setQuickSuggestions(suggestions);
        setShowSuggestions(true);
      }
    } catch (error) {
      console.error('خطأ في البحث السريع:', error);
    }
  };

  // تنفيذ البحث المتقدم
  const performSearch = async (page: number = 1) => {
    setIsSearching(true);
    try {
      const searchParams = new URLSearchParams();
      
      if (searchQuery.trim()) {
        searchParams.append('q', searchQuery.trim());
      }
      
      if (filters.status?.length) {
        filters.status.forEach(status => searchParams.append('status', status));
      }
      
      if (filters.priority?.length) {
        filters.priority.forEach(priority => searchParams.append('priority', priority));
      }
      
      if (filters.requestType?.length) {
        filters.requestType.forEach(type => searchParams.append('requestType', type));
      }
      
      if (filters.employeeName) {
        searchParams.append('employeeName', filters.employeeName);
      }
      
      if (filters.dateFrom) {
        searchParams.append('dateFrom', filters.dateFrom);
      }
      
      if (filters.dateTo) {
        searchParams.append('dateTo', filters.dateTo);
      }
      
      if (filters.hasAttachments !== undefined) {
        searchParams.append('hasAttachments', filters.hasAttachments.toString());
      }
      
      if (filters.isArchived !== undefined) {
        searchParams.append('isArchived', filters.isArchived.toString());
      }
      
      searchParams.append('page', page.toString());

      const response = await fetch(`/api/search/requests?${searchParams.toString()}`);
      
      if (response.ok) {
        const data = await response.json();
        onSearchResults(data.results, data.total);
        
        if (data.results.length === 0) {
          toast({
            title: 'لا توجد نتائج',
            description: 'لم يتم العثور على طلبات تطابق معايير البحث'
          });
        }
      } else {
        throw new Error('فشل في البحث');
      }
    } catch (error) {
      console.error('خطأ في البحث:', error);
      toast({
        title: 'خطأ في البحث',
        description: 'حدث خطأ أثناء البحث، يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      });
    } finally {
      setIsSearching(false);
      setShowSuggestions(false);
    }
  };

  // مسح البحث
  const clearSearch = () => {
    setSearchQuery('');
    setFilters({});
    setQuickSuggestions([]);
    setShowSuggestions(false);
    onClearResults();
  };

  // تحديث الفلاتر
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // معالجة تغيير البحث
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    
    // البحث السريع مع تأخير
    const timeoutId = setTimeout(() => {
      fetchQuickSuggestions(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // اختيار اقتراح
  const selectSuggestion = (suggestion: SearchResult) => {
    setSearchQuery(suggestion.requestNumber);
    setShowSuggestions(false);
    performSearch();
  };

  // عدد الفلاتر النشطة
  const activeFiltersCount = Object.values(filters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    return value !== undefined && value !== null && value !== '';
  }).length;

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 space-x-reverse">
          <Search className="h-5 w-5 text-primary" />
          <span>البحث المتقدم</span>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount} فلتر نشط
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* شريط البحث الرئيسي */}
        <div className="relative">
          <div className="flex space-x-2 space-x-reverse">
            <div className="relative flex-1">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder="ابحث برقم الطلب، اسم الموظف، أو المحتوى..."
                className="pr-10"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    performSearch();
                  }
                }}
              />
              
              {/* اقتراحات البحث السريع */}
              {showSuggestions && quickSuggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {quickSuggestions.map((suggestion) => (
                    <div
                      key={suggestion.id}
                      className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={() => selectSuggestion(suggestion)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm">{suggestion.requestNumber}</p>
                          <p className="text-xs text-gray-600">{suggestion.employeeName}</p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {suggestion.priority}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <Button onClick={() => performSearch()} disabled={isSearching}>
              {isSearching ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 ml-1" />
              فلاتر
            </Button>
            
            {(searchQuery || activeFiltersCount > 0) && (
              <Button variant="outline" onClick={clearSearch}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* الفلاتر المتقدمة */}
        {showFilters && (
          <div className="border-t pt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* فلتر الحالة */}
              <div>
                <Label className="text-sm font-medium">الحالة</Label>
                <Select
                  value={filters.status?.[0] || ''}
                  onValueChange={(value) => updateFilter('status', value ? [value] : [])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الحالات</SelectItem>
                    <SelectItem value="قيد المراجعة">قيد المراجعة</SelectItem>
                    <SelectItem value="قيد المراجعة المتقدمة">قيد المراجعة المتقدمة</SelectItem>
                    <SelectItem value="تم التنفيذ">تم التنفيذ</SelectItem>
                    <SelectItem value="مرفوض">مرفوض</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* فلتر الأولوية */}
              <div>
                <Label className="text-sm font-medium">الأولوية</Label>
                <Select
                  value={filters.priority?.[0] || ''}
                  onValueChange={(value) => updateFilter('priority', value ? [value] : [])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الأولوية" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأولويات</SelectItem>
                    <SelectItem value="عادي">عادي</SelectItem>
                    <SelectItem value="طاريء">طاريء</SelectItem>
                    <SelectItem value="طاريء جدا">طاريء جداً</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* فلتر نوع الطلب */}
              <div>
                <Label className="text-sm font-medium">نوع الطلب</Label>
                <Select
                  value={filters.requestType?.[0] || ''}
                  onValueChange={(value) => updateFilter('requestType', value ? [value] : [])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر النوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأنواع</SelectItem>
                    <SelectItem value="تعديل">تعديل</SelectItem>
                    <SelectItem value="حذف">حذف</SelectItem>
                    <SelectItem value="إعادة نظر">إعادة نظر</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* فلتر اسم الموظف */}
              <div>
                <Label className="text-sm font-medium">اسم الموظف</Label>
                <Input
                  value={filters.employeeName || ''}
                  onChange={(e) => updateFilter('employeeName', e.target.value)}
                  placeholder="ابحث باسم الموظف"
                />
              </div>

              {/* فلتر التاريخ من */}
              <div>
                <Label className="text-sm font-medium">من تاريخ</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-right">
                      <CalendarIcon className="ml-2 h-4 w-4" />
                      {filters.dateFrom ? format(new Date(filters.dateFrom), 'yyyy/MM/dd') : 'اختر التاريخ'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.dateFrom ? new Date(filters.dateFrom) : undefined}
                      onSelect={(date) => updateFilter('dateFrom', date)}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* فلتر التاريخ إلى */}
              <div>
                <Label className="text-sm font-medium">إلى تاريخ</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-right">
                      <CalendarIcon className="ml-2 h-4 w-4" />
                      {filters.dateTo ? format(new Date(filters.dateTo), 'yyyy/MM/dd') : 'اختر التاريخ'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.dateTo ? new Date(filters.dateTo) : undefined}
                      onSelect={(date) => updateFilter('dateTo', date)}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* خيارات إضافية */}
            <Separator />
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="hasAttachments"
                  checked={filters.hasAttachments || false}
                  onCheckedChange={(checked) => updateFilter('hasAttachments', checked)}
                />
                <Label htmlFor="hasAttachments" className="text-sm">
                  يحتوي على مرفقات
                </Label>
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="isArchived"
                  checked={filters.isArchived || false}
                  onCheckedChange={(checked) => updateFilter('isArchived', checked)}
                />
                <Label htmlFor="isArchived" className="text-sm">
                  البحث في الأرشيف
                </Label>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
