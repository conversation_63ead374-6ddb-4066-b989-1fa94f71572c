'use client';

import { useState, useMemo, useEffect, useRef } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';
import { DarkModeToggle } from './DarkModeToggle';
import './enhanced-styles.css';
import type {
  Device,
  FaultType,
  DamageType,
  DeviceStatus,
  MaintenanceResult,
  MaintenanceOrder,
  EmployeeRequestType,
  EmployeeRequestPriority,
  SystemSettings,
  DeliveryOrder,
  DeliveryOrderItem,
  EmployeeRequest,
} from '@/lib/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Wrench,
  Package,
  Inbox,
  Trash2,
  Send,
  Save,
  Printer,
  X,
  FolderOpen,
  File,
  FileText,
  RotateCcw,
  PlusCircle,
  Trash,
  MessageSquareQuote,
  Upload,
  FileSpreadsheet,
  PackageSearch,
  Search,
  ChevronsUpDown,
  Check,
  Bell,
  FileDown,
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { cn } from '@/lib/utils';
import MaintenanceMemo from './MaintenanceMemo';
import AttachmentsViewer from '@/components/AttachmentsViewer';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

type DeliveryFormItem = {
  device: Device;
  result: MaintenanceResult;
  fault?: string;
  damage?: string;
  notes?: string;
};

interface AttachmentFile {
  originalName: string;
  fileName: string;
  filePath: string;
  size: number;
  type: string;
  uploadedAt: string;
}

const initialRequestFormState = {
  requestType: 'تعديل' as EmployeeRequestType,
  priority: 'عادي' as EmployeeRequestPriority,
  notes: '',
  attachmentName: '',
};

export default function MaintenancePage() {
  const store = useStore();
  const currentUser = store.currentUser;
  const { isLoading, getAuthHeader } = store;
  
  // Add permission checks
  const { canCreate, canEdit, canDelete } = usePermission('maintenance');

  // 💾 مفاتيح المسودات
  const RECEIVE_DRAFT_KEY = `maintenance-receive-draft-${currentUser?.id || 'anonymous'}`;
  const DELIVERY_DRAFT_KEY = `maintenance-delivery-draft-${currentUser?.id || 'anonymous'}`;
  const { toast } = useToast();
  const { devices, warehouses } = store;

  const [orderNumber, setOrderNumber] = useState('');
  const [referenceNumber, setReferenceNumber] = useState('');
  const [employeeName, setEmployeeName] = useState(currentUser?.name || ''); // ربط المستخدم تلقائياً
  // تحسين تنسيق التاريخ
  // استخدم formatDateTime من date-utils بدلاً من هذه الدالة

  // 📝 وضع الإنشاء (Create Mode)
  const [isCreatingReceive, setIsCreatingReceive] = useState(false);
  const [isCreatingDelivery, setIsCreatingDelivery] = useState(false);

  // 💾 حالة المسودات
  const [isReceiveDraft, setIsReceiveDraft] = useState(false);
  const [isDeliveryDraft, setIsDeliveryDraft] = useState(false);
  const [draftRefresh, setDraftRefresh] = useState(0);

  // تتبع حالة المسودات
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsReceiveDraft(!!localStorage.getItem(RECEIVE_DRAFT_KEY));
      setIsDeliveryDraft(!!localStorage.getItem(DELIVERY_DRAFT_KEY));
    }
  }, [RECEIVE_DRAFT_KEY, DELIVERY_DRAFT_KEY, draftRefresh]);

  const [orderDate, setOrderDate] = useState(
    new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM
  );
  const [orderNotes, setOrderNotes] = useState('');
  const [attachmentName, setAttachmentName] = useState('');
  const [orderItems, setOrderItems] = useState<Device[]>([]);
  const [imeiToReceive, setImeiToReceive] = useState('');
  const attachmentInputRef = useRef<HTMLInputElement>(null);
  const imeiFileInputRef = useRef<HTMLInputElement>(null);

  // 📎 متغيرات المرفقات للاستلام
  const [receiveAttachments, setReceiveAttachments] = useState<AttachmentFile[]>([]);
  const [isReceiveAttachmentsModalOpen, setIsReceiveAttachmentsModalOpen] = useState(false);
  const receiveAttachmentsInputRef = useRef<HTMLInputElement>(null);

  const [isLoadMaintOrderDialogOpen, setIsLoadMaintOrderDialogOpen] =
    useState(false);
  const [loadedMaintOrder, setLoadedMaintOrder] =
    useState<MaintenanceOrder | null>(null);
  const [maintOrderToDelete, setMaintOrderToDelete] =
    useState<MaintenanceOrder | null>(null);
  const [requestMaintOrder, setRequestMaintOrder] =
    useState<MaintenanceOrder | null>(null);
  const [requestFormData, setRequestFormData] = useState(
    initialRequestFormState
    );
  const attachmentRequestInputRef = useRef<HTMLInputElement>(null);

  const [deliveryOrderNumber, setDeliveryOrderNumber] = useState('');
  const [deliveryReferenceNumber, setDeliveryReferenceNumber] = useState('');
  const [deliveryEmployeeName, setDeliveryEmployeeName] = useState(currentUser?.name || ''); // ربط المستخدم تلقائياً
  const [deliveryOrderDate, setDeliveryOrderDate] = useState(
    new Date().toISOString().split('T')[0]
    );
  const [deliveryAttachmentName, setDeliveryAttachmentName] = useState('');
  const [imeiToDeliver, setImeiToDeliver] = useState('');
  const [deliveryResult, setDeliveryResult] =
    useState<MaintenanceResult>('Repaired');
  const [deliveryFault, setDeliveryFault] = useState<FaultType | ''>('');
  const [customDeliveryFault, setCustomDeliveryFault] = useState('');
  const [deliveryDamage, setDeliveryDamage] = useState<DamageType | ''>('');
  const [customDeliveryDamage, setCustomDeliveryDamage] = useState('');
  const [deliveryItemNotes, setDeliveryItemNotes] = useState('');
  const [receivingEmployeeName, setReceivingEmployeeName] = useState('');
  const [deliveryWarehouseId, setDeliveryWarehouseId] = useState<number | undefined>(undefined); // New state for delivery warehouse
  const [deliveryItems, setDeliveryItems] = useState<DeliveryFormItem[]>([]);
  const deliveryAttachmentInputRef = useRef<HTMLInputElement>(null);
  const deliveryImeiFileInputRef = useRef<HTMLInputElement>(null);

  // 📎 متغيرات المرفقات للتسليم
  const [deliveryAttachments, setDeliveryAttachments] = useState<AttachmentFile[]>([]);
  const [isDeliveryAttachmentsModalOpen, setIsDeliveryAttachmentsModalOpen] = useState(false);
  const deliveryAttachmentsInputRef = useRef<HTMLInputElement>(null);

  const [isLoadDelivOrderDialogOpen, setIsLoadDelivOrderDialogOpen] =
    useState(false);
  const [loadedDelivOrder, setLoadedDelivOrder] =
    useState<DeliveryOrder | null>(null);
  const [delivOrderToDelete, setDelivOrderToDelete] =
    useState<DeliveryOrder | null>(null);
  const [requestDelivOrder, setRequestDelivOrder] =
    useState<DeliveryOrder | null>(null);

  // State for Browse Inventory
  const [browseModelFilter, setBrowseModelFilter] = useState('');
  const [isModelSearchOpen, setIsModelSearchOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<{
    needsMaintenance: { exists: boolean; warehouseNames: string[] };
    damaged: { exists: boolean; warehouseNames: string[] };
    defective: { exists: boolean; warehouseNames: string[] };
  } | null>(null);
  const [isSparePartRequestDialogOpen, setIsSparePartRequestDialogOpen] =
    useState(false);
  const [sparePartRequestNotes, setSparePartRequestNotes] = useState('');

  const [deviceForRequest, setDeviceForRequest] = useState<Device | null>(null);
  const [isDeviceRequestDialogOpen, setIsDeviceRequestDialogOpen] =
    useState(false);
  const [deviceRequestNotes, setDeviceRequestNotes] = useState('');

  // State for History/Reports Tab
  const [historyFilter, setHistoryFilter] = useState<{
    dateFrom: Date;
    dateTo: Date;
    result: MaintenanceResult | 'all';
    orderNumber: string;
  }>({
    dateFrom: '',
    dateTo: '',
    result: 'all',
    orderNumber: '',
  });
const [detailsModal, setDetailsModal] = useState<{
    isOpen: boolean;
    title: string;
    data: any[];
  }>({ isOpen: false, title: '', data: [] });




  // نظام الترقيم التسلسلي المحسن - يحافظ على نفس الرقم عند الحفظ
  useEffect(() => {
    if (loadedMaintOrder) {
      // عند تحميل أمر موجود، نحافظ على رقمه الأصلي
      setOrderNumber(loadedMaintOrder.orderNumber);
    } else if (!orderNumber || orderNumber === '') {
      // فقط عند عدم وجود رقم أمر، نولد رقماً جديداً
      const existingNumbers = (store.maintenanceOrders || [])
        .map((order) => {
          const match = order.orderNumber.match(/MAINT-(\d+)$/);
          return match ? parseInt(match[1], 10) : 0;
        })
        .filter(num => num > 0);

      const maxOrderNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : 0;
      setOrderNumber(`MAINT-${maxOrderNumber + 1}`);
    }
  }, [store.maintenanceOrders, loadedMaintOrder, orderNumber]);

  // تعيين اسم المستخدم تلقائياً ومنع تعديله
  useEffect(() => {
    if (currentUser?.name && (!employeeName || employeeName === '')) {
      setEmployeeName(currentUser.name);
    }
    if (currentUser?.name && (!deliveryEmployeeName || deliveryEmployeeName === '')) {
      setDeliveryEmployeeName(currentUser.name);
    }
  }, [currentUser, employeeName, deliveryEmployeeName]);

  // تعيين المخزن الافتراضي لأوامر التسليم
  useEffect(() => {
    if (warehouses.length > 0 && deliveryWarehouseId === undefined) {
      setDeliveryWarehouseId(warehouses[0].id);
    }
  }, [warehouses, deliveryWarehouseId]);

  useEffect(() => {
    // Check for resolved requests and update device status
    store.employeeRequests.forEach((request) => {
      if (
        request.status === 'تم التنفيذ' &&
        request.relatedOrderType === 'maintenance'
      ) {
        const order = store.maintenanceOrders.find(
          (o) => o.id === request.relatedOrderId
    );
        if (order) {
          const deviceIdMatch = request.notes.match(/الجهاز (\S+)/);
          if (deviceIdMatch) {
            const deviceId = deviceIdMatch[1];
            const device = store.devices.find((d) => d.id === deviceId);
            if (device && device.status === 'مراجعة الطلب من الإدارة') {
              store.updateDeviceStatus(deviceId, 'قيد الإصلاح');
            }
          }
        }
      }
    });
  }, [store.employeeRequests, store.devices, store.maintenanceOrders, store.updateDeviceStatus]);

  // نظام الترقيم التسلسلي المحسن لأوامر التسليم
  useEffect(() => {
    if (loadedDelivOrder) {
      // عند تحميل أمر موجود، نحافظ على رقمه الأصلي
      setDeliveryOrderNumber(loadedDelivOrder.deliveryOrderNumber);
    } else if (!deliveryOrderNumber || deliveryOrderNumber === '') {
      // فقط عند عدم وجود رقم أمر، نولد رقماً جديداً
      const existingNumbers = (store.deliveryOrders || [])
        .map((order) => {
          const match = order.deliveryOrderNumber.match(/DELIV-(\d+)$/);
          return match ? parseInt(match[1], 10) : 0;
        })
        .filter(num => num > 0);

      const maxOrderNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : 0;
      setDeliveryOrderNumber(`DELIV-${maxOrderNumber + 1}`);
    }
  }, [store.deliveryOrders, loadedDelivOrder, deliveryOrderNumber]);

  // 📎 دوال المرفقات
  const handleReceiveFileUpload = async (files: FileList) => {
    try {
      const formData = new FormData();
      Array.from(files).forEach(file => formData.append('files', file));
      formData.append('section', 'maintenance');

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: getAuthHeader(),
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setReceiveAttachments(prev => [...prev, ...result.files]);
        toast({
          title: 'تم إرفاق الملفات',
          description: result.message,
        });
      } else {
        toast({
          title: 'خطأ في رفع الملفات',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ في رفع الملفات',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    }
  };

  const handleDeliveryFileUpload = async (files: FileList) => {
    try {
      const formData = new FormData();
      Array.from(files).forEach(file => formData.append('files', file));
      formData.append('section', 'maintenance');

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: getAuthHeader(),
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setDeliveryAttachments(prev => [...prev, ...result.files]);
        toast({
          title: 'تم إرفاق الملفات',
          description: result.message,
        });
      } else {
        toast({
          title: 'خطأ في رفع الملفات',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ في رفع الملفات',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    }
  };

  // 💾 دوال المسودات
  const saveReceiveDraft = () => {
    const draftData = {
      referenceNumber,
      orderDate,
      orderNotes,
      attachmentName,
      orderItems,
      orderNumber,
      employeeName, // حفظ اسم المستخدم
      receiveAttachments,
      timestamp: new Date(),
    };
    localStorage.setItem(RECEIVE_DRAFT_KEY, JSON.stringify(draftData));
    setIsReceiveDraft(true);
    setDraftRefresh(prev => prev + 1);
    toast({
      title: 'تم حفظ المسودة',
      description: 'تم حفظ مسودة أمر الاستلام بنجاح',
    });
  };

  const continueReceiveDraft = () => {
    const savedDraft = localStorage.getItem(RECEIVE_DRAFT_KEY);
    if (savedDraft) {
      try {
        const draft = JSON.parse(savedDraft);
        setReferenceNumber(draft.referenceNumber || '');
        setOrderDate(draft.orderDate || new Date().toISOString().slice(0, 16));
        setOrderNotes(draft.orderNotes || '');
        setAttachmentName(draft.attachmentName || '');
        setOrderItems(draft.orderItems || []);
        setOrderNumber(draft.orderNumber || '');
        setEmployeeName(draft.employeeName || currentUser?.name || ''); // استعادة اسم المستخدم
        setReceiveAttachments(draft.receiveAttachments || []);
        setIsCreatingReceive(true);
        toast({
          title: 'تم تحميل المسودة',
          description: 'تم تحميل مسودة أمر الاستلام بنجاح',
        });
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في تحميل المسودة',
          description: 'حدث خطأ أثناء تحميل المسودة',
        });
      }
    }
  };

  const clearReceiveDraft = () => {
    localStorage.removeItem(RECEIVE_DRAFT_KEY);
    setIsReceiveDraft(false);
    setDraftRefresh(prev => prev + 1);
    toast({
      title: 'تم حذف المسودة',
      description: 'تم حذف مسودة أمر الاستلام',
    });
  };

  // دوال وضع الإنشاء
  const checkExistingReceiveDrafts = () => {
    const existingDraft = localStorage.getItem(RECEIVE_DRAFT_KEY);
    if (existingDraft) {
      toast({
        variant: 'destructive',
        title: 'يوجد مسودة محفوظة',
        description: 'يرجى تحميل المسودة الموجودة أو حذفها قبل إنشاء أمر جديد.',
      });
      return true;
    }
    return false;
  };

  const startCreatingReceive = () => {
    if (checkExistingReceiveDrafts()) {
      return; // إيقاف العملية وعرض التنبيه
    }
    resetReceivePage();
    setIsCreatingReceive(true);
    toast({
      title: 'وضع الإنشاء',
      description: 'تم تفعيل وضع إنشاء أمر استلام جديد',
    });
  };

  const resetReceivePage = () => {
    setReferenceNumber('');
    setOrderDate(new Date().toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM
    setOrderNotes('');
    setAttachmentName('');
    setOrderItems([]);
    setImeiToReceive('');
    setLoadedMaintOrder(null);
    setIsCreatingReceive(false); // العودة إلى وضع القراءة فقط
    setReceiveAttachments([]); // إعادة تعيين المرفقات
    setOrderNumber(''); // إعادة تعيين رقم الأمر لتوليد رقم جديد
    setEmployeeName(currentUser?.name || ''); // تعيين اسم المستخدم تلقائياً
    clearReceiveDraft(); // تنظيف المسودة
    if (attachmentInputRef.current) attachmentInputRef.current.value = '';
    if (imeiFileInputRef.current) imeiFileInputRef.current.value = '';
    if (receiveAttachmentsInputRef.current) receiveAttachmentsInputRef.current.value = '';
  };

  const saveDeliveryDraft = () => {
    const draftData = {
      deliveryItems,
      deliveryReferenceNumber,
      deliveryOrderDate,
      receivingEmployeeName,
      deliveryWarehouseId,
      deliveryAttachmentName,
      deliveryOrderNumber,
      deliveryEmployeeName, // حفظ اسم المستخدم
      deliveryAttachments,
      timestamp: new Date(),
    };
    localStorage.setItem(DELIVERY_DRAFT_KEY, JSON.stringify(draftData));
    setIsDeliveryDraft(true);
    setDraftRefresh(prev => prev + 1);
    toast({
      title: 'تم حفظ المسودة',
      description: 'تم حفظ مسودة أمر التسليم بنجاح',
    });
  };

  const continueDeliveryDraft = () => {
    const savedDraft = localStorage.getItem(DELIVERY_DRAFT_KEY);
    if (savedDraft) {
      try {
        const draft = JSON.parse(savedDraft);
        setDeliveryItems(draft.deliveryItems || []);
        setDeliveryReferenceNumber(draft.deliveryReferenceNumber || '');
        setDeliveryOrderDate(draft.deliveryOrderDate || new Date().toISOString().slice(0, 16));
        setReceivingEmployeeName(draft.receivingEmployeeName || '');
        setDeliveryWarehouseId(draft.deliveryWarehouseId || undefined);
        setDeliveryAttachmentName(draft.deliveryAttachmentName || '');
        setDeliveryOrderNumber(draft.deliveryOrderNumber || '');
        setDeliveryEmployeeName(draft.deliveryEmployeeName || currentUser?.name || ''); // استعادة اسم المستخدم
        setDeliveryAttachments(draft.deliveryAttachments || []);
        setIsCreatingDelivery(true);
        toast({
          title: 'تم تحميل المسودة',
          description: 'تم تحميل مسودة أمر التسليم بنجاح',
        });
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في تحميل المسودة',
          description: 'حدث خطأ أثناء تحميل المسودة',
        });
      }
    }
  };

  const clearDeliveryDraft = () => {
    localStorage.removeItem(DELIVERY_DRAFT_KEY);
    setIsDeliveryDraft(false);
    setDraftRefresh(prev => prev + 1);
    toast({
      title: 'تم حذف المسودة',
      description: 'تم حذف مسودة أمر التسليم',
    });
  };

  const checkExistingDeliveryDrafts = () => {
    const existingDraft = localStorage.getItem(DELIVERY_DRAFT_KEY);
    if (existingDraft) {
      toast({
        variant: 'destructive',
        title: 'يوجد مسودة محفوظة',
        description: 'يرجى تحميل المسودة الموجودة أو حذفها قبل إنشاء أمر جديد.',
      });
      return true;
    }
    return false;
  };

  const startCreatingDelivery = () => {
    if (checkExistingDeliveryDrafts()) {
      return; // إيقاف العملية وعرض التنبيه
    }
    resetDeliveryPage();
    setIsCreatingDelivery(true);
    toast({
      title: 'وضع الإنشاء',
      description: 'تم تفعيل وضع إنشاء أمر تسليم جديد',
    });
  };

  const resetDeliveryPage = () => {
    setDeliveryItems([]);
    setImeiToDeliver('');
    setDeliveryItemNotes('');
    setDeliveryFault('');
    setCustomDeliveryFault('');
    setDeliveryDamage('');
    setCustomDeliveryDamage('');
    setDeliveryReferenceNumber('');
    setDeliveryOrderDate(new Date().toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM
    setDeliveryAttachmentName('');
    setReceivingEmployeeName('');
    setDeliveryWarehouseId(undefined); // Reset delivery warehouse
    setLoadedDelivOrder(null);
    setIsCreatingDelivery(false); // العودة إلى وضع القراءة فقط
    setDeliveryAttachments([]); // إعادة تعيين المرفقات
    setDeliveryOrderNumber(''); // إعادة تعيين رقم الأمر لتوليد رقم جديد
    setDeliveryEmployeeName(currentUser?.name || ''); // تعيين اسم المستخدم تلقائياً
    clearDeliveryDraft(); // تنظيف المسودة
    if (deliveryImeiFileInputRef.current)
      deliveryImeiFileInputRef.current.value = '';
    if (deliveryAttachmentInputRef.current)
      deliveryAttachmentInputRef.current.value = '';
    if (deliveryAttachmentsInputRef.current)
      deliveryAttachmentsInputRef.current.value = '';
  };

  const devicesInMaintenance = useMemo(
    () =>
      store.devices.filter((d) =>
        [
          'قيد الإصلاح',
          'بانتظار قطع غيار',
          'مراجعة الطلب من الإدارة',
        ].includes(d.status),
      ),
    [store.devices]
    );

  const devicesReturnedFromMaintenanceAwaitingReceipt = useMemo(() => {
    const pendingIds = new Set(
      (store.maintenanceHistory || [])
        .filter((log) => log.status === 'pending')
        .map((log) => log.deviceId)
    );
    return (store.devices || []).filter((d) => pendingIds.has(d.id));
  }, [store.devices, store.maintenanceHistory]);

  const devicesSentToMaintenanceAwaitingReceipt = useMemo(
    () => (store.devices || []).filter((d) => d.status === 'بانتظار استلام في الصيانة'),
    [store.devices]
    );

  // الأجهزة المرسلة للمخزن (بانتظار الاستلام)
  const devicesSentToWarehouseAwaitingReceipt = useMemo(
    () => store.devices.filter((d) => d.status === 'بانتظار استلام في المخزن'),
    [store.devices]
    );

  const devicesAwaitingMaintenance = useMemo(
    () => devices.filter((d) => d.status === 'بانتظار إرسال للصيانة'),
    [devices]
    );

  const devicesReceivedDirectlyInMaintenance = useMemo(() => {
    const directOrders = store.maintenanceOrders.filter(
      (order) => order.source === 'direct'
    );
    const deviceIds = new Set(
      directOrders.flatMap((order) =>
        Array.isArray(order.items) ? order.items.map((item) => item.id) : []
      )
    );
    return store.devices.filter((device) => deviceIds.has(device.id));
  }, [store.devices, store.maintenanceOrders]);

  const allModels = useMemo(
    () => [...new Set((devices || []).map((d) => d.model))].sort(),
    [devices]
    );



  const handleAddItemToOrder = async () => {
    if (!imeiToReceive) return;
    const device = store.devices.find((d) => d.id === imeiToReceive);
    if (!device) {
      toast({
        title: 'خطأ',
        description: 'الجهاز غير موجود في النظام.',
        variant: 'destructive',
      });
      return;
    }

    // التحقق من إضافة الجهاز للأمر الحالي
    if (orderItems.some((item) => item.id === imeiToReceive)) {
      toast({
        title: 'خطأ',
        description: 'هذا الجهاز مضاف للأمر الحالي بالفعل.',
        variant: 'destructive',
      });
      return;
    }

    // التحقق من أن الجهاز في حالة "بانتظار استلام في الصيانة"
    if (device.status !== 'بانتظار استلام في الصيانة') {
      let errorMessage = 'لا يمكن استلام هذا الجهاز. يجب أن يكون مرسلاً من المخزن أولاً.';
      if (device.status === 'قيد الإصلاح') {
        errorMessage = 'هذا الجهاز قيد الإصلاح حالياً في قسم الصيانة.';
      } else if (device.status === 'بانتظار تسليم من الصيانة') {
        errorMessage = 'هذا الجهاز بانتظار الاستلام من قسم الصيانة. يجب استلامه أولاً قبل إعادة إرساله.';
      }
      
      toast({
        title: 'خطأ',
        description: errorMessage,
        variant: 'destructive',
      });
      return;
    }

    // إضافة الجهاز للجدول أولاً (بدون تحديث الحالة)
    setOrderItems((prev) => [...prev, device]);
    setImeiToReceive('');

    // رسالة نجاح الإضافة
    toast({
      title: '✅ تم إضافة الجهاز للجدول',
      description: `تم إضافة الجهاز ${device.model} (${device.id}) للأمر بنجاح. سيتم تحديث حالته إلى "قيد الإصلاح" عند حفظ الأمر.`,
    });
  };

  const handleReceiveFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    let blockedCount = 0;
    const newItems: Device[] = [];
    const existingDeviceIds = new Set((orderItems || []).map((item) => item.id));
    
    // الحالات المحظورة (لا يمكن إرسال الجهاز للصيانة في هذه الحالات)
    for (const imei of lines) {
      // تحقق من التكرار في الأمر الحالي
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }

      const device = store.devices.find((d) => d.id === imei);
      if (!device) {
        invalidCount++;
        continue;
      }

      // تحقق من حالة الجهاز - يجب أن يكون بانتظار الاستلام في الصيانة
      if (device.status !== 'بانتظار استلام في الصيانة') {
        blockedCount++;
        continue;
      }

      // تحديث حالة الجهاز إذا كان بانتظار الاستلام في الصيانة
      if (device.status === 'بانتظار استلام في الصيانة') {
        await store.updateDeviceStatus(device.id, 'قيد الإصلاح');
      }

      newItems.push(device);
      existingDeviceIds.add(imei);
      addedCount++;
    }

    if (newItems.length > 0) {
      setOrderItems((prev) => [...prev, ...newItems]);
    }

    let description = `تمت إضافة ${addedCount} جهازًا بنجاح.`;
    if (invalidCount > 0) description += ` تم تخطي ${invalidCount} جهازًا (غير موجود).`;
    if (duplicateCount > 0) description += ` تم تخطي ${duplicateCount} جهازًا (مكرر).`;
    if (blockedCount > 0) description += ` تم تخطي ${blockedCount} جهازًا (حالة محظورة).`;

    toast({
      title: 'اكتمل الاستيراد',
      description,
    });

    if (event.target) event.target.value = '';
  };

  const handleRemoveFromOrder = (id: string) => {
    // التحقق من حالة الجهاز قبل الحذف
    const device = orderItems.find(item => item.id === id);
    if (!device) return;

    if (device.status === 'قيد الإصلاح' || 
        device.status === 'بانتظار قطع غيار' ||
        device.status === 'مراجعة الطلب من الإدارة') {
      toast({
        variant: 'destructive',
        title: 'لا يمكن الحذف',
        description: 'لا يمكن حذف الجهاز أثناء العمل عليه. أكمل العمل أولاً.',
      });
      return;
    }

    // التحقق من وجود أوامر تسليم مرتبطة
    const hasDeliveryOrder = store.deliveryOrders.some(order =>
      Array.isArray(order.items) && order.items.some(item => item.deviceId === id)
    );

    if (hasDeliveryOrder) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن الحذف',
        description: 'الجهاز مرتبط بأمر تسليم. لا يمكن حذفه.',
      });
      return;
    }

    setOrderItems((prev) => prev.filter((item) => item.id !== id));
    toast({
      title: 'تم الحذف',
      description: 'تم حذف الجهاز من الأمر بنجاح.',
    });
  };

  const handleSaveMaintOrder = async () => {
    // التحقق من البيانات الأساسية
    if (orderItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى إضافة جهاز واحد على الأقل للأمر.',
      });
      return;
    }

    // التحقق من عدم تكرار رقم الأمر (فقط للأوامر الجديدة)
    if (!loadedMaintOrder) {
      const existingOrder = store.maintenanceOrders.find(order => order.orderNumber === orderNumber);
      if (existingOrder) {
        toast({
          variant: 'destructive',
          title: 'رقم أمر مكرر',
          description: `رقم الأمر ${orderNumber} موجود بالفعل. يرجى المحاولة مرة أخرى.`,
        });
        // إعادة توليد رقم جديد
        setOrderNumber('');
        return;
      }
    }

    const orderData = {
      orderNumber,
      referenceNumber,
      date: new Date(orderDate),
      employeeName: employeeName || store.currentUser?.name || 'غير معروف',
      items: orderItems,
      notes: orderNotes,
      attachmentName: receiveAttachments.map(file => file.fileName).join(';'), // حفظ المرفقات
      status: 'wip' as 'wip' | 'completed',
      source: 'direct' as 'direct',
      createdAt: new Date(), // إضافة الختم الزمني
    };

    try {
      if (loadedMaintOrder) {
        await store.updateMaintenanceOrder({ ...orderData, id: loadedMaintOrder.id });

        // تحديث حالة جميع الأجهزة في الأمر إلى "قيد الإصلاح"
        for (const item of orderItems) {
          await store.updateDeviceStatus(item.id, 'قيد الإصلاح');
        }

        // تسجيل النشاط
        store.addActivity({
          type: 'maintenance',
          description: `تم تحديث أمر استلام الصيانة ${orderNumber} - ${orderItems.length} جهاز`,
        });

        toast({
          title: 'تم التحديث',
          description: `تم تحديث أمر الصيانة ${orderNumber} بنجاح.`,
        });
      } else {
        await store.addMaintenanceOrder(orderData);

        // تحديث حالة جميع الأجهزة في الأمر إلى "قيد الإصلاح"
        for (const item of orderItems) {
          await store.updateDeviceStatus(item.id, 'قيد الإصلاح');
        }

        // تسجيل النشاط
        store.addActivity({
          type: 'maintenance',
          description: `تم استلام ${orderItems.length} أجهزة في الصيانة - أمر رقم ${orderNumber}`,
        });

        // حذف المسودة عند الحفظ الناجح
        localStorage.removeItem(RECEIVE_DRAFT_KEY);
        setIsReceiveDraft(false);
        setDraftRefresh(prev => prev + 1);
        toast({
          title: 'تم الحفظ',
          description: `تم حفظ أمر الصيانة ${orderNumber} بنجاح.`,
        });
      }
      resetReceivePage();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: error.message || 'حدث خطأ أثناء حفظ أمر الصيانة.',
      });
    }
  };


  const handleLoadMaintOrder = (order: MaintenanceOrder) => {
    setLoadedMaintOrder(order);
    setOrderNumber(order.orderNumber);
    setReferenceNumber(order.referenceNumber || '');
    setOrderDate(new Date(order.date).toISOString().slice(0, 16)) // مطلوب لـ HTML input; // YYYY-MM-DDTHH:MM
    setOrderNotes(order.notes || '');
    setAttachmentName(order.attachmentName || '');
    setOrderItems(order.items);
    setEmployeeName(order.employeeName || currentUser?.name || ''); // تحميل اسم المستخدم
    setIsLoadMaintOrderDialogOpen(false);
    setIsCreatingReceive(false); // تعطيل وضع الإنشاء عند تحميل أمر موجود

    // تحميل المرفقات
    const fileNames = order.attachmentName ? order.attachmentName.split(';') : [];
    const convertedAttachments: AttachmentFile[] = fileNames.map(fileName => ({
      originalName: fileName,
      fileName: fileName,
      filePath: `/attachments/maintenance/${fileName}`,
      size: 0,
      type: 'application/octet-stream',
      uploadedAt: order.createdAt || new Date()
    }));
    setReceiveAttachments(convertedAttachments);

    toast({
      title: 'تم التحميل',
      description: `تم تحميل الأمر ${order.orderNumber}`,
    });
  };

  const handleDeleteMaintOrder = async () => {
    if (maintOrderToDelete) {
      try {
        const relationCheck = store.checkMaintenanceOrderRelations(maintOrderToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setMaintOrderToDelete(null);
          return;
        }

        await store.deleteMaintenanceOrder(maintOrderToDelete.id);
        toast({
          title: 'تم الحذف',
          description: `تم حذف الأمر ${maintOrderToDelete.orderNumber} وإعادة الأجهزة إلى قائمة الانتظار`,
        });
        if (loadedMaintOrder && loadedMaintOrder.id === maintOrderToDelete.id) {
          resetReceivePage();
        }
        setMaintOrderToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setMaintOrderToDelete(null);
      }
    }
  };

  const handleSendMaintRequest = () => {
    if (!requestMaintOrder) return;
    if (!requestFormData.notes.trim()) {
      toast({
        variant: 'destructive',
        title: 'مطلوب',
        description: 'يرجى كتابة تفاصيل المشكلة أو الطلب.',
      });
      return;
    }

    store.addEmployeeRequest({
      relatedOrderType: 'maintenance',
      relatedOrderId: requestMaintOrder.id,
      relatedOrderDisplayId: requestMaintOrder.orderNumber,
      requestType: requestFormData.requestType,
      priority: requestFormData.priority,
      notes: requestFormData.notes,
      attachmentName: requestFormData.attachmentName,
    });

    toast({
      title: 'تم إرسال الطلب',
      description: 'تم إرسال ملاحظتك إلى الإدارة بنجاح.',
    });
    setRequestMaintOrder(null);
    setRequestFormData(initialRequestFormState);
  };

  const handleAddToDelivery = () => {
    if (!imeiToDeliver) return;
    const device = devicesInMaintenance.find((d) => d.id === imeiToDeliver);
    if (!device) {
      toast({
        title: 'خطأ',
        description: 'الجهاز غير موجود في قائمة الأجهزة قيد الإصلاح.',
        variant: 'destructive',
      });
      return;
    }
    if (deliveryItems.some((item) => item.device.id === imeiToDeliver)) {
      toast({
        title: 'خطأ',
        description: 'الجهاز مضاف بالفعل إلى دفعة التسليم.',
        variant: 'destructive',
      });
      return;
    }

    const newDeliveryItem: DeliveryFormItem = {
      device,
      result: deliveryResult,
      notes: deliveryItemNotes,
      fault:
        deliveryResult === 'Unrepairable-Defective'
          ? deliveryFault === 'أعطال أخرى'
            ? customDeliveryFault
            : deliveryFault
          : undefined,
      damage:
        deliveryResult === 'Unrepairable-Damaged'
          ? deliveryDamage === 'أخرى'
            ? customDeliveryDamage
            : deliveryDamage
          : undefined,
    };

    setDeliveryItems((prev) => [...prev, newDeliveryItem]);
    setImeiToDeliver('');
    setDeliveryItemNotes('');
    setDeliveryFault('');
    setCustomDeliveryFault('');
    setDeliveryDamage('');
    setCustomDeliveryDamage('');
    toast({
      title: 'تمت الإضافة',
      description: `تمت إضافة ${device.model} إلى دفعة التسليم.`,
    });
  };

  const handleDeliveryFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    const newItems: DeliveryFormItem[] = [];
    const existingDeviceIds = new Set(
      (deliveryItems || []).map((item) => item.device.id)
    );

    for (const imei of lines) {
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }

      const device = devicesInMaintenance.find((d) => d.id === imei);
      if (!device) {
        invalidCount++;
        continue;
      }

      newItems.push({
        device,
        result: 'Repaired', // Default result for batch import
      });
      existingDeviceIds.add(imei);
      addedCount++;
    }

    if (newItems.length > 0) {
      setDeliveryItems((prev) => [...prev, ...newItems]);
    }

    let description = `تمت إضافة ${addedCount} جهازًا بنجاح.`;
    if (invalidCount > 0) description += ` تم تخطي ${invalidCount} جهازًا (غير موجود).`;
    if (duplicateCount > 0) description += ` تم تخطي ${duplicateCount} جهازًا (مكرر).`;

    toast({
      title: 'اكتمل الاستيراد',
      description,
    });

    if (event.target) event.target.value = '';
  };

  const handleRemoveFromDelivery = (imei: string) => {
    setDeliveryItems((prev) => prev.filter((item) => item.device.id !== imei));
  };

  // دالة تنظيف البيانات من null bytes
  const sanitizeString = (str: any): string => {
    if (!str) return '';
    if (typeof str !== 'string') {
      try {
        str = String(str);
      } catch {
        return '';
      }
    }
    
    // إزالة null bytes وأحرف التحكم
    return str
      .replace(/\x00/g, '') // إزالة null bytes
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // إزالة أحرف التحكم
      .replace(/\uFFFD/g, '') // إزالة أحرف الاستبدال
      .trim();
  };

  const handleSaveDeliveryOrder = async () => {
    // التحقق من البيانات الأساسية
    if (deliveryItems.length === 0) {
      toast({
        title: 'خطأ',
        description: 'لا توجد أجهزة في دفعة التسليم.',
        variant: 'destructive',
      });
      return;
    }

    // التحقق من وجود المخزن والتاريخ
    if (!deliveryWarehouseId) {
      toast({
        title: 'خطأ',
        description: 'يرجى اختيار المخزن.',
        variant: 'destructive',
      });
      return;
    }

    if (!deliveryOrderDate) {
      toast({
        title: 'خطأ',
        description: 'يرجى تحديد التاريخ.',
        variant: 'destructive',
      });
      return;
    }

    // التحقق من عدم تكرار رقم الأمر (فقط للأوامر الجديدة)
    if (!loadedDelivOrder) {
      const existingOrder = store.deliveryOrders.find(order => order.deliveryOrderNumber === deliveryOrderNumber);
      if (existingOrder) {
        toast({
          variant: 'destructive',
          title: 'رقم أمر مكرر',
          description: `رقم الأمر ${deliveryOrderNumber} موجود بالفعل. يرجى المحاولة مرة أخرى.`,
        });
        // إعادة توليد رقم جديد
        setDeliveryOrderNumber('');
        return;
      }
    }

    // الحصول على اسم المخزن
    const selectedWarehouse = store.warehouses.find(w => w.id === deliveryWarehouseId);
    const warehouseName = selectedWarehouse ? selectedWarehouse.name : 'مخزن غير محدد';

    const deliveryData: Omit<DeliveryOrder, 'id' | 'createdAt'> = {
      deliveryOrderNumber: sanitizeString(deliveryOrderNumber),
      referenceNumber: sanitizeString(deliveryReferenceNumber),
      date: new Date(deliveryOrderDate),
      employeeName: sanitizeString(deliveryEmployeeName || store.currentUser?.name || 'غير معروف'),
      receivingEmployeeName: sanitizeString(receivingEmployeeName) || undefined,
      warehouseId: deliveryWarehouseId, // Use the new state variable
      warehouseName: sanitizeString(warehouseName), // إضافة اسم المخزن
      notes: '',
      attachmentName: (deliveryAttachments || []).map(file => sanitizeString(file.fileName)).join(';'), // حفظ المرفقات
      items: (deliveryItems || []).map(({ device, ...rest }) => ({
        deviceId: sanitizeString(device.id),
        model: sanitizeString(device.model),
        result: sanitizeString(rest.result),
        fault: sanitizeString(rest.fault),
        damage: sanitizeString(rest.damage),
        notes: sanitizeString(rest.notes),
      })),
    };

    if (loadedDelivOrder) {
      store.updateDeliveryOrder({
        ...deliveryData,
        id: loadedDelivOrder.id,
        createdAt: loadedDelivOrder.createdAt // الحفاظ على التاريخ الأصلي
      });

      // تسجيل النشاط
      store.addActivity({
        type: 'maintenance',
        description: `تم تحديث أمر تسليم الصيانة ${deliveryOrderNumber} - ${deliveryItems.length} جهاز`,
      });

      toast({
        title: 'تم تحديث أمر التسليم',
      });
    } else {
      try {
        await store.addDeliveryOrder(deliveryData);

        // ملاحظة: تحديث حالة الأجهزة يتم تلقائياً في API

        // تسجيل النشاط
        store.addActivity({
          type: 'maintenance',
          description: `تم تسليم ${deliveryItems.length} أجهزة من الصيانة - أمر رقم ${deliveryOrderNumber}`,
        });

        // حذف المسودة عند الحفظ الناجح
        localStorage.removeItem(DELIVERY_DRAFT_KEY);
        setIsDeliveryDraft(false);
        setDraftRefresh(prev => prev + 1);

        toast({
          title: 'اكتمل التسليم',
          description: `تم إنشاء أمر تسليم لـ ${deliveryItems.length} جهازًا بنجاح وتحديث حالة الأجهزة.`,
        });

        // إعادة تعيين الصفحة بعد تأخير قصير للسماح بتحديث البيانات
        setTimeout(() => {
          resetDeliveryPage();
        }, 100);
      } catch (error) {
        console.error('Error creating delivery order:', error);
        toast({
          variant: 'destructive',
          title: 'خطأ في الحفظ',
          description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ أمر التسليم.',
        });
        return;
      }
    }
  };

  const handleLoadDelivOrder = (order: DeliveryOrder) => {
    setLoadedDelivOrder(order);
    setDeliveryOrderNumber(order.deliveryOrderNumber);
    setDeliveryReferenceNumber(order.referenceNumber || '');
    setDeliveryOrderDate(new Date(order.date).toISOString().slice(0, 16)) // مطلوب لـ HTML input; // YYYY-MM-DDTHH:MM
    setReceivingEmployeeName(order.receivingEmployeeName || '');
    setDeliveryWarehouseId(order.warehouseId); // Set delivery warehouse from loaded order
    setDeliveryAttachmentName(order.attachmentName || '');
    setDeliveryEmployeeName(order.employeeName || currentUser?.name || ''); // تحميل اسم المستخدم
    const orderItems = Array.isArray(order.items) ? order.items : [];
    const itemsToLoad = orderItems.map((item) => {
      const device = store.devices.find((d) => d.id === item.deviceId);
      return { device: device as Device, ...item };
    });
    setDeliveryItems(itemsToLoad as DeliveryFormItem[]);
    setIsLoadDelivOrderDialogOpen(false);
    setIsCreatingDelivery(false); // تعطيل وضع الإنشاء عند تحميل أمر موجود

    // تحميل المرفقات
    const fileNames = order.attachmentName ? order.attachmentName.split(';') : [];
    const convertedAttachments: AttachmentFile[] = fileNames.map(fileName => ({
      originalName: fileName,
      fileName: fileName,
      filePath: `/attachments/maintenance/${fileName}`,
      size: 0,
      type: 'application/octet-stream',
      uploadedAt: order.createdAt || new Date()
    }));
    setDeliveryAttachments(convertedAttachments);

    toast({
      title: 'تم التحميل',
      description: `تم تحميل الأمر ${order.deliveryOrderNumber}`,
    });
  };

  const handleDeleteDelivOrder = async () => {
    if (delivOrderToDelete) {
      try {
        const relationCheck = store.checkDeliveryOrderRelations(delivOrderToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setDelivOrderToDelete(null);
          return;
        }

        await store.deleteDeliveryOrder(delivOrderToDelete.id);
        toast({
          title: 'تم الحذف',
          description: `تم حذف الأمر ${delivOrderToDelete.deliveryOrderNumber}`,
        });
        if (
          loadedDelivOrder &&
          loadedDelivOrder.id === delivOrderToDelete.id
        ) {
          resetDeliveryPage();
        }
        setDelivOrderToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setDelivOrderToDelete(null);
      }
    }
  };

  const handleSendDelivRequest = () => {
    if (!requestDelivOrder) return;
    if (!requestFormData.notes.trim()) {
      toast({
        variant: 'destructive',
        title: 'مطلوب',
        description: 'يرجى كتابة تفاصيل المشكلة أو الطلب.',
      });
      return;
    }
    store.addEmployeeRequest({
      relatedOrderType: 'delivery',
      relatedOrderId: requestDelivOrder.id,
      relatedOrderDisplayId: requestDelivOrder.deliveryOrderNumber,
      requestType: requestFormData.requestType,
      priority: requestFormData.priority,
      notes: requestFormData.notes,
      attachmentName: requestFormData.attachmentName,
    });
    toast({
      title: 'تم إرسال الطلب',
      description: 'تم إرسال ملاحظتك إلى الإدارة بنجاح.',
    });
    setRequestDelivOrder(null);
    setRequestFormData(initialRequestFormState);
  };

  const handlePrintDeliverySlip = (action: 'print' | 'download') => {
    const itemsToPrint =
      loadedDelivOrder && (deliveryItems || []).length === 0
        ? (Array.isArray(loadedDelivOrder.items) ? loadedDelivOrder.items : [])
        : (deliveryItems || []).map((item) => ({
            ...item,
            deviceId: item.device.id,
            model: item.device.model,
          }));

    if (itemsToPrint.length === 0) {
      toast({ variant: 'destructive', title: 'لا توجد عناصر للطباعة' });
      return;
    }
    const doc = new jsPDF();
    doc.setR2L(true);

    const settings = store.systemSettings;
    if (settings.logoUrl) {
      try {
        doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
      } catch (e) {
        console.error('Error adding logo image to PDF:', e);
      }
    }
    doc.setFontSize(16);
    doc.text(settings.companyName, 190, 15, { align: 'right' });
    doc.setFontSize(10);
    doc.text(settings.companyAddress, 190, 22, { align: 'right' });
    doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
    doc.setLineWidth(0.5);
    doc.line(15, 35, 195, 35);

    doc.setFontSize(18);
    doc.text(`أمر تسليم صيانة`, 190, 45, { align: 'right' });
    doc.setFontSize(12);
    doc.text(
      `رقم الأمر: ${loadedDelivOrder?.deliveryOrderNumber || deliveryOrderNumber}`,
      190,
      52,
      { align: 'right' }
    );
    autoTable(doc, {
      startY: 75,
      head: [['النتيجة/الوصف', 'الموديل', 'الرقم التسلسلي']],
      body: (itemsToPrint || []).map((item) => [
        item.fault || item.damage || item.notes || item.result,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    if (action === 'print') doc.output('dataurlnewwindow');
    else
      doc.save(
        `delivery_slip_${loadedDelivOrder?.deliveryOrderNumber || deliveryOrderNumber}.pdf`
    );
  };

  const handleExportDeliveryExcel = async () => {
    const itemsToExport =
      loadedDelivOrder && deliveryItems.length === 0
        ? (Array.isArray(loadedDelivOrder.items) ? loadedDelivOrder.items : [])
        : deliveryItems.map((item) => ({
            ...item,
            deviceId: item.device.id,
            model: item.device.model,
          }));

    if (itemsToExport.length === 0) {
      toast({ variant: 'destructive', title: 'لا توجد عناصر للتصدير' });
      return;
    }

    const XLSX = await import('xlsx');
    const dataToExport = itemsToExport.map((item) => ({
      'رقم أمر التسليم':
        loadedDelivOrder?.deliveryOrderNumber || deliveryOrderNumber,
      'تاريخ التسليم': loadedDelivOrder?.date || deliveryOrderDate,
      'الرقم المرجعي':
        loadedDelivOrder?.referenceNumber || deliveryReferenceNumber,
      'الرقم التسلسلي': item.deviceId,
      الموديل: item.model,
      النتيجة: item.result,
      'الوصف/السبب': item.fault || item.damage || item.notes || '',
    }));

    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'أمر تسليم الصيانة');
    XLSX.writeFile(
      workbook,
      `Delivery_Order_${loadedDelivOrder?.deliveryOrderNumber || deliveryOrderNumber}.xlsx`
    );
  };

  const handleRevertToMaintenance = (deviceId: string) => {
    store.revertDeviceToMaintenance(deviceId);
    toast({
      title: 'تمت الاستعادة',
      description: 'تمت إعادة الجهاز إلى قائمة العمل قيد الإصلاح بنجاح.',
    });
  };

  const getStatusBadge = (status: Device['status']) => {
    switch (status) {
      case 'بانتظار تسليم من الصيانة':
        return 'border-purple-500/20 bg-purple-500/20 text-purple-400';
      case 'معيب':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/20';
      case 'تالف':
        return 'bg-red-500/20 text-red-400 border-red-500/20';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/20';
    }
  };

  const getStatusColor = (status: DeviceStatus) => {
    switch (status) {
      case 'قيد الإصلاح':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/20';
      case 'بانتظار قطع غيار':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/20';
      case 'مراجعة الطلب من الإدارة':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/20';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/20';
    }
  };

  const handleBrowseInventory = () => {
    if (!browseModelFilter) {
      toast({
        title: 'خطأ',
        description: 'يرجى اختيار موديل أولاً.',
        variant: 'destructive',
      });
      return;
    }

    const relevantDevices = devices.filter((d) => d.model === browseModelFilter);
    
    // Find devices by status and get their warehouse names
    const needsMaintenanceDevices = relevantDevices.filter(
      (d) => d.status === 'بانتظار إرسال للصيانة'
    );
    const damagedDevices = relevantDevices.filter(
      (d) => d.status === 'تالف'
    );
    const defectiveDevices = relevantDevices.filter(
      (d) => d.status === 'معيب'
    );

    // Get warehouse names for each status
    const getWarehouseNames = (deviceList: Device[]) => {
      return [...new Set(deviceList.map(d => {
        const warehouse = store.warehouses.find(w => w.id === d.warehouseId);
        return warehouse?.name || 'مخزن غير محدد';
      }))];
    };

    setSearchResults({
      needsMaintenance: {
        exists: needsMaintenanceDevices.length > 0,
        warehouseNames: getWarehouseNames(needsMaintenanceDevices)
      },
      damaged: {
        exists: damagedDevices.length > 0,
        warehouseNames: getWarehouseNames(damagedDevices)
      },
      defective: {
        exists: defectiveDevices.length > 0,
        warehouseNames: getWarehouseNames(defectiveDevices)
      }
    });
  };

  const handleSendDeviceRequest = () => {
    if (!deviceForRequest || !deviceRequestNotes.trim()) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى كتابة تفاصيل الطلب.',
      });
      return;
    }

    const order = store.maintenanceOrders.find((o) =>
      Array.isArray(o.items) && o.items.some((i) => i.id === deviceForRequest.id)
    );

    if (!order) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لم يتم العثور على أمر صيانة مرتبط بهذا الجهاز.',
      });
      return;
    }

    store.addEmployeeRequest({
      relatedOrderType: 'maintenance',
      relatedOrderId: order.id,
      relatedOrderDisplayId: order.orderNumber,
      requestType: 'إعادة نظر', // أو أي نوع آخر مناسب
      priority: 'عادي',
      notes: `طلب بخصوص الجهاز ${deviceForRequest.id} (${deviceForRequest.model}): ${deviceRequestNotes}`,
      attachmentName: '',
    });

    store.updateDeviceStatus(deviceForRequest.id, 'مراجعة الطلب من الإدارة');

    toast({
      title: 'تم إرسال الطلب',
      description: 'تم إرسال طلبك إلى الإدارة بنجاح.',
    });

    setIsDeviceRequestDialogOpen(false);
    setDeviceForRequest(null);
    setDeviceRequestNotes('');
  };

  const handleSendSparePartRequest = () => {
    if (!sparePartRequestNotes.trim()) {
      toast({
        variant: 'destructive',
        title: 'مطلوب',
        description: 'يرجى كتابة تفاصيل الطلب.',
      });
      return;
    }

    const adminUser = store.users.find((u) => u.username === 'admin');
    if (!adminUser) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لم يتم العثور على حساب الإدارة لإرسال الطلب.',
      });
      return;
    }

    // Build a detailed message that includes the search results
    const getAvailabilityText = (result: { exists: boolean; warehouseNames: string[] }) => {
      if (!result.exists) return 'غير موجود';
      return `موجود في: ${result.warehouseNames.join('، ')}`;
    };

    const messageText = `
طلب قطع غيار للصيانة
-------------------
الموديل المطلوب: ${browseModelFilter}

توفر القطع المطلوبة:
- بانتظار الصيانة: ${searchResults ? getAvailabilityText(searchResults.needsMaintenance) : 'لم يتم البحث'}
- معيب: ${searchResults ? getAvailabilityText(searchResults.defective) : 'لم يتم البحث'}
- تالف: ${searchResults ? getAvailabilityText(searchResults.damaged) : 'لم يتم البحث'}

تفاصيل الطلب:
${sparePartRequestNotes}

مقدم الطلب: ${store.currentUser?.name || 'غير معروف'}
التاريخ: ${formatDateTime(new Date())}
    `;

    store.sendMessage({
      recipientId: adminUser.id,
      recipientName: adminUser.name || 'Admin',
      text: messageText,
    });

    // Also create an employee request for tracking
    store.addEmployeeRequest({
      relatedOrderType: 'maintenance',
      relatedOrderId: 0, // Generic request not tied to a specific order
      relatedOrderDisplayId: `SPARE-${Date.now()}`,
      requestType: 'تعديل',
      priority: 'عادي',
      notes: messageText,
      attachmentName: '',
    });

    toast({
      title: 'تم إرسال الطلب',
      description: 'تم إرسال طلب قطع الغيار إلى الإدارة وتسجيله في سجل الطلبات.',
    });
    setIsSparePartRequestDialogOpen(false);
    setSparePartRequestNotes('');
  };

  const handleReceiveAwaitingDevice = (deviceId: string) => {
    store.updateDeviceStatus(deviceId, 'قيد الإصلاح');
    toast({
      title: 'تم الاستلام',
      description: 'تم استلام الجهاز في قسم الصيانة وتحديث حالته.',
    });
  };

  if (isLoading) {
    return (
      <PermissionGuard pageKey="maintenance">
        <div className="space-y-4">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">جاري تحميل بيانات الصيانة...</p>
            </div>
          </div>
        </div>
      </PermissionGuard>
    );
  }

  return (
    <PermissionGuard pageKey="maintenance">
      <div className="maintenance-page">
        {/* رأس الصفحة المحسن */}
        <div className="header-card mb-6">
          <div className="p-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <span className="text-xl font-bold">🔧</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
                    نظام إدارة الصيانة المتقدم
                  </h1>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                    إدارة شاملة لعمليات الصيانة والإصلاح مع تتبع متقدم للأجهزة
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* زر الوضع الليلي */}
                <DarkModeToggle
                  size="md"
                  variant="outline"
                  className="enhanced-button"
                />
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="overview" className="enhanced-tabs w-full">
        <TabsList className="w-full flex justify-between">
          <TabsTrigger value="overview" className="enhanced-tab-trigger">
            <Wrench className="ml-2 h-4 w-4" />
            🔧 نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="receive" className="enhanced-tab-trigger">
            <Inbox className="ml-2 h-4 w-4" />
            📥 استلام أجهزة {isReceiveDraft && <Badge variant="secondary" className="mr-1">مسودة</Badge>}
          </TabsTrigger>
          <TabsTrigger value="deliver" className="enhanced-tab-trigger">
            <Send className="ml-2 h-4 w-4" />
            📤 تسليم الأجهزة {isDeliveryDraft && <Badge variant="secondary" className="mr-1">مسودة</Badge>}
          </TabsTrigger>
          <TabsTrigger value="browse-inventory" className="enhanced-tab-trigger">
            <PackageSearch className="ml-2 h-4 w-4" />
            🔍 البحث عن قطع
          </TabsTrigger>
          <TabsTrigger value="orders" className="enhanced-tab-trigger">
            <FileText className="ml-2 h-4 w-4" />
            📋 أوامر الصيانة
          </TabsTrigger>
          <TabsTrigger value="history" className="enhanced-tab-trigger">
            <FileText className="ml-2 h-4 w-4" />
            📜 سجل الصيانة
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <Card className="enhanced-maintenance-card overview-section animate-fade-in-up">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 py-4">
                  <CardTitle className="text-lg text-blue-800 dark:text-blue-200 flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                      {devicesInMaintenance.length}
                    </div>
                    <div>
                      <div className="font-bold">قيد الإصلاح</div>
                      <div className="text-xs text-blue-600 dark:text-blue-300 font-normal mt-1">الأجهزة قيد الصيانة حالياً</div>
                    </div>
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card className="enhanced-maintenance-card overview-section animate-fade-in-up">
                <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20 py-4">
                  <CardTitle className="text-lg text-orange-800 dark:text-orange-200 flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-amber-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                      {devicesSentToMaintenanceAwaitingReceipt.length}
                    </div>
                    <div>
                      <div className="font-bold">بانتظار الاستلام</div>
                      <div className="text-xs text-orange-600 dark:text-orange-300 font-normal mt-1">أجهزة مرسلة للصيانة</div>
                    </div>
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card className="enhanced-maintenance-card overview-section animate-fade-in-up">
                <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-4">
                  <CardTitle className="text-lg text-green-800 dark:text-green-200 flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                      {devicesReceivedDirectlyInMaintenance.length}
                    </div>
                    <div>
                      <div className="font-bold">مستلمة مباشرة</div>
                      <div className="text-xs text-green-600 dark:text-green-300 font-normal mt-1">أجهزة مستلمة للصيانة</div>
                    </div>
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card className="enhanced-maintenance-card overview-section animate-fade-in-up">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 py-4">
                  <CardTitle className="text-lg text-purple-800 dark:text-purple-200 flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                      {devicesReturnedFromMaintenanceAwaitingReceipt.length}
                    </div>
                    <div>
                      <div className="font-bold">تم التسليم</div>
                      <div className="text-xs text-purple-600 dark:text-purple-300 font-normal mt-1">أجهزة مسلمة من الصيانة</div>
                    </div>
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card className="enhanced-maintenance-card overview-section animate-fade-in-up">
                <CardHeader className="bg-gradient-to-r from-cyan-50 to-teal-50 dark:from-cyan-950/20 dark:to-teal-950/20 py-4">
                  <CardTitle className="text-lg text-cyan-800 dark:text-cyan-200 flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 to-teal-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                      {devicesSentToWarehouseAwaitingReceipt.length}
                    </div>
                    <div>
                      <div className="font-bold">مرسلة للمخزن</div>
                      <div className="text-xs text-cyan-600 dark:text-cyan-300 font-normal mt-1">بانتظار الاستلام في المخزن</div>
                    </div>
                  </CardTitle>
                </CardHeader>
              </Card>
            </div>

            {/* مذكرات وإنجازات الصيانة */}
            <MaintenanceMemo
              devices={devicesInMaintenance.map(device => ({
                id: device.id,
                name: device.model,
                maintenanceStartDate: new Date(), // TODO: استخدم تاريخ البدء الفعلي
              }))}
            />
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2 text-gray-800 dark:text-gray-200">لوحة تحكم الصيانة المتقدمة</h2>
              <p className="text-gray-600 dark:text-gray-400">نظرة عامة سريعة على حالة الأجهزة في قسم الصيانة مع إحصائيات مفصلة</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* الأجهزة قيد العمل */}
              <Card
                className="enhanced-maintenance-card overview-section animate-fade-in-up cursor-pointer"
                onClick={() => setDetailsModal({
                  isOpen: true,
                  title: 'الأجهزة قيد العمل في الصيانة',
                  data: devicesInMaintenance
                })}
              >
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-lg enhanced-icon">
                      <Wrench className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    {devicesInMaintenance.length}
                  </div>
                  <div className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-1">
                    🔧 قيد العمل
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    انقر للتفاصيل الكاملة
                  </div>
                </CardContent>
              </Card>

              {/* أجهزة بانتظار الاستلام */}
              <Card
                className="enhanced-maintenance-card overview-section animate-fade-in-up cursor-pointer"
                onClick={() => setDetailsModal({
                  isOpen: true,
                  title: 'الأجهزة بانتظار الاستلام في الصيانة',
                  data: devicesSentToMaintenanceAwaitingReceipt
                })}
              >
                <CardContent className="p-6 text-center">
                  <div className="flex items-center justify-center mb-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center text-white shadow-lg enhanced-icon">
                      <Inbox className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                    {devicesSentToMaintenanceAwaitingReceipt.length}
                  </div>
                  <div className="text-sm font-semibold text-yellow-700 dark:text-yellow-300 mb-1">
                    ⏳ بانتظار الاستلام
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    انقر للتفاصيل
                  </div>
                </CardContent>
              </Card>

              {/* الأجهزة المرسلة للمخزن */}
              <Card 
                className="hover:shadow-lg transition-shadow cursor-pointer border-purple-200 bg-purple-50/50"
                onClick={() => setDetailsModal({
                  isOpen: true,
                  title: 'الأجهزة المرسلة للمخزن (بانتظار الاستلام)',
                  data: devicesReturnedFromMaintenanceAwaitingReceipt
                })}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Send className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {devicesReturnedFromMaintenanceAwaitingReceipt.length}
                  </div>
                  <div className="text-sm font-medium text-purple-700">
                    مرسلة للمخزن
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    انقر للتفاصيل
                  </div>
                </CardContent>
              </Card>

              {/* أجهزة مستلمة مباشرة */}
              <Card 
                className="hover:shadow-lg transition-shadow cursor-pointer border-green-200 bg-green-50/50"
                onClick={() => setDetailsModal({
                  isOpen: true,
                  title: 'الأجهزة المستلمة مباشرة في الصيانة',
                  data: devicesReceivedDirectlyInMaintenance
                })}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Package className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {devicesReceivedDirectlyInMaintenance.length}
                  </div>
                  <div className="text-sm font-medium text-green-700">
                    مستلمة مباشرة
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    انقر للتفاصيل
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* إضافة قسم للإحصائيات السريعة */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="shadow-lg border-l-4 border-l-teal-500 hover:shadow-xl transition-all duration-300">
                <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 py-2">
                  <CardTitle className="text-sm text-teal-800 flex items-center">
                    <div className="w-5 h-5 bg-teal-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">
                      {store.maintenanceOrders.filter(o => o.status === 'wip').length}
                    </div>
                    أوامر صيانة نشطة
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card className="shadow-lg border-l-4 border-l-indigo-500 hover:shadow-xl transition-all duration-300">
                <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 py-2">
                  <CardTitle className="text-sm text-indigo-800 flex items-center">
                    <div className="w-5 h-5 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">
                      {store.deliveryOrders.length}
                    </div>
                    أوامر تسليم مكتملة
                  </CardTitle>
                </CardHeader>
              </Card>

              <Card className="shadow-lg border-l-4 border-l-red-500 hover:shadow-xl transition-all duration-300">
                <CardHeader className="bg-gradient-to-r from-red-50 to-pink-50 py-2">
                  <CardTitle className="text-sm text-red-800 flex items-center">
                    <div className="w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">
                      {devicesAwaitingMaintenance.length}
                    </div>
                    أجهزة بانتظار الإرسال للصيانة
                  </CardTitle>
                </CardHeader>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="receive">
          <div className="flex flex-wrap items-center justify-between gap-2 mb-4">
            <h2 className="text-xl font-bold">
              {loadedMaintOrder
                ? `تعديل أمر صيانة ${loadedMaintOrder.orderNumber}`
                : 'أمر صيانة جديد'}
            </h2>
            {!isCreatingReceive && !loadedMaintOrder && canCreate && (
              <div className="text-sm text-blue-700 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 rounded-lg border border-blue-200 shadow-sm animate-pulse">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center mr-3">
                    💡
                  </div>
                  <div>
                    <div className="font-medium">ابدأ بإنشاء أمر استلام جديد</div>
                    <div className="text-xs text-blue-600 mt-1">اضغط على "أمر جديد" لتفعيل وضع الإنشاء</div>
                  </div>
                </div>
              </div>
            )}
            <div className="flex gap-2">
              {canCreate && (
                <Button
                  onClick={startCreatingReceive}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <PlusCircle className="ml-2 h-4 w-4" /> أمر جديد
                </Button>
              )}
              {isReceiveDraft && (
                <>
                  <Button
                    variant="outline"
                    onClick={continueReceiveDraft}
                    className="border-orange-300 text-orange-600 hover:bg-orange-50 relative"
                  >
                    <FileDown className="ml-2 h-4 w-4" /> تحميل مسودة
                    <Badge variant="secondary" className="absolute -top-2 -right-2 text-xs">
                      محفوظة
                    </Badge>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={clearReceiveDraft}
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    <X className="ml-2 h-4 w-4" /> حذف المسودة
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                onClick={() => setIsLoadMaintOrderDialogOpen(true)}
              >
                <FolderOpen className="ml-2 h-4 w-4" /> تحميل أمر سابق
              </Button>
              <Button
                variant="secondary"
                onClick={() => setRequestMaintOrder(loadedMaintOrder)}
                disabled={!loadedMaintOrder}
              >
                <MessageSquareQuote className="ml-2 h-4 w-4" /> إرسال ملاحظة
              </Button>
              {canDelete && (
                <Button
                  variant="destructive"
                  onClick={() => setMaintOrderToDelete(loadedMaintOrder)}
                  disabled={!loadedMaintOrder}
                >
                  <Trash className="ml-2 h-4 w-4" /> حذف الأمر
                </Button>
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>تفاصيل الأمر</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="orderNumber">رقم الأمر</Label>
                    <Input id="orderNumber" value={orderNumber} disabled />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="orderDate">التاريخ</Label>
                    <Input
                      id="orderDate"
                      type="datetime-local"
                      value={orderDate}
                      onChange={(e) => setOrderDate(e.target.value)}
                      className="h-8 text-xs font-mono"
                      style={{ direction: 'ltr' }}
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>الموظف المسؤول</Label>
                    <Input
                      value={employeeName || store.currentUser?.name || 'مدير النظام'}
                      disabled // منع تعديل اسم المستخدم
                      className="bg-gray-50"
                      title="اسم المستخدم محدد تلقائياً ولا يمكن تعديله"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="referenceNumber">
                      رقم مرجعي (اختياري)
                    </Label>
                    <Input
                      id="referenceNumber"
                      value={referenceNumber}
                      onChange={(e) => setReferenceNumber(e.target.value)}
                      placeholder="أدخل الرقم المرجعي"
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="orderNotes">ملاحظات (اختياري)</Label>
                    <Textarea
                      id="orderNotes"
                      value={orderNotes}
                      onChange={(e) => setOrderNotes(e.target.value)}
                      placeholder="أدخل ملاحظاتك هنا..."
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="attachment">إضافة مرفق</Label>
                    <input
                      type="file"
                      id="attachment"
                      ref={attachmentInputRef}
                      className="hidden"
                      onChange={(e) =>
                        setAttachmentName(e.target.files?.[0]?.name || '')
                      }
                    />
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => attachmentInputRef.current?.click()}
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    >
                      <File className="ml-2 h-4 w-4" />
                      {attachmentName || 'اختيار ملف...'}
                    </Button>
                  </div>

                  {/* 📎 قسم المرفقات الجديد */}
                  <div className="space-y-2">
                    <Label>المرفقات المتقدمة</Label>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => receiveAttachmentsInputRef.current?.click()}
                        disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                        className="flex-1"
                      >
                        <File className="ml-2 h-4 w-4" />
                        إرفاق ملفات متعددة
                      </Button>
                      {receiveAttachments.length > 0 && (
                        <Button
                          variant="outline"
                          onClick={() => setIsReceiveAttachmentsModalOpen(true)}
                          className="px-3"
                        >
                          <span className="bg-blue-500 text-white rounded-full px-2 py-1 text-xs">
                            {receiveAttachments.length}
                          </span>
                        </Button>
                      )}
                    </div>
                    <input
                      ref={receiveAttachmentsInputRef}
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => e.target.files && handleReceiveFileUpload(e.target.files)}
                    />
                  </div>


                </CardContent>
                <CardFooter className="flex-col gap-2 items-stretch">
                  {((canCreate && isCreatingReceive) || (canEdit && loadedMaintOrder)) && (
                    <Button
                      onClick={handleSaveMaintOrder}
                      className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    >
                      <Save className="ml-2 h-4 w-4" />{' '}
                      {loadedMaintOrder ? 'تحديث الأمر' : 'حفظ الأمر'}
                    </Button>
                  )}
                  {isCreatingReceive && !loadedMaintOrder && (
                    <Button
                      variant="outline"
                      onClick={saveReceiveDraft}
                      className="border-orange-300 text-orange-600 hover:bg-orange-50"
                    >
                      <FileDown className="ml-2 h-4 w-4" /> حفظ كمسودة
                    </Button>
                  )}
                  <Button variant="outline">
                    <Printer className="ml-2 h-4 w-4" /> طباعة
                  </Button>
                </CardFooter>
              </Card>
            </div>
            <div className="lg:col-span-2 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>إضافة أجهزة للأمر</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="امسح أو أدخل الرقم التسلسلي"
                      value={imeiToReceive}
                      onChange={(e) => setImeiToReceive(e.target.value)}
                      onKeyDown={(e) =>
                        e.key === 'Enter' && handleAddItemToOrder()
                      }
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    />
                    <Button
                      onClick={handleAddItemToOrder}
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    >
                      إضافة
                    </Button>
                    <input
                      ref={imeiFileInputRef}
                      type="file"
                      className="hidden"
                      onChange={handleReceiveFileImport}
                      accept=".txt"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => imeiFileInputRef.current?.click()}
                      title="استيراد من ملف"
                      disabled={!canCreate || (!isCreatingReceive && !loadedMaintOrder)}
                    >
                      <Upload className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>
                    الأجهزة في الأمر الحالي ({orderItems.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-200">
                        <TableHead className="border border-gray-300 text-center font-bold text-gray-700 bg-blue-200/70 py-2 text-sm w-12">#</TableHead>
                        <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الرقم التسلسلي</TableHead>
                        <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الموديل</TableHead>
                        <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الحالة الحالية</TableHead>
                        <TableHead className="border border-gray-300 text-center font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">إجراء</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orderItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="h-32 text-center">
                            <div className="flex flex-col items-center justify-center text-gray-500">
                              <div className="text-4xl mb-2">📱</div>
                              <div className="text-lg font-medium mb-1">لا توجد أجهزة في الأمر</div>
                              <div className="text-sm">استخدم الحقل أعلاه لإضافة أجهزة للاستلام</div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        orderItems.map((item, index) => (
                          <TableRow key={item.id} className={`hover:bg-blue-50 transition-colors duration-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                            <TableCell className="border border-gray-300 text-center text-gray-600 font-bold py-2 text-sm w-12 bg-gray-50/50">
                              {index + 1}
                            </TableCell>
                            <TableCell className="border border-gray-300 text-right py-2 text-sm" dir="ltr">{item.id}</TableCell>
                            <TableCell className="border border-gray-300 text-right py-2 text-sm">{item.model}</TableCell>
                            <TableCell className="border border-gray-300 text-right py-2 text-sm">
                              <Badge variant="secondary">{item.status}</Badge>
                            </TableCell>
                            <TableCell className="border border-gray-300 text-center py-2 text-sm">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveFromOrder(item.id)}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="deliver" className="space-y-4">
          <div className="flex flex-wrap items-center justify-between gap-2 mb-4">
            <h2 className="text-xl font-bold">
              {loadedDelivOrder
                ? `تعديل أمر التسليم ${loadedDelivOrder.deliveryOrderNumber}`
                : 'أمر تسليم جديد'}
            </h2>
            {!isCreatingDelivery && !loadedDelivOrder && canCreate && (
              <div className="text-sm text-green-700 bg-gradient-to-r from-green-50 to-emerald-50 px-4 py-3 rounded-lg border border-green-200 shadow-sm animate-pulse">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center mr-3">
                    💡
                  </div>
                  <div>
                    <div className="font-medium">ابدأ بإنشاء أمر تسليم جديد</div>
                    <div className="text-xs text-green-600 mt-1">اضغط على "أمر جديد" لتفعيل وضع الإنشاء</div>
                  </div>
                </div>
              </div>
            )}
            <div className="flex gap-2">
              {canCreate && (
                <Button
                  onClick={startCreatingDelivery}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <PlusCircle className="ml-2 h-4 w-4" /> أمر جديد
                </Button>
              )}
              {isDeliveryDraft && (
                <>
                  <Button
                    variant="outline"
                    onClick={continueDeliveryDraft}
                    className="border-orange-300 text-orange-600 hover:bg-orange-50 relative"
                  >
                    <FileDown className="ml-2 h-4 w-4" /> تحميل مسودة
                    <Badge variant="secondary" className="absolute -top-2 -right-2 text-xs">
                      محفوظة
                    </Badge>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={clearDeliveryDraft}
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    <X className="ml-2 h-4 w-4" /> حذف المسودة
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                onClick={() => setIsLoadDelivOrderDialogOpen(true)}
              >
                <FolderOpen className="ml-2 h-4 w-4" /> تحميل أمر سابق
              </Button>
              <Button
                variant="secondary"
                onClick={() => setRequestDelivOrder(loadedDelivOrder)}
                disabled={!loadedDelivOrder}
              >
                <MessageSquareQuote className="ml-2 h-4 w-4" /> إرسال ملاحظة
              </Button>
              {canDelete && (
                <Button
                  variant="destructive"
                  onClick={() => setDelivOrderToDelete(loadedDelivOrder)}
                  disabled={!loadedDelivOrder}
                >
                  <Trash className="ml-2 h-4 w-4" /> حذف الأمر
                </Button>
              )}
            </div>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>تفاصيل أمر التسليم</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label>رقم أمر التسليم</Label>
                <Input value={deliveryOrderNumber} disabled />
              </div>
              <div className="space-y-2">
                <Label>تاريخ التسليم</Label>
                <Input
                  type="datetime-local"
                  value={deliveryOrderDate}
                  onChange={(e) => setDeliveryOrderDate(e.target.value)}
                  className="h-8 text-xs font-mono"
                  style={{ direction: 'ltr' }}
                  disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                />
              </div>
              <div className="space-y-2">
                <Label>رقم مرجعي (اختياري)</Label>
                <Input
                  placeholder="أدخل الرقم المرجعي"
                  value={deliveryReferenceNumber}
                  onChange={(e) =>
                    setDeliveryReferenceNumber(e.target.value)
                  }
                  disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                />
              </div>
              <div className="space-y-2">
                <Label>الموظف المسؤول</Label>
                <Input
                  value={deliveryEmployeeName || store.currentUser?.name || 'مدير النظام'}
                  disabled // منع تعديل اسم المستخدم
                  className="bg-gray-50"
                  title="اسم المستخدم محدد تلقائياً ولا يمكن تعديله"
                />
              </div>
              <div className="space-y-2">
                <Label>الموظف المستلم</Label>
                <Input
                  placeholder="أدخل اسم الموظف المستلم"
                  value={receivingEmployeeName}
                  onChange={(e) => setReceivingEmployeeName(e.target.value)}
                  disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                />
              </div>
              <div className="space-y-2">
                <Label>المخزن المستلم</Label>
                <Select
                  dir="rtl"
                  value={deliveryWarehouseId?.toString() || ''}
                  onValueChange={(value) => setDeliveryWarehouseId(parseInt(value))}
                  disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر المخزن..." />
                  </SelectTrigger>
                  <SelectContent>
                    {warehouses.map((warehouse) => (
                      <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                        {warehouse.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2 self-end">
                <Label htmlFor="delivery-attachment">إضافة مرفق</Label>
                <input
                  type="file"
                  id="delivery-attachment"
                  ref={deliveryAttachmentInputRef}
                  className="hidden"
                  onChange={(e) =>
                    setDeliveryAttachmentName(
                      e.target.files?.[0]?.name || '',
                    )
                  }
                />
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => deliveryAttachmentInputRef.current?.click()}
                  disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                >
                  <File className="ml-2 h-4 w-4" />
                  {deliveryAttachmentName || 'اختيار ملف...'}
                </Button>
              </div>

              {/* 📎 قسم المرفقات المتقدم للتسليم */}
              <div className="space-y-2">
                <Label>المرفقات المتقدمة</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => deliveryAttachmentsInputRef.current?.click()}
                    disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                    className="flex-1"
                  >
                    <File className="ml-2 h-4 w-4" />
                    إرفاق ملفات متعددة
                  </Button>
                  {deliveryAttachments.length > 0 && (
                    <Button
                      variant="outline"
                      onClick={() => setIsDeliveryAttachmentsModalOpen(true)}
                      className="px-3"
                    >
                      <span className="bg-green-500 text-white rounded-full px-2 py-1 text-xs">
                        {deliveryAttachments.length}
                      </span>
                    </Button>
                  )}
                </div>
                <input
                  ref={deliveryAttachmentsInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  onChange={(e) => e.target.files && handleDeliveryFileUpload(e.target.files)}
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>إضافة جهاز لدفعة التسليم</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Input
                    id="deliver-imei"
                    placeholder="امسح أو أدخل الرقم التسلسلي..."
                    value={imeiToDeliver}
                    onChange={(e) => setImeiToDeliver(e.target.value)}
                    onKeyDown={(e) =>
                      e.key === 'Enter' && handleAddToDelivery()
                    }
                    disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                  />
                  <Button
                    onClick={handleAddToDelivery}
                    disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                  >
                    إضافة
                  </Button>
                  <input
                    ref={deliveryImeiFileInputRef}
                    type="file"
                    className="hidden"
                    onChange={handleDeliveryFileImport}
                    accept=".txt"
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => deliveryImeiFileInputRef.current?.click()}
                    title="استيراد من ملف"
                    disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-2">
                  <Label>نتيجة الصيانة النهائية</Label>
                  <Select
                    dir="rtl"
                    value={deliveryResult}
                    onValueChange={(v: MaintenanceResult) => {
                      setDeliveryResult(v);
                      setDeliveryFault('');
                      setCustomDeliveryFault('');
                      setDeliveryDamage('');
                      setCustomDeliveryDamage('');
                    }}
                    disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Repaired">
                        تم الإصلاح - جاهز
                      </SelectItem>
                      <SelectItem value="Unrepairable-Defective">
                        معيوب فنيًا - لا يمكن إصلاحه
                      </SelectItem>
                      <SelectItem value="Unrepairable-Damaged">
                        تالف - لا يمكن إصلاحه
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {deliveryResult === 'Unrepairable-Defective' && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="space-y-2">
                      <Label>تحديد نوع العيب</Label>
                      <Select
                        dir="rtl"
                        value={deliveryFault}
                        onValueChange={(v: FaultType) => setDeliveryFault(v)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر نوع العيب..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="شاشة">شاشة</SelectItem>
                          <SelectItem value="بطارية">بطارية</SelectItem>
                          <SelectItem value="منفذ شحن">منفذ شحن</SelectItem>
                          <SelectItem value="كاميرا">كاميرا</SelectItem>
                          <SelectItem value="صوت">صوت</SelectItem>
                          <SelectItem value="لمس">لمس</SelectItem>
                          <SelectItem value="حساس">حساس</SelectItem>
                          <SelectItem value="هزاز">هزاز</SelectItem>
                          <SelectItem value="وايفاي">وايفاي</SelectItem>
                          <SelectItem value="ذاكره">ذاكره</SelectItem>
                          <SelectItem value="بطاقة sim">بطاقة SIM</SelectItem>
                          <SelectItem value="أعطال أخرى">أعطال أخرى</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {deliveryFault === 'أعطال أخرى' && (
                      <div className="space-y-2">
                        <Label>وصف العيب</Label>
                        <Input
                          placeholder="صف العيب..."
                          value={customDeliveryFault}
                          onChange={(e) =>
                            setCustomDeliveryFault(e.target.value)
                          }
                        />
                      </div>
                    )}
                  </div>
                )}

                {deliveryResult === 'Unrepairable-Damaged' && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="space-y-2">
                      <Label>تحديد نوع التلف</Label>
                      <Select
                        dir="rtl"
                        value={deliveryDamage}
                        onValueChange={(v: DamageType) => setDeliveryDamage(v)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="اختر نوع التلف..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="شاشة">شاشة</SelectItem>
                          <SelectItem value="ماذر بورد">ماذر بورد</SelectItem>
                          <SelectItem value="الغرق">الغرق</SelectItem>
                          <SelectItem value="أخرى">أخرى</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {deliveryDamage === 'أخرى' && (
                      <div className="space-y-2">
                        <Label>وصف التلف</Label>
                        <Input
                          placeholder="صف التلف..."
                          value={customDeliveryDamage}
                          onChange={(e) =>
                            setCustomDeliveryDamage(e.target.value)
                          }
                        />
                      </div>
                    )}
                  </div>
                )}
                <Textarea
                  placeholder="وصف الإصلاح / ملاحظات إضافية (اختياري)..."
                  value={deliveryItemNotes}
                  onChange={(e) => setDeliveryItemNotes(e.target.value)}
                  disabled={!canCreate || (!isCreatingDelivery && !loadedDelivOrder)}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  دفعة التسليم الحالية ({deliveryItems.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-80 overflow-y-auto border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-200">
                        <TableHead className="border border-gray-300 text-center font-bold text-gray-700 bg-green-200/70 py-2 text-sm w-12">#</TableHead>
                        <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-green-100/50 py-2 text-sm">الموديل</TableHead>
                        <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-green-100/50 py-2 text-sm">النتيجة / الوصف</TableHead>
                        <TableHead className="border border-gray-300 text-center font-semibold text-gray-700 bg-green-100/50 py-2 text-sm">حذف</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {deliveryItems.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={4} className="h-24 text-center">
                            لم يتم إضافة أجهزة بعد.
                          </TableCell>
                        </TableRow>
                      ) : (
                        deliveryItems.map(
                          ({ device, result, fault, damage, notes }, index) => (
                            <TableRow key={device.id} className={`hover:bg-green-50 transition-colors duration-200 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                              <TableCell className="border border-gray-300 text-center text-gray-600 font-bold py-2 text-sm w-12 bg-gray-50/50">
                                {index + 1}
                              </TableCell>
                              <TableCell className="border border-gray-300 text-right py-2 text-sm">
                                {device.model}{' '}
                                <span
                                  className="text-muted-foreground text-xs"
                                  dir="ltr"
                                >
                                  {device.id}
                                </span>
                              </TableCell>
                              <TableCell className="border border-gray-300 text-right py-2 text-sm">
                                <div className="flex flex-col items-start">
                                  <Badge
                                    variant={
                                      result === 'Repaired'
                                        ? 'default'
                                        : 'destructive'
                                    }
                                  >
                                    {result === 'Repaired'
                                      ? 'تم الإصلاح'
                                      : result === 'Unrepairable-Defective'
                                        ? 'معيوب'
                                        : 'تالف'}
                                  </Badge>
                                  {(fault || damage || notes) && (
                                    <span className="text-xs text-muted-foreground mt-1">
                                      {fault || damage || notes}
                                    </span>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell className="border border-gray-300 text-center py-2 text-sm">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() =>
                                    handleRemoveFromDelivery(device.id)
                                  }
                                >
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ),
                        )
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>



          <div className="flex flex-wrap justify-start gap-2 mt-4">
            {((canCreate && isCreatingDelivery) || (canEdit && loadedDelivOrder)) && (
              <Button
                onClick={handleSaveDeliveryOrder}
                disabled={deliveryItems.length === 0 && !loadedDelivOrder}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <Package className="ml-2 h-4 w-4" /> تسليم الدفعة للمخزن (
                {deliveryItems.length})
              </Button>
            )}
            {isCreatingDelivery && !loadedDelivOrder && (
              <Button
                variant="outline"
                onClick={saveDeliveryDraft}
                className="border-orange-300 text-orange-600 hover:bg-orange-50"
              >
                <FileDown className="ml-2 h-4 w-4" /> حفظ كمسودة
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => handlePrintDeliverySlip('print')}
              disabled={deliveryItems.length === 0 && !loadedDelivOrder}
            >
              <Printer className="ml-2 h-4 w-4" /> طباعة أمر التسليم
            </Button>
            <Button
              variant="outline"
              onClick={handleExportDeliveryExcel}
              disabled={deliveryItems.length === 0 && !loadedDelivOrder}
            >
              <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
            </Button>
            <Button variant="destructive" onClick={resetDeliveryPage}>
              <X className="ml-2 h-4 w-4" /> إلغاء التسليم
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="browse-inventory">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>البحث عن قطع غيار</CardTitle>
                <CardDescription>
                  استخدم الفلاتر للبحث عن أجهزة مطابقة يمكن استخدامها كقطع غيار
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="browse-model-filter">الموديل</Label>
                  <Popover
                    open={isModelSearchOpen}
                    onOpenChange={setIsModelSearchOpen}
                  >
                    <PopoverTrigger asChild>
                      <Button
                        id="browse-model-filter"
                        variant="outline"
                        role="combobox"
                        className="w-full justify-between"
                      >
                        {browseModelFilter || 'اختر موديلًا للبحث...'}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                      <Command>
                        <CommandInput placeholder="ابحث عن موديل..." />
                        <CommandList>
                          <CommandEmpty>لا يوجد موديل بهذا الاسم.</CommandEmpty>
                          <CommandGroup>
                            {allModels.map((model) => (
                              <CommandItem
                                key={model}
                                value={model}
                                onSelect={() => {
                                  setBrowseModelFilter(model);
                                  setIsModelSearchOpen(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    'ml-2 h-4 w-4',
                                    browseModelFilter === model
                                      ? 'opacity-100'
                                      : 'opacity-0',
                                  )}
                                />
                                {model}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
                

                
                <Button onClick={handleBrowseInventory} className="w-full">
                  <Search className="ml-2 h-4 w-4" /> بحث
                </Button>
              </CardContent>
            </Card>
            
            <Card className="md:col-span-2">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>نتائج البحث</CardTitle>
                  {browseModelFilter && (
                    <CardDescription>
                      عرض نتائج البحث عن موديل: {browseModelFilter}
                    </CardDescription>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsSparePartRequestDialogOpen(true)}
                  >
                    <MessageSquareQuote className="ml-2 h-4 w-4" /> طلب قطع
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {searchResults ? (
                  <>
                    <div className="grid grid-cols-1 gap-4 mb-4">
                      <Card className="bg-secondary/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold">بانتظار إرسال للصيانة</h3>
                            <Badge variant="secondary">
                              {searchResults.needsMaintenance.exists ? 'موجود' : 'غير موجود'}
                            </Badge>
                          </div>
                          {searchResults.needsMaintenance.exists && (
                            <p className="text-sm text-muted-foreground">
                              متوفر في: {searchResults.needsMaintenance.warehouseNames.join('، ')}
                            </p>
                          )}
                        </CardContent>
                      </Card>
                      
                      <Card className="bg-orange-500/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold">معيب</h3>
                            <Badge variant="secondary">
                              {searchResults.defective.exists ? 'موجود' : 'غير موجود'}
                            </Badge>
                          </div>
                          {searchResults.defective.exists && (
                            <p className="text-sm text-muted-foreground">
                              متوفر في: {searchResults.defective.warehouseNames.join('، ')}
                            </p>
                          )}
                        </CardContent>
                      </Card>
                      
                      <Card className="bg-red-500/10">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold">تالف</h3>
                            <Badge variant="secondary">
                              {searchResults.damaged.exists ? 'موجود' : 'غير موجود'}
                            </Badge>
                          </div>
                          {searchResults.damaged.exists && (
                            <p className="text-sm text-muted-foreground">
                              متوفر في: {searchResults.damaged.warehouseNames.join('، ')}
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                    
                    <div className="flex justify-center">
                      <Button 
                        onClick={() => setIsSparePartRequestDialogOpen(true)}
                        className="mt-4"
                      >
                        <MessageSquareQuote className="ml-2 h-4 w-4" />
                        إرسال طلب للإدارة
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center h-[300px] text-muted-foreground">
                    <PackageSearch className="h-16 w-16 mb-4 opacity-20" />
                    <p>استخدم خيارات البحث للعثور على قطع الغيار المتوفرة</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>الأجهزة داخل الصيانة وإجراءاتها</CardTitle>
              <CardDescription>
                استعراض جميع الأجهزة داخل الصيانة وتفاصيلها.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-200">
                    <TableHead className="border border-gray-300 text-center font-bold text-gray-700 bg-blue-200/70 py-2 text-sm w-12">#</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الرقم التسلسلي</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الموديل</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">العطل</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">رقم الأمر</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">تاريخ الاستلام</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الموظف</TableHead>
                    <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">الحالة</TableHead>
                    <TableHead className="border border-gray-300 text-center font-semibold text-gray-700 bg-blue-100/50 py-2 text-sm">إجراء</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devicesInMaintenance.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        لا توجد أجهزة قيد الصيانة حاليًا.
                      </TableCell>
                    </TableRow>
                  ) : (
                    devicesInMaintenance.map((device) => {
                      const order = store.maintenanceOrders.find(
                        (o) =>
                          o.status === 'wip' &&
                          Array.isArray(o.items) && o.items.some((i) => i.id === device.id)
    );
                      const faultLog = store.maintenanceHistory.find(
                        (log) => log.deviceId === device.id
    );
                      const deviceRequests = store.employeeRequests.filter(
                        (req) =>
                          req.relatedOrderId === order?.id &&
                          req.notes.includes(device.id)
    );
                      const lastRequest = deviceRequests.sort(
                        (a, b) =>
                          new Date(b.requestDate).getTime() -
                          new Date(a.requestDate).getTime(),
                      )[0];

                      return (
                        <TableRow key={device.id}>
                          <TableCell dir="ltr">{device.id}</TableCell>
                          <TableCell>{device.model}</TableCell>
                          <TableCell>{faultLog?.notes || 'غير محدد'}</TableCell>
                          <TableCell>{order?.orderNumber || '-'}</TableCell>
                          <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                            {order
                              ? formatDateTime(order.date)
                              : '-'}
                          </TableCell>
                          <TableCell>{order?.employeeName || '-'}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={cn(getStatusColor(device.status))}
                            >
                              {device.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="relative">
                            {lastRequest?.status === 'تم التنفيذ' && (
                              <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-green-500 animate-ping"></span>
                            )}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setDeviceForRequest(device);
                                setIsDeviceRequestDialogOpen(true);
                              }}
                            >
                              <MessageSquareQuote
                                className={cn(
                                  'ml-2 h-4 w-4',
                                  lastRequest?.status === 'قيد المراجعة' &&
                                    'text-orange-500',
                                  lastRequest?.status === 'تم التنفيذ' &&
                                    'text-green-500',
                                )}
                              />
                              طلب
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>تقارير و سجل الصيانة</CardTitle>
              <CardDescription>
                عرض إحصائيات وتفاصيل الأجهزة التي تمت صيانتها.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card
                  className="bg-green-500/10 hover:bg-green-500/20 cursor-pointer"
                  onClick={() =>
                    setDetailsModal({
                      isOpen: true,
                      title: 'الأجهزة التي تم إصلاحها',
                      data: store.maintenanceHistory.filter(
                        (log) => log.result === 'Repaired',
                      ),
                    })
                  }
                >
                  <CardHeader>
                    <CardTitle>تم إصلاحها</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">
                      {
                        store.maintenanceHistory.filter(
                          (log) => log.result === 'Repaired',
                        ).length
                      }
                    </p>
                  </CardContent>
                </Card>
                <Card
                  className="bg-red-500/10 hover:bg-red-500/20 cursor-pointer"
                  onClick={() =>
                    setDetailsModal({
                      isOpen: true,
                      title: 'الأجهزة التي لم يتم إصلاحها',
                      data: store.maintenanceHistory.filter(
                        (log) => log.result !== 'Repaired',
                      ),
                    })
                  }
                >
                  <CardHeader>
                    <CardTitle>لم يتم إصلاحها</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">
                      {
                        store.maintenanceHistory.filter(
                          (log) => log.result !== 'Repaired',
                        ).length
                      }
                    </p>
                  </CardContent>
                </Card>
                <Card
                  className="bg-blue-500/10 hover:bg-blue-500/20 cursor-pointer"
                  onClick={() =>
                    setDetailsModal({
                      isOpen: true,
                      title: 'الأجهزة قيد الإصلاح حالياً',
                      data: devicesInMaintenance,
                    })
                  }
                >
                  <CardHeader>
                    <CardTitle>قيد الإصلاح حالياً</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">
                      {devicesInMaintenance.length}
                    </p>
                  </CardContent>
                </Card>
                <Card
                  className="bg-yellow-500/10 hover:bg-yellow-500/20 cursor-pointer"
                  onClick={() =>
                    setDetailsModal({
                      isOpen: true,
                      title: 'الأجهزة المتأخرة',
                      data: devicesInMaintenance.filter(
                        (d) =>
                          new Date().getTime() -
                            new Date(
                              store.maintenanceOrders.find((o) =>
                                Array.isArray(o.items) && o.items.some((i) => i.id === d.id),
                              )?.date || new Date(),
                            ).getTime() >
                          7 * 24 * 60 * 60 * 1000,
                      ),
                    })
                  }
                >
                  <CardHeader>
                    <CardTitle>أجهزة متأخرة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">
                      {
                        devicesInMaintenance.filter(
                          (d) =>
                            new Date().getTime() -
                              new Date(
                                store.maintenanceOrders.find((o) =>
                                  Array.isArray(o.items) && o.items.some((i) => i.id === d.id),
                                )?.date || new Date(),
                              ).getTime() >
                            7 * 24 * 60 * 60 * 1000,
                        ).length
                      }
                    </p>
                  </CardContent>
                </Card>
              </div>
              <div className="flex flex-wrap items-center gap-4 p-4 border rounded-md">
                <div className="flex items-center gap-2">
                  <Label htmlFor="dateFrom">من تاريخ</Label>
                  <Input
                    id="dateFrom"
                    type="date"
                    value={historyFilter.dateFrom}
                    onChange={(e) =>
                      setHistoryFilter((f) => ({ ...f, dateFrom: e.target.value }))
                    }
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="dateTo">إلى تاريخ</Label>
                  <Input
                    id="dateTo"
                    type="date"
                    value={historyFilter.dateTo}
                    onChange={(e) =>
                      setHistoryFilter((f) => ({ ...f, dateTo: e.target.value }))
                    }
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Label>نتيجة الصيانة</Label>
                  <Select
                    value={historyFilter.result}
                    onValueChange={(v: MaintenanceResult | 'all') =>
                      setHistoryFilter((f) => ({ ...f, result: v }))
                    }
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">الكل</SelectItem>
                      <SelectItem value="Repaired">تم الإصلاح</SelectItem>
                      <SelectItem value="Unrepairable-Defective">
                        معيوب
                      </SelectItem>
                      <SelectItem value="Unrepairable-Damaged">تالف</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="orderNumberFilter">رقم أمر الاستلام/التسليم</Label>
                  <Input
                    id="orderNumberFilter"
                    placeholder="MAINT-..., DELIV-..."
                    value={historyFilter.orderNumber}
                    onChange={(e) =>
                      setHistoryFilter((f) => ({
                        ...f,
                        orderNumber: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>سجل الصيانة المفصل</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الرقم التسلسلي</TableHead>
                    <TableHead>الموديل</TableHead>
                    <TableHead>أمر الاستلام</TableHead>
                    <TableHead>أمر التسليم</TableHead>
                    <TableHead>تاريخ الإرسال</TableHead>
                    <TableHead>النتيجة</TableHead>
                    <TableHead>تاريخ الاستلام</TableHead>
                    <TableHead>الموظف المستلم</TableHead>
                    <TableHead>ملاحظات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {store.maintenanceHistory.filter((log) => {
                    const repairDate = new Date(log.repairDate);
                    const from = historyFilter.dateFrom
                      ? new Date(historyFilter.dateFrom)
                      : null;
                    const to = historyFilter.dateTo
                      ? new Date(historyFilter.dateTo)
                      : null;
                    if (from && repairDate < from) return false;
                    if (to && repairDate > to) return false;
                    if (
                      historyFilter.result !== 'all' &&
                      log.result !== historyFilter.result
                    ) return false;
                    const maintOrder = store.maintenanceOrders.find((o) =>
                      Array.isArray(o.items) && o.items.some((i) => i.id === log.deviceId)
    );
                    const delivOrder = store.deliveryOrders.find((o) =>
                      Array.isArray(o.items) && o.items.some((i) => i.deviceId === log.deviceId)
    );
                    if (
                      historyFilter.orderNumber &&
                      !maintOrder?.orderNumber.includes(
                        historyFilter.orderNumber,
                      ) &&
                      !delivOrder?.deliveryOrderNumber.includes(
                        historyFilter.orderNumber,
                      )
                    )
                      return false;
                    return true;
                  }).length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        لا توجد سجلات صيانة تطابق الفلترة.
                      </TableCell>
                    </TableRow>
                  ) : (
                    store.maintenanceHistory
                      .filter((log) => {
                        const repairDate = new Date(log.repairDate);
                        const from = historyFilter.dateFrom
                          ? new Date(historyFilter.dateFrom)
                          : null;
                        const to = historyFilter.dateTo
                          ? new Date(historyFilter.dateTo)
                          : null;
                        if (from && repairDate < from) return false;
                        if (to && repairDate > to) return false;
                        if (
                          historyFilter.result !== 'all' &&
                          log.result !== historyFilter.result
                        ) return false;
                        const maintOrder = store.maintenanceOrders.find((o) =>
                          Array.isArray(o.items) && o.items.some((i) => i.id === log.deviceId)
    );
                        const delivOrderForFilter = store.deliveryOrders.find(
                          (o) => Array.isArray(o.items) && o.items.some((i) => i.deviceId === log.deviceId)
    );
                        if (
                          historyFilter.orderNumber &&
                          !maintOrder?.orderNumber.includes(
                            historyFilter.orderNumber,
                          ) &&
                          !delivOrderForFilter?.deliveryOrderNumber.includes(
                            historyFilter.orderNumber,
                          )
                        )
                          return false;
                        return true;
                      })
                      .map((log) => {
                        const maintOrder = store.maintenanceOrders.find((o) =>
                          Array.isArray(o.items) && o.items.some((i) => i.id === log.deviceId)
    );
                        const delivOrder = store.deliveryOrders.find((o) =>
                          Array.isArray(o.items) && o.items.some((i) => i.deviceId === log.deviceId)
    );
                        return (
                          <TableRow key={log.deviceId + log.repairDate}>
                            <TableCell dir="ltr">{log.deviceId}</TableCell>
                            <TableCell>{log.model}</TableCell>
                            <TableCell>{maintOrder?.orderNumber || '-'}</TableCell>
                            <TableCell>
                              {delivOrder?.deliveryOrderNumber || '-'}
                            </TableCell>
                            <TableCell>
                              {format(new Date(log.repairDate), 'yyyy-MM-dd')}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  log.result === 'Repaired'
                                    ? 'default'
                                    : 'destructive'
                                }
                              >
                                {log.result === 'Repaired' && 'تم الإصلاح'}
                                {log.result === 'Unrepairable-Defective' &&
                                  'معيوب'}
                                {log.result === 'Unrepairable-Damaged' && 'تالف'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {log.acknowledgedDate
                                ? format(
                                    new Date(log.acknowledgedDate),
                                    'yyyy-MM-dd',
                                  )
                                : '-'}
                            </TableCell>
                            <TableCell>{log.acknowledgedBy || '-'}</TableCell>
                            <TableCell>{log.notes || '-'}</TableCell>
                          </TableRow>
                        );
                      })
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog
        open={isLoadMaintOrderDialogOpen}
        onOpenChange={setIsLoadMaintOrderDialogOpen}
      >
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>تحميل أمر صيانة سابق</DialogTitle>
            <DialogDescription>
              اختر أمرًا لتحميله وتعديله في شاشة استلام الأجهزة.
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الأمر</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الموظف</TableHead>
                  <TableHead>عدد الأجهزة</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {store.maintenanceOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      لا توجد أوامر سابقة.
                    </TableCell>
                  </TableRow>
                ) : (
                  store.maintenanceOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>{order.orderNumber}</TableCell>
                      <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                        {formatDateTime(order.date)}
                      </TableCell>
                      <TableCell>{order.employeeName}</TableCell>
                      <TableCell>{Array.isArray(order.items) ? order.items.length : 0}</TableCell>
                      <TableCell className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleLoadMaintOrder(order)}
                        >
                          تحميل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={!!maintOrderToDelete}
        onOpenChange={() => setMaintOrderToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيؤدي هذا الإجراء إلى حذف أمر الصيانة بشكل دائم. سيتم إعادة حالة
              الأجهزة الموجودة فيه إلى "تحتاج صيانة".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteMaintOrder}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog
        open={!!requestMaintOrder}
        onOpenChange={() => setRequestMaintOrder(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إرسال ملاحظة للإدارة</DialogTitle>
            <DialogDescription>
              بخصوص أمر الصيانة رقم: {requestMaintOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>نوع الطلب</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.requestType}
                  onValueChange={(v: EmployeeRequestType) =>
                    setRequestFormData((s) => ({ ...s, requestType: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تعديل">تعديل</SelectItem>
                    <SelectItem value="إعادة نظر">إعادة نظر</SelectItem>
                    <SelectItem value="حذف">حذف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>الأولوية</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.priority}
                  onValueChange={(v: EmployeeRequestPriority) =>
                    setRequestFormData((s) => ({ ...s, priority: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="عادي">عادي</SelectItem>
                    <SelectItem value="طاريء">طاريء</SelectItem>
                    <SelectItem value="طاريء جدا">طاريء جداً</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label>تفاصيل المشكلة / الملاحظة</Label>
              <Textarea
                placeholder="اشرح المشكلة أو الطلب بالتفصيل..."
                value={requestFormData.notes}
                onChange={(e) =>
                  setRequestFormData((s) => ({ ...s, notes: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label>إرفاق ملف (اختياري)</Label>
              <Input
                type="file"
                ref={attachmentRequestInputRef}
                onChange={(e) =>
                  setRequestFormData((s) => ({
                    ...s,
                    attachmentName: e.target.files?.[0]?.name || '',
                  }))
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSendMaintRequest}>إرسال الطلب</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={isLoadDelivOrderDialogOpen}
        onOpenChange={setIsLoadDelivOrderDialogOpen}
      >
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>تحميل أمر تسليم سابق</DialogTitle>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الأمر</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الموظف</TableHead>
                  <TableHead>عدد الأجهزة</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {store.deliveryOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      لا توجد أوامر سابقة.
                    </TableCell>
                  </TableRow>
                ) : (
                  store.deliveryOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>{order.deliveryOrderNumber}</TableCell>
                      <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                        {formatDateTime(order.date)}
                      </TableCell>
                      <TableCell>{order.employeeName}</TableCell>
                      <TableCell>{Array.isArray(order.items) ? order.items.length : 0}</TableCell>
                      <TableCell className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleLoadDelivOrder(order)}
                        >
                          تحميل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <AlertDialog
        open={!!delivOrderToDelete}
        onOpenChange={() => setDelivOrderToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيؤدي هذا الإجراء إلى حذف أمر التسليم بشكل دائم.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDelivOrder}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog
        open={!!requestDelivOrder}
        onOpenChange={() => setRequestDelivOrder(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إرسال ملاحظة للإدارة</DialogTitle>
            <DialogDescription>
              بخصوص أمر التسليم رقم: {requestDelivOrder?.deliveryOrderNumber}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>نوع الطلب</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.requestType}
                  onValueChange={(v: EmployeeRequestType) =>
                    setRequestFormData((s) => ({ ...s, requestType: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تعديل">تعديل</SelectItem>
                    <SelectItem value="إعادة نظر">إعادة نظر</SelectItem>
                    <SelectItem value="حذف">حذف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>الأولوية</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.priority}
                  onValueChange={(v: EmployeeRequestPriority) =>
                    setRequestFormData((s) => ({ ...s, priority: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="عادي">عادي</SelectItem>
                    <SelectItem value="طاريء">طاريء</SelectItem>
                    <SelectItem value="طاريء جدا">طاريء جداً</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label>تفاصيل المشكلة / الملاحظة</Label>
              <Textarea
                placeholder="اشرح المشكلة أو الطلب بالتفصيل..."
                value={requestFormData.notes}
                onChange={(e) =>
                  setRequestFormData((s) => ({ ...s, notes: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label>إرفاق ملف (اختياري)</Label>
              <Input
                type="file"
                ref={attachmentRequestInputRef}
                onChange={(e) =>
                  setRequestFormData((s) => ({
                    ...s,
                    attachmentName: e.target.files?.[0]?.name || '',
                  }))
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSendDelivRequest}>إرسال الطلب</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isSparePartRequestDialogOpen}
        onOpenChange={setIsSparePartRequestDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>طلب قطع غيار للإدارة</DialogTitle>
            <DialogDescription>
              أرسل طلبًا لتوفير قطع غيار للموديل: {browseModelFilter}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="request-notes">ملاحظات الطلب</Label>
            <Textarea
              id="request-notes"
              value={sparePartRequestNotes}
              onChange={(e) => setSparePartRequestNotes(e.target.value)}
              placeholder="صف سبب الطلب والكمية المطلوبة..."
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsSparePartRequestDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={handleSendSparePartRequest}>إرسال الطلب</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={detailsModal.isOpen}
        onOpenChange={() =>
          setDetailsModal((prev) => ({ ...prev, isOpen: false }))
        }
      >
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {detailsModal.title.includes('قيد العمل') && <Wrench className="h-5 w-5 text-blue-600" />}
              {detailsModal.title.includes('بانتظار الاستلام في الصيانة') && <Inbox className="h-5 w-5 text-yellow-600" />}
              {detailsModal.title.includes('المرسلة للمخزن') && <Send className="h-5 w-5 text-purple-600" />}
              {detailsModal.title.includes('المستلمة مباشرة') && <Package className="h-5 w-5 text-green-600" />}
              {detailsModal.title}
            </DialogTitle>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            {detailsModal.data.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                لا توجد أجهزة في هذه الفئة حالياً
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الرقم التسلسلي</TableHead>
                    <TableHead>الموديل</TableHead>
                    <TableHead>الحالة</TableHead>
                    {detailsModal.title.includes('قيد العمل') && <TableHead>أمر الصيانة</TableHead>}
                    {detailsModal.title.includes('المستلمة مباشرة') && <TableHead>رقم الأمر</TableHead>}
                    {(detailsModal.title.includes('بانتظار الاستلام في الصيانة') || 
                      detailsModal.title.includes('المرسلة للمخزن')) && <TableHead>إجراء</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {detailsModal.data.map((device) => {
                    const order = store.maintenanceOrders.find((o) =>
                      Array.isArray(o.items) && o.items.some((i) => i.id === device.id)
    );
                    return (
                      <TableRow key={device.id || device.deviceId}>
                        <TableCell dir="ltr" className="font-mono">
                          {device.id || device.deviceId}
                        </TableCell>
                        <TableCell>{device.model}</TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={getStatusColor(device.status)}
                          >
                            {device.status || device.result}
                          </Badge>
                        </TableCell>
                        {detailsModal.title.includes('قيد العمل') && (
                          <TableCell>
                            {order?.orderNumber || 'إضافة مباشرة'}
                          </TableCell>
                        )}
                        {detailsModal.title.includes('المستلمة مباشرة') && (
                          <TableCell>
                            {order?.orderNumber || '-'}
                          </TableCell>
                        )}
                        {detailsModal.title.includes('بانتظار الاستلام في الصيانة') && (
                          <TableCell>
                            <Button
                              size="sm"
                              onClick={() => {
                                handleReceiveAwaitingDevice(device.id);
                                setDetailsModal(prev => ({
                                  ...prev,
                                  data: prev.data.filter(d => d.id !== device.id)
                                }));
                              }}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <Check className="ml-2 h-4 w-4" /> استلام
                            </Button>
                          </TableCell>
                        )}
                        {detailsModal.title.includes('المرسلة للمخزن') && (
                          <TableCell>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                handleRevertToMaintenance(device.id);
                                setDetailsModal(prev => ({
                                  ...prev,
                                  data: prev.data.filter(d => d.id !== device.id)
                                }));
                              }}
                            >
                              <RotateCcw className="ml-2 h-4 w-4" />
                              استعادة
                            </Button>
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </div>
          <DialogFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              إجمالي الأجهزة: {detailsModal.data.length}
            </div>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isDeviceRequestDialogOpen}
        onOpenChange={setIsDeviceRequestDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إرسال طلب للإدارة</DialogTitle>
            <DialogDescription>
              بخصوص الجهاز: {deviceForRequest?.model} (
              <span dir="ltr">{deviceForRequest?.id}</span>)
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="p-4 bg-secondary/50 rounded-md">
              <h4 className="font-semibold mb-2">تفاصيل الجهاز</h4>
              <p>
                <strong>الموديل:</strong> {deviceForRequest?.model}
              </p>
              <p>
                <strong>الحالة الحالية:</strong> {deviceForRequest?.status}
              </p>
              <p>
                <strong>رقم أمر الصيانة:</strong>{' '}
                {
                  store.maintenanceOrders.find((o) =>
                    Array.isArray(o.items) && o.items.some((i) => i.id === deviceForRequest?.id),
                  )?.orderNumber
                }
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold">سجل الطلبات السابقة</h4>
              <div className="max-h-40 overflow-y-auto space-y-2 rounded-md border p-2">
                {store.employeeRequests
                  .filter(
                    (req) =>
                      req.relatedOrderId ===
                        store.maintenanceOrders.find((o) =>
                          Array.isArray(o.items) && o.items.some((i) => i.id === deviceForRequest?.id),
                        )?.id && req.notes.includes(deviceForRequest?.id || ''),
                  )
                  .map((req) => (
                    <div key={req.id} className="text-xs">
                      <p>
                        <strong>الطلب:</strong> {req.notes}
                      </p>
                      <p>
                        <strong>الحالة:</strong> {req.status}
                      </p>
                      {req.adminNotes && (
                        <p className="text-blue-600">
                          <strong>رد الإدارة:</strong> {req.adminNotes}
                        </p>
                      )}
                    </div>
                  ))}
                {store.employeeRequests.filter(
                  (req) =>
                    req.relatedOrderId ===
                      store.maintenanceOrders.find((o) =>
                        Array.isArray(o.items) && o.items.some((i) => i.id === deviceForRequest?.id),
                      )?.id && req.notes.includes(deviceForRequest?.id || ''),
                ).length === 0 && (
                  <p className="text-xs text-muted-foreground">
                    لا توجد طلبات سابقة لهذا الجهاز.
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="device-request-notes">
                تفاصيل الطلب الجديد (مثال: طلب قطعة غيار، استفسار، إلخ)
              </Label>
              <Textarea
                id="device-request-notes"
                value={deviceRequestNotes}
                onChange={(e) => setDeviceRequestNotes(e.target.value)}
                placeholder="اكتب تفاصيل طلبك هنا..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeviceRequestDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={handleSendDeviceRequest}>إرسال الطلب</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 📎 مكونات عرض المرفقات */}
      <AttachmentsViewer
        isOpen={isReceiveAttachmentsModalOpen}
        onClose={() => setIsReceiveAttachmentsModalOpen(false)}
        attachments={receiveAttachments}
        onAttachmentsChange={setReceiveAttachments}
        title="مرفقات أمر الاستلام"
        section="maintenance"
        canDelete={canDelete}
      />

      <AttachmentsViewer
        isOpen={isDeliveryAttachmentsModalOpen}
        onClose={() => setIsDeliveryAttachmentsModalOpen(false)}
        attachments={deliveryAttachments}
        onAttachmentsChange={setDeliveryAttachments}
        title="مرفقات أمر التسليم"
        section="maintenance"
        canDelete={canDelete}
      />
      </div>
    </PermissionGuard>
  );
}