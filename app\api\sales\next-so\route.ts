import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, generateSequentialSONumber } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // إنشاء رقم SO التالي
    const result = await executeInTransaction(async (tx) => {
      const nextSONumber = await generateSequentialSONumber(tx);
      return { soNumber: nextSONumber };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to generate SO number:', error);
    return NextResponse.json({ error: 'Failed to generate SO number' }, { status: 500 });
  }
}
