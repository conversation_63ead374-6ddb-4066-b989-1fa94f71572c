'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Trash2, RefreshCw, AlertTriangle, Database, Eye } from 'lucide-react';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface EvaluationOrder {
  id: number;
  orderId: string;
  employeeName: string;
  date: string;
  createdAt: string;
  itemsCount: number;
}

interface DuplicateGroup {
  orderId: string;
  count: number;
  orders: EvaluationOrder[];
}

export function EvaluationCleanup() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [duplicates, setDuplicates] = useState<DuplicateGroup[]>([]);
  const [allOrders, setAllOrders] = useState<EvaluationOrder[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState<'duplicates' | 'all'>('duplicates');

  const loadDuplicates = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/evaluations/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'list_duplicates' })
      });

      if (response.ok) {
        const data = await response.json();
        setDuplicates(data.duplicates);
        toast({
          title: 'تم التحميل',
          description: `تم العثور على ${data.totalDuplicateGroups} مجموعة من الأوامر المكررة`,
        });
      } else {
        throw new Error('فشل في تحميل البيانات');
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل الأوامر المكررة',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadAllOrders = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/evaluations/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'list_all' })
      });

      if (response.ok) {
        const data = await response.json();
        setAllOrders(data.orders);
        toast({
          title: 'تم التحميل',
          description: `تم تحميل ${data.total} أمر تقييم`,
        });
      } else {
        throw new Error('فشل في تحميل البيانات');
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل أوامر التقييم',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const removeDuplicates = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/evaluations/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'remove_duplicates' })
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: 'تم الحذف بنجاح',
          description: data.message,
        });
        // إعادة تحميل البيانات
        await loadDuplicates();
      } else {
        throw new Error('فشل في حذف الأوامر المكررة');
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في حذف الأوامر المكررة',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const deleteSelectedOrders = async () => {
    if (selectedIds.length === 0) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/evaluations/cleanup', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: selectedIds })
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: 'تم الحذف بنجاح',
          description: data.message,
        });
        setSelectedIds([]);
        setShowConfirmDialog(false);
        // إعادة تحميل البيانات
        if (activeTab === 'duplicates') {
          await loadDuplicates();
        } else {
          await loadAllOrders();
        }
      } else {
        throw new Error('فشل في حذف الأوامر');
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في حذف الأوامر المحددة',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (date: Date | string) => {
    return new Date(dateString);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            إدارة أوامر التقييم
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <Button
              variant={activeTab === 'duplicates' ? 'default' : 'outline'}
              onClick={() => setActiveTab('duplicates')}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              الأوامر المكررة
            </Button>
            <Button
              variant={activeTab === 'all' ? 'default' : 'outline'}
              onClick={() => setActiveTab('all')}
            >
              <Eye className="h-4 w-4 mr-2" />
              جميع الأوامر
            </Button>
          </div>

          <div className="flex gap-2 mb-4">
            <Button
              onClick={activeTab === 'duplicates' ? loadDuplicates : loadAllOrders}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              تحديث البيانات
            </Button>

            {activeTab === 'duplicates' && duplicates.length > 0 && (
              <Button
                variant="destructive"
                onClick={removeDuplicates}
                disabled={isLoading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                حذف جميع المكررات
              </Button>
            )}

            {selectedIds.length > 0 && (
              <Button
                variant="destructive"
                onClick={() => setShowConfirmDialog(true)}
                disabled={isLoading}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                حذف المحدد ({selectedIds.length})
              </Button>
            )}
          </div>

          {activeTab === 'duplicates' && (
            <div className="space-y-4">
              {duplicates.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد أوامر مكررة
                </div>
              ) : (
                duplicates.map((group) => (
                  <Card key={group.orderId} className="border-orange-200">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">
                          أمر التقييم: {group.orderId}
                        </CardTitle>
                        <Badge variant="destructive">
                          {group.count} نسخة مكررة
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>الموظف</TableHead>
                            <TableHead>التاريخ</TableHead>
                            <TableHead>تاريخ الإنشاء</TableHead>
                            <TableHead>عدد الأجهزة</TableHead>
                            <TableHead>إجراء</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {group.orders.map((order, index) => (
                            <TableRow key={order.id} className={index === group.orders.length - 1 ? 'bg-green-50' : 'bg-red-50'}>
                              <TableCell>{order.id}</TableCell>
                              <TableCell>{order.employeeName}</TableCell>
                              <TableCell>{formatDate(order.date)}</TableCell>
                              <TableCell>{formatDate(order.createdAt)}</TableCell>
                              <TableCell>{order.itemsCount}</TableCell>
                              <TableCell>
                                {index === group.orders.length - 1 ? (
                                  <Badge variant="secondary">سيتم الاحتفاظ به</Badge>
                                ) : (
                                  <Badge variant="destructive">سيتم حذفه</Badge>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}

          {activeTab === 'all' && (
            <div className="space-y-4">
              {allOrders.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد أوامر تقييم
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <input
                          type="checkbox"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedIds(allOrders.map(o => o.id));
                            } else {
                              setSelectedIds([]);
                            }
                          }}
                          checked={selectedIds.length === allOrders.length}
                        />
                      </TableHead>
                      <TableHead>رقم الأمر</TableHead>
                      <TableHead>الموظف</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>عدد الأجهزة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedIds.includes(order.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedIds([...selectedIds, order.id]);
                              } else {
                                setSelectedIds(selectedIds.filter(id => id !== order.id));
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>{order.orderId}</TableCell>
                        <TableCell>{order.employeeName}</TableCell>
                        <TableCell>{formatDate(order.date)}</TableCell>
                        <TableCell>{order.itemsCount}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من حذف {selectedIds.length} أمر تقييم؟ هذا الإجراء لا يمكن التراجع عنه.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={deleteSelectedOrders}>
              حذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
