/**
 * Fix React Date Display Errors Script
 * Date: 2025-08-04
 * Description: Fix all Date objects being displayed directly in JSX
 */

const fs = require('fs');
const path = require('path');

// Search for files with React date display errors
function findDateDisplayErrors() {
  const results = [];
  
  function searchInFile(filePath) {
    if (!fs.existsSync(filePath)) return [];
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Check for {new Date(...)} patterns in JSX
      if (line.includes('{new Date(') && line.includes(')}') && !line.includes('formatDate') && !line.includes('formatDateTime')) {
        // Skip if it's already formatted or in comments
        if (!line.trim().startsWith('//') && !line.trim().startsWith('*')) {
          issues.push({
            file: filePath,
            line: lineNumber,
            content: line.trim(),
            type: 'Direct Date Display in JSX'
          });
        }
      }
      
      // Check for other Date display patterns
      if (line.includes('{') && line.includes('date') && line.includes('}') && 
          !line.includes('formatDate') && !line.includes('formatDateTime') &&
          !line.includes('toISOString') && !line.includes('slice') &&
          !line.includes('value=') && !line.includes('defaultValue=')) {
        // This might be a date being displayed directly
        if (line.includes('Date(') || line.includes('.date}')) {
          issues.push({
            file: filePath,
            line: lineNumber,
            content: line.trim(),
            type: 'Potential Date Display Issue'
          });
        }
      }
    });
    
    return issues;
  }
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (item !== 'node_modules' && item !== '.next' && item !== '.git') {
          walkDir(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (['.tsx', '.jsx'].includes(ext)) {
          const fileIssues = searchInFile(fullPath);
          results.push(...fileIssues);
        }
      }
    }
  }
  
  walkDir(process.cwd());
  return results;
}

// Fixes for React date display errors
const dateDisplayFixes = [
  // Fix direct Date object display in JSX
  {
    search: /\{new Date\([^}]+\)\}/g,
    replace: function(match) {
      // Extract the date parameter
      const dateParam = match.match(/new Date\(([^)]+)\)/)[1];
      return `{formatDateTime(new Date(${dateParam}))}`;
    },
    description: 'إصلاح عرض Date object مباشرة في JSX'
  },
  
  // Fix specific patterns
  {
    search: /\{new Date\(\)\}/g,
    replace: '{formatDateTime(new Date())}',
    description: 'إصلاح عرض new Date() مباشرة في JSX'
  },
  
  // Fix date properties being displayed directly
  {
    search: /\{([^}]*\.date)\}/g,
    replace: function(match, dateProperty) {
      if (!dateProperty.includes('formatDate') && !dateProperty.includes('formatDateTime')) {
        return `{formatDateTime(new Date(${dateProperty}))}`;
      }
      return match;
    },
    description: 'إصلاح عرض خصائص التاريخ مباشرة'
  }
];

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    
    if (typeof fix.replace === 'function') {
      content = content.replace(fix.search, fix.replace);
    } else {
      content = content.replace(fix.search, fix.replace);
    }
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixReactDateDisplayErrors() {
  console.log('🚨 البحث عن أخطاء عرض التواريخ في React وإصلاحها...\n');

  try {
    // Find all date display errors
    console.log('🔍 البحث عن أخطاء عرض التواريخ...');
    const dateErrors = findDateDisplayErrors();
    
    if (dateErrors.length === 0) {
      console.log('✅ لم يتم العثور على أخطاء عرض التواريخ');
      return;
    }
    
    console.log(`🚨 تم العثور على ${dateErrors.length} خطأ محتمل في عرض التواريخ:\n`);
    
    // Display errors
    dateErrors.forEach(error => {
      console.log(`📁 ${path.relative(process.cwd(), error.file)}`);
      console.log(`   🔴 السطر ${error.line}: ${error.content}`);
      console.log(`   📝 النوع: ${error.type}`);
      console.log('');
    });
    
    // Get unique files
    const uniqueFiles = [...new Set(dateErrors.map(error => error.file))];
    
    console.log(`🔧 إصلاح ${uniqueFiles.length} ملف...\n`);
    
    let totalFixed = 0;
    let filesModified = [];
    
    for (const file of uniqueFiles) {
      console.log(`🔍 فحص: ${path.basename(file)}`);
      
      // Add date-utils import if needed
      addDateUtilsImport(file);
      
      if (applyFixes(file, dateDisplayFixes)) {
        totalFixed += dateDisplayFixes.length;
        filesModified.push(file);
      }
    }
    
    // Generate summary
    console.log('\n📊 ملخص إصلاح أخطاء عرض التواريخ:');
    console.log('='.repeat(50));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`🚨 أخطاء عرض التواريخ المكتشفة: ${dateErrors.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }
    
    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      dateErrors: dateErrors,
      fixes: dateDisplayFixes
    };
    
    fs.writeFileSync('react-date-display-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح عرض التواريخ في: react-date-display-fixes-report.json');
    
    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح جميع أخطاء عرض التواريخ بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على أخطاء عرض التواريخ للإصلاح');
    }
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح أخطاء عرض التواريخ:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixReactDateDisplayErrors()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح أخطاء عرض التواريخ');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح أخطاء عرض التواريخ:', error);
      process.exit(1);
    });
}

module.exports = { fixReactDateDisplayErrors };
