{"timestamp": "2025-08-05T00:28:36.394Z", "totalFixes": 450, "filesModified": ["lib/date-utils.ts", "lib/export-utils/enhanced-html-export.ts", "lib/print-templates/index.ts", "lib/search-service.ts", "lib/types.ts", "app/(main)/grading/2page.tsx", "app/(main)/maintenance/MaintenanceMemo.tsx", "app/(main)/messaging/page.tsx", "app/(main)/supply/page.tsx", "app/(main)/track/DeviceHistoryTimeline.tsx", "app/(main)/track/DeviceTrackingFilters.tsx", "app/(main)/track/page.tsx", "app/(main)/track/simple-page.tsx", "components/AttachmentsViewer.tsx", "components/evaluation-cleanup.tsx", "components/ReportPreview.tsx", "components/requests/EscalationDashboard.tsx", "context/stocktake-store.tsx"], "totalFilesProcessed": 30, "fixes": [{"search": {}, "replace": "export function formatDateForCSV(date: Date | null | undefined): string", "description": "تحسين نوع formatDateForCSV (مقبول - يرجع string)"}, {"search": {}, "replace": "export function getRelativeTimeArabic(date: Date | null | undefined): string", "description": "تحسين نوع getRelativeTimeArabic (مقبول - يرجع string)"}, {"search": {}, "replace": "export function getCurrentArabicDate(): string", "description": "تحسين نوع getCurrentArabicDate (مقبول - يرجع string)"}, {"search": {}, "replace": "export function parseArabicDate(input: string | Date | null | undefined): Date", "description": "تحسين نوع parseArabicDate (مقبول - يقبل string ويرجع Date)"}, {"search": {}, "replace": "export function formatArabicDate(date: Date): string", "description": "تحسين formatArabicDate لقبول Date فقط"}, {"search": {}, "replace": "export function formatArabicDateOnly(date: Date): string", "description": "تحسين formatArabicDateOnly لقبول Date فقط"}, {"search": {}, "replace": "export function formatArabicTime(date: Date): string", "description": "تحسين formatArabicTime لقبول Date فقط"}, {"search": {}, "replace": "export function formatShortDate(date: Date): string", "description": "تحسين formatShortDate لقبول Date فقط"}, {"search": {}, "replace": "date: Date;", "description": "تحويل date من string إلى Date في الواجهات"}, {"search": {}, "replace": "expiryDate: Date;", "description": "تحويل expiryDate من string إلى Date"}, {"search": {}, "replace": "requestDate: Date;", "description": "تحويل requestDate من string إلى Date"}, {"search": {}, "replace": "maintenanceStartDate: Date;", "description": "تحويل maintenanceStartDate من string إلى Date"}, {"search": {}, "replace": "lastMessageDate: Date;", "description": "تحويل lastMessageDate من string إلى Date"}, {"search": {}, "replace": "scheduledDate?: Date;", "description": "تحويل scheduledDate من string إلى Date"}, {"search": {}, "replace": "function formatArabicDate(date: Date): string", "description": "تحسين دالة formatArabicDate"}, {"search": {}, "replace": "const formatDateTime = (dateTime: Date): string", "description": "تحسين دالة formatDateTime"}, {"search": {}, "replace": "const formatUploadDate = (date: Date): string", "description": "تحسين دالة formatUploadDate"}, {"search": {}, "replace": "export function validateIpAddress(ip: string): boolean", "description": "تحسين validateIpAddress (مقبول - لا علاقة بالتواريخ)"}, {"search": {}, "replace": "export function formatConnectionTime(date: Date): string", "description": "تحسين formatConnectionTime (مقبول - يقبل Date ويرجع string)"}, {"search": {}, "replace": "static getOverdueTimeText(priority: string): string", "description": "تحسين getOverdueTimeText (مقبول - لا علاقة بالتواريخ)"}, {"search": {}, "replace": "static getTimeText(minutes: number): string", "description": "تحسين getTimeText (مقبول - لا علاقة بالتواريخ)"}, {"search": {}, "replace": "formatDate: (date: Date): string", "description": "تحسين formatDate في templates"}, {"search": {}, "replace": "formattedDate?: string; // يبقى string للعرض", "description": "الاحتفاظ بـ formattedDate كـ string (للعرض)"}, {"search": {}, "replace": "timezone?: string; // يبقى string (ليس تاريخ)", "description": "الاحتفاظ بـ timezone كـ string (ليس تاريخ)"}, {"search": {}, "replace": "id: string; // يبقى string (معرف وليس تاريخ)", "description": "الاحتفاظ بـ id كـ string (معرف وليس تاريخ)"}]}