/**
 * فحص وإصلاح مشاكل employee_requests - Employee Requests Fix
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixEmployeeRequestsIssues() {
  console.log('🔧 فحص وإصلاح مشاكل employee_requests...\n');
  
  try {
    // 1. فحص بنية الجدول الحالية
    console.log('1️⃣ فحص بنية جدول employee_requests...');
    
    try {
      const result = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'employee_requests' 
        ORDER BY ordinal_position;
      `;
      
      console.log('   الأعمدة الموجودة:');
      result.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type}, nullable: ${col.is_nullable})`);
      });
      
      // البحث عن العمود attachments
      const hasAttachments = result.some(col => col.column_name === 'attachments');
      console.log(`   العمود attachments: ${hasAttachments ? '✅ موجود' : '❌ غير موجود'}`);
      
    } catch (error) {
      console.log('   ❌ فشل في فحص بنية الجدول:', error.message);
    }
    
    // 2. محاولة إضافة العمود المفقود
    console.log('\n2️⃣ إضافة العمود attachments إذا كان مفقوداً...');
    
    try {
      await prisma.$executeRaw`
        ALTER TABLE "employee_requests" 
        ADD COLUMN IF NOT EXISTS "attachments" JSONB;
      `;
      console.log('   ✅ تم إضافة العمود attachments بنجاح');
    } catch (error) {
      console.log('   ⚠️ العمود attachments موجود بالفعل أو هناك خطأ:', error.message);
    }
    
    // 3. إضافة أعمدة إضافية قد تكون مفقودة
    console.log('\n3️⃣ إضافة أعمدة إضافية...');
    
    const additionalColumns = [
      { name: 'tags', type: 'JSONB' },
      { name: 'isArchived', type: 'BOOLEAN NOT NULL DEFAULT false' },
      { name: 'archivedAt', type: 'TIMESTAMP(3)' },
      { name: 'searchVector', type: 'TEXT' }
    ];
    
    for (const col of additionalColumns) {
      try {
        await prisma.$executeRaw`
          ALTER TABLE "employee_requests" 
          ADD COLUMN IF NOT EXISTS "${col.name}" ${col.type};
        `;
        console.log(`   ✅ تم إضافة العمود ${col.name}`);
      } catch (error) {
        console.log(`   ⚠️ العمود ${col.name} موجود بالفعل أو هناك خطأ: ${error.message}`);
      }
    }
    
    // 4. اختبار العملية
    console.log('\n4️⃣ اختبار عمليات employee_requests...');
    
    try {
      // عد السجلات
      const count = await prisma.employeeRequest.count();
      console.log(`   ✅ عدد السجلات: ${count}`);
      
      // اختبار جلب البيانات
      const requests = await prisma.employeeRequest.findMany({
        take: 3,
        orderBy: { id: 'desc' }
      });
      console.log(`   ✅ تم جلب ${requests.length} طلب بنجاح`);
      
      if (requests.length > 0) {
        const firstRequest = requests[0];
        console.log(`   - أول طلب: ID=${firstRequest.id}, Type=${firstRequest.requestType}`);
      }
      
    } catch (error) {
      console.log('   ❌ خطأ في اختبار العمليات:', error.message);
    }
    
    // 5. اختبار API endpoint
    console.log('\n5️⃣ اختبار API endpoint...');
    
    const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    
    try {
      const response = await fetch('http://localhost:9005/api/employee-requests?view=simple', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ API يعمل - تم جلب ${Array.isArray(data) ? data.length : 'object'} عنصر`);
      } else {
        const errorText = await response.text();
        console.log(`   ❌ API فشل - Status: ${response.status}, Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ في API: ${error.message}`);
    }
    
    // 6. إنشاء طلب تجريبي لاختبار النظام
    console.log('\n6️⃣ إنشاء طلب تجريبي للاختبار...');
    
    try {
      const testRequest = await prisma.employeeRequest.create({
        data: {
          requestNumber: `TEST-${Date.now()}`,
          requestType: 'طلب إجازة',
          priority: 'متوسط',
          notes: 'طلب تجريبي لاختبار النظام',
          status: 'قيد المراجعة',
          requestDate: new Date().toISOString(),
          employeeName: 'موظف تجريبي',
          employeeId: 1,
          attachments: JSON.stringify([]),
          tags: JSON.stringify(['test']),
          isArchived: false
        }
      });
      
      console.log(`   ✅ تم إنشاء طلب تجريبي: ID=${testRequest.id}`);
      
      // حذف الطلب التجريبي
      await prisma.employeeRequest.delete({
        where: { id: testRequest.id }
      });
      console.log('   ✅ تم حذف الطلب التجريبي');
      
    } catch (error) {
      console.log('   ❌ خطأ في إنشاء الطلب التجريبي:', error.message);
    }
    
    console.log('\n🎉 تم الانتهاء من فحص وإصلاح employee_requests!');
    console.log('\n💡 النصائح:');
    console.log('1. تأكد من إعادة تشغيل الخادم بعد التغييرات');
    console.log('2. قم بمراجعة ملف schema.prisma للتأكد من تطابقه');
    console.log('3. جرب صفحة الطلبات في المتصفح للتأكد من عملها');
    
  } catch (error) {
    console.error('❌ خطأ عام في أداة الإصلاح:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل أداة الإصلاح
fixEmployeeRequestsIssues();
