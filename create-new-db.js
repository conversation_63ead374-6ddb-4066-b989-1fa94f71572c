/**
 * Create New Database Script
 * Date: 2025-08-04
 * Description: Create a completely new database to avoid permission issues
 */

const { Client } = require('pg');

async function createNewDatabase() {
  console.log('🗄️ إنشاء قاعدة بيانات جديدة...\n');

  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'password',
    database: 'postgres' // Connect to default database first
  });

  try {
    await client.connect();
    console.log('✅ تم الاتصال بخادم PostgreSQL');

    // Drop existing database if exists
    try {
      await client.query('DROP DATABASE IF EXISTS deviceflow_db_new;');
      console.log('✅ تم حذف قاعدة البيانات القديمة (إن وجدت)');
    } catch (error) {
      console.log('⚠️ لم يتم العثور على قاعدة بيانات قديمة');
    }

    // Create new database
    await client.query('CREATE DATABASE deviceflow_db_new;');
    console.log('✅ تم إنشاء قاعدة البيانات الجديدة: deviceflow_db_new');

    console.log('\n🎉 تم إنشاء قاعدة البيانات بنجاح!');
    console.log('\n📝 الخطوات التالية:');
    console.log('1. قم بتحديث DATABASE_URL في ملف .env');
    console.log('2. غير deviceflow_db إلى deviceflow_db_new');
    console.log('3. شغل: npx prisma db push');

  } catch (error) {
    console.error('❌ خطأ في إنشاء قاعدة البيانات:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run creation
if (require.main === module) {
  createNewDatabase()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إنشاء قاعدة البيانات');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إنشاء قاعدة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { createNewDatabase };
