// اختبار النظام الجديد - SO بنفس أسلوب صفحة التوريد
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة btoa للـ Node.js
function btoa(str) {
  return Buffer.from(str, 'binary').toString('base64');
}

const createToken = (username, role) => {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData);
};

async function testSupplyStyleSystem() {
  try {
    console.log('🧪 اختبار النظام الجديد (أسلوب صفحة التوريد)...\n');
    
    const adminToken = createToken('admin', 'admin');
    
    console.log('📋 اختبار 1: فاتورة بدون رقم فاتورة رسمية');
    const saleWithoutOP = {
      clientName: 'عميل اختبار بأسلوب التوريد',
      opNumber: '', // فارغ
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار أسلوب التوريد',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [{
        deviceId: 'TEST_SUPPLY_STYLE',
        model: 'Xiaomi Redmi Note 12',
        price: 600,
        condition: 'جديد'
      }]
    };

    const response1 = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(saleWithoutOP)
    });

    const result1 = await response1.json();
    
    if (response1.ok) {
      console.log('✅ نجح الاختبار الأول!');
      console.log(`   SO Number: ${result1.soNumber}`);
      console.log(`   OP Number: "${result1.opNumber || 'فارغ'}"`);
      
      // التحقق من التنسيق
      if (result1.soNumber.startsWith('SO-')) {
        console.log('✅ رقم SO له التنسيق الصحيح (SO-xxxxxx)');
      } else {
        console.log('❌ رقم SO لا يتبع التنسيق المطلوب');
      }
    } else {
      console.log('❌ فشل الاختبار الأول:', result1);
      return;
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('\n📋 اختبار 2: فاتورة أخرى للتحقق من التسلسل');
    const secondSale = {
      clientName: 'عميل اختبار ثاني',
      opNumber: 'OFFICIAL-2025-001',
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار التسلسل',
      warrantyPeriod: '3months',
      date: new Date().toISOString(),
      items: [{
        deviceId: 'TEST_SUPPLY_STYLE_2',
        model: 'Oppo A78',
        price: 750,
        condition: 'جديد'
      }]
    };

    const response2 = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(secondSale)
    });

    const result2 = await response2.json();
    
    if (response2.ok) {
      console.log('✅ نجح الاختبار الثاني!');
      console.log(`   SO Number: ${result2.soNumber}`);
      console.log(`   OP Number: "${result2.opNumber}"`);
    } else {
      console.log('❌ فشل الاختبار الثاني:', result2);
      return;
    }

    // مقارنة مع أوامر التوريد للتأكد من التشابه
    console.log('\n🔍 مقارنة مع أوامر التوريد:');
    
    const supplyOrders = await prisma.supplyOrder.findMany({
      orderBy: { id: 'desc' },
      take: 2,
      select: {
        supplyOrderId: true,
        supplierId: true
      }
    });

    if (supplyOrders.length > 0) {
      console.log('📦 أمثلة على أوامر التوريد:');
      supplyOrders.forEach((order, index) => {
        console.log(`   ${index + 1}. ${order.supplyOrderId} (مورد: ${order.supplierId})`);
      });
    }

    // عرض أحدث فواتير المبيعات
    console.log('\n📊 أحدث فواتير المبيعات:');
    
    const sales = await prisma.sale.findMany({
      orderBy: { id: 'desc' },
      take: 3,
      select: {
        soNumber: true,
        opNumber: true,
        clientName: true,
        createdAt: true
      }
    });

    sales.forEach((sale, index) => {
      console.log(`   ${index + 1}. ${sale.soNumber} | OP: "${sale.opNumber || 'فارغ'}" | ${sale.clientName}`);
    });

    console.log('\n🎉 النظام الآن يعمل بنفس أسلوب صفحة التوريد:');
    console.log('   - رقم SO: بتنسيق SO-timestamp+random (مثل SUP-timestamp+random)');
    console.log('   - رقم OP: اختياري للمستخدم');
    console.log('   - يتحقق من آخر رقم محفوظ ويعطي رقم فريد');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testSupplyStyleSystem();
