/**
 * Fix All Remaining Issues Final Script
 * Date: 2025-08-04
 * Description: Fix all remaining actual issues to reach zero real problems
 */

const fs = require('fs');
const path = require('path');

// Files with remaining actual issues that need fixing
const remainingIssueFiles = [
  // Critical fixes needed
  'app/(main)/returns/page.tsx',
  'app/api/maintenance-receipts/route.ts',
  'app/api/database/backup/route.ts',
  
  // Form input fixes (keep toISOString().slice for HTML inputs)
  'app/(main)/grading/2page.tsx',
  'app/(main)/inventory/page_backup.tsx',
  'app/(main)/maintenance/page.tsx',
  'app/(main)/maintenance-transfer/page.tsx',
  'app/(main)/reports/employee-reports/page.tsx',
  'app/(main)/reports/grading-reports/page.tsx',
  'app/(main)/sales/page.tsx',
  'app/(main)/stocktaking/page.tsx',
  'app/(main)/supply/page.tsx'
];

// Final comprehensive fixes
const finalComprehensiveFixes = [
  // Fix actual problematic usage in returns page
  {
    search: /return `ضمان منتهي \(انتهى: \$\{formatDateTime\(expiryDate\.toISOString\(\)\)\}\)`;/g,
    replace: 'return `ضمان منتهي (انتهى: ${formatDateTime(expiryDate)})`;',
    description: 'إصلاح استخدام formatDateTime مع Date object مباشرة'
  },
  
  // Fix API route that doesn't need toISOString for internal processing
  {
    search: /date: newReceipt\.date \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'date: newReceipt.date || new Date()',
    description: 'استخدام Date object في API internal processing'
  },
  
  // Add comment for valid timestamp usage in backup
  {
    search: /const timestamp = new Date\(\)\.toISOString\(\)\.replace\(\/\[\:\.\]\/g, '-'\);/g,
    replace: 'const timestamp = new Date().toISOString().replace(/[:.]/g, \'-\'); // مطلوب لاسم الملف',
    description: 'إضافة تعليق توضيحي للـ timestamp (مطلوب لاسم الملف)'
  },
  
  // Add comments for valid form input usages (these are correct and needed)
  {
    search: /defaultValue=\{new Date\(\)\.toISOString\(\)\.slice\(0, 16\)\}/g,
    replace: 'defaultValue={new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input',
    description: 'إضافة تعليق توضيحي (مطلوب لـ HTML input)'
  },
  {
    search: /value=\{.*\.toISOString\(\)\.slice\(0, 16\)\}/g,
    replace: function(match) {
      return match + ' // مطلوب لـ HTML input';
    },
    description: 'إضافة تعليق توضيحي للـ form inputs'
  },
  {
    search: /setOrderDate\(new Date\(order\.date\)\.toISOString\(\)\.slice\(0, 16\)\)/g,
    replace: 'setOrderDate(new Date(order.date).toISOString().slice(0, 16)) // مطلوب لـ HTML input',
    description: 'إضافة تعليق توضيحي (مطلوب لـ HTML input)'
  },
  {
    search: /setDeliveryOrderDate\(new Date\(order\.date\)\.toISOString\(\)\.slice\(0, 16\)\)/g,
    replace: 'setDeliveryOrderDate(new Date(order.date).toISOString().slice(0, 16)) // مطلوب لـ HTML input',
    description: 'إضافة تعليق توضيحي (مطلوب لـ HTML input)'
  },
  
  // Add comments for valid filename usages
  {
    search: /doc\.save\(`.*\$\{new Date\(\)\.toISOString\(\)\.slice\(0, 10\)\}.*`\);/g,
    replace: function(match) {
      return match.replace(');', '); // مطلوب لاسم الملف');
    },
    description: 'إضافة تعليق توضيحي (مطلوب لاسم الملف)'
  },
  {
    search: /`.*_\$\{new Date\(\)\.toISOString\(\)\.slice\(0, 10\)\}\..*`/g,
    replace: function(match) {
      return match + ' // مطلوب لاسم الملف';
    },
    description: 'إضافة تعليق توضيحي (مطلوب لاسم الملف)'
  },
  
  // Add comments for valid data processing
  {
    search: /date: new Date\(order\.date\)\.toISOString\(\)\.slice\(0, 16\), \/\/ YYYY-MM-DDTHH:MM/g,
    replace: 'date: new Date(order.date).toISOString().slice(0, 16), // مطلوب لـ HTML input format',
    description: 'إضافة تعليق توضيحي (مطلوب لـ HTML input format)'
  },
  {
    search: /supplyDate: new Date\(\)\.toISOString\(\)\.slice\(0, 16\), \/\/ YYYY-MM-DDTHH:MM format/g,
    replace: 'supplyDate: new Date().toISOString().slice(0, 16), // مطلوب لـ HTML input format',
    description: 'إضافة تعليق توضيحي (مطلوب لـ HTML input format)'
  },
  {
    search: /defaultValue=\{new Date\(\)\.toISOString\(\)\.split\('T'\)\[0\]\}/g,
    replace: 'defaultValue={new Date().toISOString().split(\'T\')[0]} // مطلوب لـ HTML date input',
    description: 'إضافة تعليق توضيحي (مطلوب لـ HTML date input)'
  }
];

// Create comprehensive documentation for valid usages
const validUsageDocumentation = `
/*
 * ===== دليل الاستخدامات الصحيحة للتواريخ =====
 * 
 * الاستخدامات التالية صحيحة ولا تحتاج إصلاح:
 * 
 * 1. toISOString().slice() في form inputs:
 *    - مطلوب لـ HTML date/datetime inputs
 *    - مثال: defaultValue={new Date().toISOString().slice(0, 16)}
 * 
 * 2. toISOString() في أسماء الملفات:
 *    - مطلوب لتجنب الأحرف الخاصة في أسماء الملفات
 *    - مثال: doc.save(\`report_\${new Date().toISOString().slice(0, 10)}.pdf\`)
 * 
 * 3. دوال formatDate() ترجع string:
 *    - صحيح للعرض في UI
 *    - مثال: formatDate(date): string
 * 
 * 4. JSON.stringify مع Date objects:
 *    - مطلوب للتسلسل وحفظ البيانات
 *    - مثال: JSON.stringify(data, (key, value) => value instanceof Date ? value.toISOString() : value)
 * 
 * 5. console.log مع toISOString():
 *    - مفيد للتشخيص والتطوير
 *    - مثال: console.log(\`التاريخ: \${date.toISOString()}\`)
 * 
 * 6. formattedDate?: string:
 *    - للعرض المنسق في UI
 *    - مثال: { date: Date; formattedDate?: string }
 * 
 * 7. دوال غير متعلقة بالتواريخ:
 *    - validateIpAddress(ip: string): boolean
 *    - getOverdueTimeText(priority: string): string
 */
`;

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    
    if (typeof fix.replace === 'function') {
      content = content.replace(fix.search, fix.replace);
    } else {
      content = content.replace(fix.search, fix.replace);
    }
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixAllRemainingIssues() {
  console.log('🔧 إصلاح جميع المشاكل المتبقية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of remainingIssueFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${remainingIssueFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        if (applyFixes(filePath, finalComprehensiveFixes)) {
          totalFixed += finalComprehensiveFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
    }

    // Add comprehensive documentation to date-utils
    const dateUtilsPath = path.join(process.cwd(), 'lib/date-utils.ts');
    if (fs.existsSync(dateUtilsPath)) {
      let content = fs.readFileSync(dateUtilsPath, 'utf8');
      if (!content.includes('دليل الاستخدامات الصحيحة للتواريخ')) {
        content = validUsageDocumentation + content;
        fs.writeFileSync(dateUtilsPath, content);
        console.log('📚 تم إضافة دليل شامل للاستخدامات الصحيحة');
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح جميع المشاكل المتبقية:');
    console.log('='.repeat(50));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${remainingIssueFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create final report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: remainingIssueFiles.length,
      fixes: finalComprehensiveFixes,
      note: 'تم إصلاح المشاكل الحقيقية وإضافة تعليقات توضيحية للاستخدامات الصحيحة'
    };

    fs.writeFileSync('all-remaining-fixes-final-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ التقرير النهائي في: all-remaining-fixes-final-report.json');

    console.log('\n🎯 النتيجة النهائية:');
    console.log('تم إصلاح جميع المشاكل الحقيقية وإضافة تعليقات توضيحية للاستخدامات الصحيحة.');
    console.log('الاستخدامات المتبقية هي استخدامات صحيحة ومطلوبة للنظام.');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح جميع المشاكل المتبقية بنجاح!');
    } else {
      console.log('\n✅ جميع الاستخدامات المتبقية صحيحة ولا تحتاج إصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح المشاكل المتبقية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixAllRemainingIssues()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح جميع المشاكل المتبقية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح المشاكل المتبقية:', error);
      process.exit(1);
    });
}

module.exports = { fixAllRemainingIssues };
