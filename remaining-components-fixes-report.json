{"timestamp": "2025-08-05T00:19:29.641Z", "totalFixes": 494, "filesModified": ["app/(main)/grading/2page.tsx", "app/(main)/maintenance/page.tsx", "app/(main)/sales/page.tsx", "app/(main)/supply/page.tsx", "app/(main)/stocktaking/page.tsx", "app/(main)/stocktaking/page_new.tsx", "app/(main)/test-export/page.tsx", "app/(main)/settings/appearance-settings.tsx", "app/(main)/reports/client-reports/page.tsx", "app/(main)/reports/employee-reports/page.tsx", "app/(main)/reports/maintenance-reports/page.tsx", "components/AttachmentsViewer.tsx", "components/database-management.tsx", "components/database-management-backup.tsx", "components/database-management-new.tsx", "components/evaluation-cleanup.tsx", "components/requests/RequestConversation.tsx", "context/stocktake-store.tsx", "lib/template-service.ts"], "totalFilesProcessed": 36, "fixes": [{"search": {}, "replace": "new Date().toISOString().slice(0, 10)", "description": "الاحتفاظ بـ slice للتواريخ (مطلوب للـ inputs)"}, {"search": {}, "replace": "new Date().toISOString().slice(0, 16)", "description": "الاحتفاظ بـ slice للتاريخ والوقت (مطلوب للـ inputs)"}, {"search": {}, "replace": "date: new Date().toISOString().slice(0, 16)", "description": "الاحتفاظ بـ slice في الـ forms"}, {"search": {}, "replace": "supplyDate: new Date().toISOString().slice(0, 16)", "description": "الاحتفاظ بـ slice في supplyDate"}, {"search": {}, "replace": "timestamp: new Date()", "description": "استخدام Date object للـ timestamp"}, {"search": {}, "replace": "createdAt: new Date()", "description": "استخدام Date object للـ createdAt"}, {"search": {}, "replace": "uploadedAt: new Date()", "description": "استخدام Date object للـ uploadedAt"}, {"search": {}, "replace": "lastSavedAt: new Date()", "description": "استخدام Date object للـ lastSavedAt"}, {"search": {}, "replace": "completedAt: new Date()", "description": "استخدام Date object للـ completedAt"}, {"search": {}, "replace": "startTime: new Date()", "description": "استخدام Date object للـ startTime"}, {"search": {}, "replace": "", "description": "إزالة toLocaleDateString العربية"}, {"search": {}, "replace": "", "description": "إزالة toLocaleDateString السعودية"}, {"search": {}, "replace": "", "description": "إزالة toLocaleDateString الإنجليزية"}, {"search": {}, "replace": "", "description": "إزالة toLocaleString العربية"}, {"search": {}, "replace": "", "description": "إزالة toLocaleString السعودية"}, {"search": {}, "replace": "", "description": "إزالة toLocaleString الإنجليزية"}, {"search": {}, "replace": "toDate = new Date(toDate.getTime() + 24 * 60 * 60 * 1000);", "description": "تحسين عمليات التاريخ"}, {"search": {}, "replace": "const formatDateTime = (dateTime: Date | string): string", "description": "تحسين نوع معامل formatDateTime"}, {"search": {}, "replace": "function formatArabicDate(date: Date): string", "description": "تحسين دالة formatArabicDate"}, {"search": {}, "replace": "const updateRequestStatus = async (requestId: number, status: string, notes?: string)", "description": "تحسين دالة updateRequestStatus"}, {"search": {}, "replace": "lastMessageDate: Date;", "description": "تحويل lastMessageDate إلى Date"}, {"search": {}, "replace": "maintenanceStartDate: Date;", "description": "تحويل maintenanceStartDate إلى Date"}, {"search": {}, "replace": "dateFrom: Date;", "description": "تحويل dateFrom إلى Date"}, {"search": {}, "replace": "dateTo: Date;", "description": "تحويل dateTo إلى Date"}, {"search": {}, "replace": "createdAt: Date;", "description": "تحويل created<PERSON><PERSON> Date"}, {"search": {}, "replace": "updatedAt: Date;", "description": "تحويل updated<PERSON>t إلى Date"}]}