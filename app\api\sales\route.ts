import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateSequentialSONumber } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع المبيعات مع include مختلف حسب النوع
    const includeOptions = view === 'simple' 
      ? { items: false } // لا نحتاج items في العرض البسيط
      : { items: true };

    const sales = await prisma.sale.findMany({
      include: includeOptions,
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(sales);
  } catch (error) {
    console.error('Failed to fetch sales:', error);
    return NextResponse.json({ error: 'Failed to fetch sales' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newSale = await request.json();

    // Basic validation
    if (!newSale.clientName || !newSale.items || !Array.isArray(newSale.items) || newSale.items.length === 0) {
      return NextResponse.json(
        { error: 'Client name and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // ✅ استخدام soNumber المرسل من الواجهة أو إنشاء واحد جديد
      let soNumber = newSale.soNumber;
      if (!soNumber) {
        soNumber = await generateSequentialSONumber(tx);
      }

      // ✅ رقم فاتورة رسمية اختياري (OP Number)
      let opNumber: string;
      if (newSale.opNumber && newSale.opNumber.trim() !== '') {
        opNumber = newSale.opNumber.trim();
      } else {
        opNumber = ''; // اختياري - يمكن تركه فارغ
      }

      // Create the sale in the database
      const sale = await tx.sale.create({
        data: {
          soNumber,
          opNumber, // ✅ استخدام القيمة المولدة
          date: new Date(newSale.date), // ✅ تحويل إلى DateTime باللغة الإنجليزية
          clientName: newSale.clientName,
          warehouseName: newSale.warehouseName || 'المخزن الرئيسي',
          notes: newSale.notes || '',
          warrantyPeriod: newSale.warrantyPeriod || 'none',
          employeeName: newSale.employeeName || authResult.user!.username
        }
      });

      // Create sale items
      if (newSale.items && Array.isArray(newSale.items)) {
        for (const item of newSale.items) {
          await tx.saleItem.create({
            data: {
              saleId: sale.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              price: parseFloat(item.price) || 0,
              condition: item.condition || 'جديد'
            }
          });
        }
      }

      // Update device statuses
      if (newSale.items && Array.isArray(newSale.items)) {
        for (const item of newSale.items) {
          if (item.deviceId) {
            // التحقق من وجود الجهاز أولاً
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'مباع' }
              });
            } else {
              console.warn(`Device ${item.deviceId} not found for sale ${sale.soNumber}`);
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created sale: ${sale.soNumber} for client ${sale.clientName}`
      });

      // Return sale with items
      const saleWithItems = await tx.sale.findUnique({
        where: { id: sale.id },
        include: { items: true }
      });

      return saleWithItems;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedSale = await request.json();

    if (!updatedSale.id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if sale exists
      const existingSale = await tx.sale.findUnique({
        where: { id: updatedSale.id },
        include: { items: true }
      });

      if (!existingSale) {
        throw new Error('Sale not found');
      }

      // Get the old items to check for removed devices
      const oldItems = existingSale.items || [];
      const newItems = updatedSale.items || [];
      const newItemIds = newItems.map((item: any) => item.deviceId).filter(Boolean);
      const oldItemIds = oldItems.map((item: any) => item.deviceId).filter(Boolean);

      // Find devices that were removed from the sale
      const removedDeviceIds = oldItemIds.filter((deviceId: string) => !newItemIds.includes(deviceId));

      // Reset status for removed devices
      for (const deviceId of removedDeviceIds) {
        const device = await tx.device.findUnique({
          where: { id: deviceId }
        });

        if (device) {
          await tx.device.update({
            where: { id: deviceId },
            data: { status: 'متاح للبيع' }
          });
        }
      }

      // Set status for new devices
      for (const item of newItems) {
        if (item.deviceId) {
          const device = await tx.device.findUnique({
            where: { id: item.deviceId }
          });

          if (device) {
            await tx.device.update({
              where: { id: item.deviceId },
              data: { status: 'مباع' }
            });
          }
        }
      }

      // تحديد رقم الفاتورة الرسمية (OP Number) - اختياري
      let opNumber: string;
      if (updatedSale.opNumber !== undefined) {
        // إذا تم تمرير opNumber (حتى لو كان فارغ)، استخدمه
        opNumber = updatedSale.opNumber.trim();
      } else {
        // إذا لم يتم تمرير opNumber، احتفظ بالقيمة الموجودة
        opNumber = existingSale.opNumber || '';
      }

      // Update the sale
      const sale = await tx.sale.update({
        where: { id: updatedSale.id },
        data: {
          opNumber: opNumber,
          date: updatedSale.date ? new Date(updatedSale.date) : existingSale.date, // ✅ تحويل إلى DateTime باللغة الإنجليزية
          clientName: updatedSale.clientName || existingSale.clientName,
          warehouseName: updatedSale.warehouseName || existingSale.warehouseName,
          notes: updatedSale.notes !== undefined ? updatedSale.notes : existingSale.notes,
          warrantyPeriod: updatedSale.warrantyPeriod !== undefined ? updatedSale.warrantyPeriod : existingSale.warrantyPeriod,
          employeeName: updatedSale.employeeName || existingSale.employeeName
        }
      });

      // Delete existing items
      await tx.saleItem.deleteMany({
        where: { saleId: updatedSale.id }
      });

      // Create new items
      if (updatedSale.items && Array.isArray(updatedSale.items)) {
        for (const item of updatedSale.items) {
          await tx.saleItem.create({
            data: {
              saleId: sale.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              price: parseFloat(item.price) || 0,
              condition: item.condition || 'جديد'
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated sale: ${sale.soNumber} for client ${sale.clientName}`
      });

      // Return sale with items
      const saleWithItems = await tx.sale.findUnique({
        where: { id: sale.id },
        include: { items: true }
      });

      return saleWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update sale:', error);

    if (error instanceof Error && error.message === 'Sale not found') {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if sale exists
      const existingSale = await tx.sale.findUnique({
        where: { id }
      });

      if (!existingSale) {
        throw new Error('Sale not found');
      }

      // Reset device statuses from sale items
      const saleItems = await tx.saleItem.findMany({
        where: { saleId: id }
      });

      for (const item of saleItems) {
        if (item.deviceId) {
          const device = await tx.device.findUnique({
            where: { id: item.deviceId }
          });

          if (device) {
            await tx.device.update({
              where: { id: item.deviceId },
              data: { status: 'متاح للبيع' }
            });
          }
        }
      }

      // Delete sale items first (will be cascade deleted anyway, but we do it explicitly for clarity)
      await tx.saleItem.deleteMany({
        where: { saleId: id }
      });

      // Delete the sale
      await tx.sale.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted sale: ${existingSale.soNumber} for client ${existingSale.clientName}`
      });

      return { message: 'Sale deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete sale:', error);

    if (error instanceof Error && error.message === 'Sale not found') {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
