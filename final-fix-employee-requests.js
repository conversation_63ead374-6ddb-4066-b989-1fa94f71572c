/**
 * إصلاح نهائي لمشاكل employee_requests - Final Employee Requests Fix
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalFixEmployeeRequests() {
  console.log('🔧 الإصلاح النهائي لمشاكل employee_requests...\n');
  
  try {
    // 1. إضافة الأعمدة المفقودة باستخدام executeRawUnsafe
    console.log('1️⃣ إضافة الأعمدة المفقودة...');
    
    const columnsToAdd = [
      { name: 'attachments', type: 'JSONB' },
      { name: 'tags', type: 'JSONB' },
      { name: 'isArchived', type: 'BOOLEAN DEFAULT false' },
      { name: 'archivedAt', type: 'TIMESTAMP(3)' },
      { name: 'searchVector', type: 'TEXT' }
    ];
    
    for (const col of columnsToAdd) {
      try {
        await prisma.$executeRawUnsafe(
          `ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS "${col.name}" ${col.type};`
        );
        console.log(`   ✅ تم إضافة العمود ${col.name}`);
      } catch (error) {
        console.log(`   ⚠️ العمود ${col.name}: ${error.message}`);
      }
    }
    
    // 2. فحص الأعمدة الحالية مرة أخرى
    console.log('\n2️⃣ فحص الأعمدة بعد الإضافة...');
    
    try {
      const result = await prisma.$queryRaw`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'employee_requests' 
        AND column_name IN ('attachments', 'tags', 'isArchived', 'archivedAt', 'searchVector')
        ORDER BY column_name;
      `;
      
      console.log('   الأعمدة الجديدة:');
      result.forEach(col => {
        console.log(`   - ${col.column_name} (${col.data_type})`);
      });
      
    } catch (error) {
      console.log('   ❌ فشل في فحص الأعمدة:', error.message);
    }
    
    // 3. تحديث السجلات الموجودة لإضافة قيم افتراضية
    console.log('\n3️⃣ تحديث السجلات الموجودة...');
    
    try {
      await prisma.$executeRawUnsafe(`
        UPDATE "employee_requests" 
        SET 
          "attachments" = '[]'::jsonb,
          "tags" = '[]'::jsonb,
          "isArchived" = false
        WHERE 
          "attachments" IS NULL 
          OR "tags" IS NULL 
          OR "isArchived" IS NULL;
      `);
      console.log('   ✅ تم تحديث السجلات الموجودة');
    } catch (error) {
      console.log('   ❌ فشل في تحديث السجلات:', error.message);
    }
    
    // 4. اختبار العمليات الآن
    console.log('\n4️⃣ اختبار العمليات بعد الإصلاح...');
    
    try {
      // عد السجلات
      const count = await prisma.employeeRequest.count();
      console.log(`   ✅ عدد السجلات: ${count}`);
      
      // اختبار جلب البيانات
      const requests = await prisma.employeeRequest.findMany({
        take: 2,
        orderBy: { id: 'desc' }
      });
      console.log(`   ✅ تم جلب ${requests.length} طلب بنجاح`);
      
      if (requests.length > 0) {
        const firstRequest = requests[0];
        console.log(`   - أول طلب: ID=${firstRequest.id}, Type=${firstRequest.requestType}`);
        console.log(`   - attachments: ${firstRequest.attachments ? 'موجود' : 'غير موجود'}`);
        console.log(`   - tags: ${firstRequest.tags ? 'موجود' : 'غير موجود'}`);
        console.log(`   - isArchived: ${firstRequest.isArchived}`);
      }
      
    } catch (error) {
      console.log('   ❌ خطأ في اختبار العمليات:', error.message);
    }
    
    // 5. اختبار API endpoint مرة أخرى
    console.log('\n5️⃣ اختبار API endpoint مرة أخرى...');
    
    const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    
    try {
      const response = await fetch('http://localhost:9005/api/employee-requests?view=simple', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ API يعمل الآن - تم جلب ${Array.isArray(data) ? data.length : 'object'} عنصر`);
      } else {
        const errorText = await response.text();
        console.log(`   ❌ API لا يزال يفشل - Status: ${response.status}, Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ في API: ${error.message}`);
    }
    
    // 6. إنشاء طلب تجريبي جديد
    console.log('\n6️⃣ إنشاء طلب تجريبي جديد...');
    
    try {
      const testRequest = await prisma.employeeRequest.create({
        data: {
          requestNumber: `FIXED-${Date.now()}`,
          requestType: 'طلب تجريبي',
          priority: 'منخفض',
          notes: 'طلب تجريبي بعد الإصلاح',
          status: 'قيد المراجعة',
          requestDate: new Date().toISOString(),
          employeeName: 'موظف تجريبي',
          employeeId: 1,
          attachments: [],
          tags: ['test', 'fixed'],
          isArchived: false
        }
      });
      
      console.log(`   ✅ تم إنشاء طلب تجريبي: ID=${testRequest.id}`);
      console.log(`   - المرفقات: ${JSON.stringify(testRequest.attachments)}`);
      console.log(`   - التاجات: ${JSON.stringify(testRequest.tags)}`);
      
      // حذف الطلب التجريبي
      await prisma.employeeRequest.delete({
        where: { id: testRequest.id }
      });
      console.log('   ✅ تم حذف الطلب التجريبي');
      
    } catch (error) {
      console.log('   ❌ خطأ في إنشاء الطلب التجريبي:', error.message);
    }
    
    console.log('\n🎉 تم الإصلاح النهائي بنجاح!');
    console.log('\n📋 ملخص الإصلاحات:');
    console.log('✅ إضافة العمود attachments (JSONB)');
    console.log('✅ إضافة العمود tags (JSONB)');
    console.log('✅ إضافة العمود isArchived (BOOLEAN)');
    console.log('✅ إضافة العمود archivedAt (TIMESTAMP)');
    console.log('✅ إضافة العمود searchVector (TEXT)');
    console.log('✅ تحديث السجلات الموجودة');
    
    console.log('\n🔄 الخطوات التالية:');
    console.log('1. أعد تشغيل خادم Next.js');
    console.log('2. جرب زيارة صفحة /requests');
    console.log('3. تحقق من أن أخطاء 401 قد اختفت');
    
  } catch (error) {
    console.error('❌ خطأ عام في الإصلاح النهائي:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الإصلاح النهائي
finalFixEmployeeRequests();
