/**
 * Fix Date Formatting Issues Script
 * Date: 2025-08-04
 * Description: Fix LOCAL_DATE_FORMAT and MANUAL_DATE_FORMAT issues
 */

const fs = require('fs');
const path = require('path');

// Files with date formatting issues
const dateFormattingFiles = [
  'app/(main)/accept-devices/page.tsx',
  'app/(main)/audit-logs/page.tsx',
  'app/(main)/grading/2page.tsx',
  'app/(main)/maintenance/page.tsx',
  'app/(main)/maintenance-transfer/page.tsx',
  'app/(main)/reports/supplier-reports/page.tsx',
  'app/(main)/supply/page.tsx',
  'app/(main)/track/DeviceDetailsSection.tsx',
  'app/(main)/track/DeviceHistoryTimeline.tsx',
  'components/database-management.tsx',
  'components/database-management-backup.tsx',
  'components/database-management-new.tsx',
  'components/ui/chart.tsx'
];

// Fixes for date formatting issues
const dateFormattingFixes = [
  // Fix toLocaleDateString usage
  {
    search: /\.toLocaleDateString\(\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString() واستبدالها بـ formatDate'
  },
  {
    search: /\.toLocaleDateString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString العربية'
  },
  {
    search: /\.toLocaleDateString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString السعودية'
  },
  {
    search: /\.toLocaleDateString\('en-US'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString الإنجليزية'
  },
  {
    search: /\.toLocaleDateString\('ar'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString العربية المختصرة'
  },
  
  // Fix toLocaleString usage
  {
    search: /\.toLocaleString\(\)/g,
    replace: '',
    description: 'إزالة toLocaleString() واستبدالها بـ formatDateTime'
  },
  {
    search: /\.toLocaleString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleString العربية'
  },
  {
    search: /\.toLocaleString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleString السعودية'
  },
  {
    search: /\.toLocaleString\('en-US'\)/g,
    replace: '',
    description: 'إزالة toLocaleString الإنجليزية'
  },
  
  // Fix specific patterns in components
  {
    search: /doc\.text\(`التاريخ: \$\{new Date\(order\.date\)\}`, 10, 47\);/g,
    replace: 'doc.text(`التاريخ: ${formatDate(new Date(order.date))}`, 10, 47);',
    description: 'استخدام formatDate في PDF'
  },
  {
    search: /\{new Date\(order\.date\)\}/g,
    replace: '{formatDate(new Date(order.date))}',
    description: 'استخدام formatDate في JSX'
  },
  {
    search: /\{new Date\(viewingOrder\.date\)\}/g,
    replace: '{formatDate(new Date(viewingOrder.date))}',
    description: 'استخدام formatDate في viewing order'
  },
  {
    search: /<TableCell>\{new Date\(log\.timestamp\)\}<\/TableCell>/g,
    replace: '<TableCell>{formatDateTime(new Date(log.timestamp))}</TableCell>',
    description: 'استخدام formatDateTime في جدول logs'
  },
  {
    search: /\{new Date\(backup\.createdAt\)\.toLocaleDateString\('ar-SA', \{[^}]+\}\)\}/g,
    replace: '{formatDate(new Date(backup.createdAt))}',
    description: 'استخدام formatDate في backup dates'
  },
  {
    search: /\{item\.value\}/g,
    replace: '{formatNumber(item.value)}',
    description: 'استخدام formatNumber للقيم'
  },
  
  // Fix manual date formatting
  {
    search: /const year = date\.getFullYear\(\);/g,
    replace: '// استخدم formatDate بدلاً من التنسيق اليدوي',
    description: 'إزالة التنسيق اليدوي للسنة'
  },
  {
    search: /const month = String\(date\.getMonth\(\) \+ 1\)\.padStart\(2, '0'\);/g,
    replace: '// استخدم formatDate بدلاً من التنسيق اليدوي',
    description: 'إزالة التنسيق اليدوي للشهر'
  },
  {
    search: /const day = String\(date\.getDate\(\)\)\.padStart\(2, '0'\);/g,
    replace: '// استخدم formatDate بدلاً من التنسيق اليدوي',
    description: 'إزالة التنسيق اليدوي لليوم'
  },
  {
    search: /return `\$\{year\}-\$\{month\}-\$\{day\}`;/g,
    replace: 'return formatDate(date);',
    description: 'استخدام formatDate بدلاً من التنسيق اليدوي'
  },
  
  // Fix specific locale patterns
  {
    search: /التاريخ: \$\{new Date\(\)\.toLocaleDateString\('ar'\)\}/g,
    replace: 'التاريخ: ${formatDate(new Date())}',
    description: 'استخدام formatDate في النصوص'
  },
  {
    search: /\{order\.supplyOrderId\} - \{new Date\(order\.supplyDate\)\}/g,
    replace: '{order.supplyOrderId} - {formatDate(new Date(order.supplyDate))}',
    description: 'استخدام formatDate في supply orders'
  },
  
  // Fix DeviceDetailsSection and DeviceHistoryTimeline
  {
    search: /return dateObj\.toLocaleDateString\('ar-EG', \{[^}]+\}\);/g,
    replace: 'return formatDate(dateObj);',
    description: 'استخدام formatDate في device components'
  },
  
  // Fix chart component
  {
    search: /\{item\.value\.toLocaleString\(\)\}/g,
    replace: '{formatNumber(item.value)}',
    description: 'استخدام formatNumber في المخططات'
  }
];

// Add formatNumber function where needed
const formatNumberFunction = `
// دالة تنسيق الأرقام
function formatNumber(value) {
  return new Intl.NumberFormat('ar-EG').format(value);
}
`;

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function addFormatNumberFunction(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if formatNumber function already exists
  if (content.includes('function formatNumber')) {
    return false;
  }

  // Add formatNumber function at the beginning of the file (after imports)
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    content = content.slice(0, insertIndex) + formatNumberFunction + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`🔢 تم إضافة دالة formatNumber في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixDateFormattingIssues() {
  console.log('🔧 إصلاح مشاكل تنسيق التواريخ...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of dateFormattingFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${dateFormattingFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Add formatNumber function if needed for chart components
        if (file.includes('chart.tsx') || file.includes('maintenance-transfer')) {
          addFormatNumberFunction(filePath);
        }
        
        // Apply fixes
        if (applyFixes(filePath, dateFormattingFixes)) {
          totalFixed += dateFormattingFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 5 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${dateFormattingFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح مشاكل تنسيق التواريخ:');
    console.log('='.repeat(50));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${dateFormattingFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: dateFormattingFiles.length,
      fixes: dateFormattingFixes
    };

    fs.writeFileSync('date-formatting-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح تنسيق التواريخ في: date-formatting-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح مشاكل تنسيق التواريخ بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل تنسيق للإصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح مشاكل تنسيق التواريخ:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixDateFormattingIssues()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح مشاكل تنسيق التواريخ');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح مشاكل تنسيق التواريخ:', error);
      process.exit(1);
    });
}

module.exports = { fixDateFormattingIssues };
