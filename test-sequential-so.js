// اختبار النظام الجديد - SO يتم توليده عند الإنشاء بالتسلسل
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة btoa للـ Node.js
function btoa(str) {
  return Buffer.from(str, 'binary').toString('base64');
}

const createToken = (username, role) => {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData);
};

async function testSequentialSystem() {
  try {
    console.log('🎯 اختبار النظام الجديد - SO تسلسلي يتم توليده عند الإنشاء...\n');
    
    const adminToken = createToken('admin', 'admin');
    
    // اختبار 1: طلب رقم SO جديد
    console.log('📋 اختبار 1: طلب رقم SO التالي...');
    const soResponse = await fetch('http://localhost:9005/api/sales/next-so', {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    if (soResponse.ok) {
      const soData = await soResponse.json();
      console.log(`✅ تم الحصول على رقم SO: ${soData.soNumber}`);
      
      // التحقق من التنسيق
      const isSequential = soData.soNumber.match(/^SO-\d+$/);
      if (isSequential) {
        console.log('✅ التنسيق صحيح (SO-رقم تسلسلي)');
      } else {
        console.log('❌ التنسيق غير صحيح');
        return;
      }
      
      // اختبار 2: إنشاء فاتورة بالرقم المولد
      console.log('\n📋 اختبار 2: إنشاء فاتورة بالرقم المولد...');
      const testSale = {
        soNumber: soData.soNumber, // استخدام الرقم المولد
        clientName: 'عميل اختبار التسلسل',
        opNumber: '', 
        warehouseName: 'مخزن اختبار',
        notes: 'اختبار النظام التسلسلي',
        warrantyPeriod: 'none',
        date: new Date().toISOString(),
        items: [{
          deviceId: 'SEQ_TEST_DEVICE',
          model: 'Sequential Test Model',
          price: 1000,
          condition: 'جديد'
        }]
      };

      const saleResponse = await fetch('http://localhost:9005/api/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`
        },
        body: JSON.stringify(testSale)
      });

      const saleResult = await saleResponse.json();
      
      if (saleResponse.ok) {
        console.log('✅ تم إنشاء الفاتورة بنجاح!');
        console.log(`🔢 SO Number المرسل: ${testSale.soNumber}`);
        console.log(`🔢 SO Number المحفوظ: ${saleResult.soNumber}`);
        
        // التحقق من التطابق
        if (testSale.soNumber === saleResult.soNumber) {
          console.log('✅ SO Number متطابق بين المرسل والمحفوظ');
        } else {
          console.log('❌ SO Number غير متطابق!');
        }
        
        // اختبار 3: طلب رقم SO التالي مرة أخرى
        console.log('\n📋 اختبار 3: طلب رقم SO التالي مرة أخرى...');
        const nextSoResponse = await fetch('http://localhost:9005/api/sales/next-so', {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });
        
        if (nextSoResponse.ok) {
          const nextSoData = await nextSoResponse.json();
          console.log(`✅ الرقم التالي: ${nextSoData.soNumber}`);
          
          // استخراج الأرقام للمقارنة
          const currentNum = parseInt(soData.soNumber.replace('SO-', ''));
          const nextNum = parseInt(nextSoData.soNumber.replace('SO-', ''));
          
          if (nextNum === currentNum + 1) {
            console.log('✅ التسلسل صحيح!');
          } else {
            console.log(`⚠️  التسلسل غير صحيح. المتوقع: ${currentNum + 1}, الفعلي: ${nextNum}`);
          }
        } else {
          console.log('❌ فشل في طلب الرقم التالي');
        }
        
        // عرض النتائج النهائية
        console.log('\n📊 النتائج النهائية:');
        const latestSales = await prisma.sale.findMany({
          orderBy: { id: 'desc' },
          take: 3,
          select: {
            soNumber: true,
            opNumber: true,
            clientName: true,
            createdAt: true
          }
        });

        console.log('═══════════════════════════════════════════════════════════');
        console.log('رقم SO               | رقم OP      | العميل');
        console.log('───────────────────────────────────────────────────────────');
        latestSales.forEach(sale => {
          const soNumber = sale.soNumber.padEnd(18);
          const opNumber = (sale.opNumber || 'فارغ').padEnd(10);
          console.log(`${soNumber} | ${opNumber} | ${sale.clientName}`);
        });
        console.log('═══════════════════════════════════════════════════════════\n');
        
        console.log('🎉 النظام الجديد يعمل بالشكل المطلوب:');
        console.log('   ✅ SO Number يتم توليده عند الإنشاء (ليس بعد الحفظ)');
        console.log('   ✅ SO Number بالتسلسل البسيط (SO-1, SO-2, SO-3...)');
        console.log('   ✅ OP Number اختياري');
        console.log('   ✅ التطابق بين الواجهة والخادم');

      } else {
        console.log('❌ فشل في إنشاء الفاتورة:', saleResult);
      }
      
    } else {
      console.log('❌ فشل في طلب رقم SO:', await soResponse.text());
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testSequentialSystem();
