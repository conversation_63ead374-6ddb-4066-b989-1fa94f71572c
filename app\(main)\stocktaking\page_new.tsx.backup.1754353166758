'use client';

import { useState, useMemo, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { Device, SystemSettings, Stocktake, StocktakeItem, StocktakeDiscrepancy, StocktakeStatus, StocktakeFilter } from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ClipboardList,
  Upload,
  Trash2,
  FileDown,
  Printer,
  FileSpreadsheet,
  Check,
  X,
  AlertTriangle,
  PackageSearch,
  Play,
  Pause,
  Save,
  Eye,
  Camera,
  History,
  Filter,
  Search,
  RotateCcw,
  CheckCircle,
  XCircle,
  Clock,
  Archive,
  Plus,
  Edit,
  MessageSquare,
} from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export default function StocktakingPage() {
  const { 
    devices, 
    warehouses, 
    stocktakes,
    addStocktake,
    updateStocktake,
    deleteStocktake,
    getFilteredStocktakes,
    addStocktakeItem,
    updateStocktakeItem,
    addStocktakeDiscrepancy,
    resolveStocktakeDiscrepancy,
    changeStocktakeStatus,
    reviewStocktake,
    getWarehouseById,
    getDeviceById,
    getModelById
  } = useStore();
  const { toast } = useToast();

  // State للفلاتر والواجهة
  const [selectedTab, setSelectedTab] = useState<'list' | 'active' | 'analysis'>('list');
  const [filter, setFilter] = useState<StocktakeFilter>({
    status: undefined,
    warehouseId: undefined,
    assigneeId: undefined,
    dateRange: undefined,
  });
  const [searchQuery, setSearchQuery] = useState('');
  
  // State لإنشاء وتعديل عمليات الجرد
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showDiscrepancyModal, setShowDiscrepancyModal] = useState(false);
  const [selectedStocktake, setSelectedStocktake] = useState<Stocktake | null>(null);
  const [formData, setFormData] = useState({
    warehouseId: '',
    type: 'full' as 'full' | 'partial' | 'cycle',
    description: '',
    assigneeId: '',
    scheduledDate: '',
    notes: '',
  });

  // State للجرد النشط
  const [activeStocktake, setActiveStocktake] = useState<Stocktake | null>(null);
  const [scanMode, setScanMode] = useState(false);
  const [scannedCode, setScannedCode] = useState('');
  const [physicalQuantity, setPhysicalQuantity] = useState('');

  // الحصول على عمليات الجرد المفلترة
  const filteredStocktakes = useMemo(() => {
    return getFilteredStocktakes(filter).filter(stocktake => {
      if (!searchQuery) return true;
      return (
        stocktake.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        stocktake.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        getWarehouseById(stocktake.warehouseId)?.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    });
  }, [stocktakes, filter, searchQuery, getFilteredStocktakes, getWarehouseById]);

  // إنشاء عملية جرد جديدة
  const handleCreateStocktake = () => {
    if (!formData.warehouseId || !formData.type || !formData.assigneeId) {
      toast({
        title: 'خطأ',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive',
      });
      return;
    }

    const newStocktake: Omit<Stocktake, 'id' | 'createdAt' | 'updatedAt'> = {
      warehouseId: formData.warehouseId,
      type: formData.type,
      status: 'planned',
      description: formData.description,
      assigneeId: formData.assigneeId,
      scheduledDate: formData.scheduledDate ? new Date(formData.scheduledDate) : new Date(),
      startDate: null,
      endDate: null,
      items: [],
      discrepancies: [],
      notes: formData.notes,
      attachments: [],
      systemSnapshot: null,
      progress: 0,
      reviewedBy: null,
      reviewedAt: null,
      reviewNotes: null,
    };

    addStocktake(newStocktake);
    setShowCreateModal(false);
    setFormData({
      warehouseId: '',
      type: 'full',
      description: '',
      assigneeId: '',
      scheduledDate: '',
      notes: '',
    });

    toast({
      title: 'تم بنجاح',
      description: 'تم إنشاء عملية الجرد بنجاح',
    });
  };

  // بدء عملية جرد
  const handleStartStocktake = (stocktakeId: string) => {
    const stocktake = stocktakes.find(s => s.id === stocktakeId);
    if (!stocktake) return;

    changeStocktakeStatus(stocktakeId, 'in-progress');
    setActiveStocktake({ ...stocktake, status: 'in-progress', startDate: new Date() });
    setSelectedTab('active');

    toast({
      title: 'تم بدء الجرد',
      description: 'يمكنك الآن البدء في جرد الأجهزة',
    });
  };

  // إيقاف عملية جرد مؤقتاً
  const handlePauseStocktake = (stocktakeId: string) => {
    changeStocktakeStatus(stocktakeId, 'paused');
    setActiveStocktake(null);

    toast({
      title: 'تم إيقاف الجرد مؤقتاً',
      description: 'يمكنك استكمال الجرد لاحقاً',
    });
  };

  // إكمال عملية جرد
  const handleCompleteStocktake = (stocktakeId: string) => {
    changeStocktakeStatus(stocktakeId, 'completed');
    setActiveStocktake(null);

    toast({
      title: 'تم إكمال الجرد',
      description: 'تم حفظ نتائج الجرد بنجاح',
    });
  };

  // إضافة عنصر جرد
  const handleAddStocktakeItem = () => {
    if (!activeStocktake || !scannedCode || !physicalQuantity) return;

    const device = devices.find(d => d.serialNumber === scannedCode || d.barcode === scannedCode);
    if (!device) {
      toast({
        title: 'الجهاز غير موجود',
        description: 'لم يتم العثور على الجهاز في النظام',
        variant: 'destructive',
      });
      return;
    }

    const systemQuantity = 1; // للأجهزة عادة ما تكون كمية واحدة
    const physicalQty = parseInt(physicalQuantity);
    const variance = physicalQty - systemQuantity;

    const newItem: Omit<StocktakeItem, 'id'> = {
      stocktakeId: activeStocktake.id,
      deviceId: device.id,
      systemQuantity,
      physicalQuantity: physicalQty,
      variance,
      notes: '',
      checkedAt: new Date(),
      checkedBy: activeStocktake.assigneeId,
    };

    addStocktakeItem(activeStocktake.id, newItem);

    // إضافة تباين إذا وجد
    if (variance !== 0) {
      const discrepancy: Omit<StocktakeDiscrepancy, 'id'> = {
        stocktakeId: activeStocktake.id,
        deviceId: device.id,
        type: variance > 0 ? 'surplus' : 'shortage',
        systemQuantity,
        physicalQuantity: physicalQty,
        variance: Math.abs(variance),
        reason: '',
        resolution: '',
        status: 'unresolved',
        reportedBy: activeStocktake.assigneeId,
        reportedAt: new Date(),
        resolvedBy: null,
        resolvedAt: null,
      };

      addStocktakeDiscrepancy(activeStocktake.id, discrepancy);
    }

    // إعادة تعيين الحقول
    setScannedCode('');
    setPhysicalQuantity('');

    toast({
      title: 'تم إضافة العنصر',
      description: `تم جرد الجهاز ${device.serialNumber}`,
    });
  };

  // حساب إحصائيات عمليات الجرد
  const stocktakeStats = useMemo(() => {
    const total = stocktakes.length;
    const planned = stocktakes.filter(s => s.status === 'planned').length;
    const inProgress = stocktakes.filter(s => s.status === 'in-progress').length;
    const completed = stocktakes.filter(s => s.status === 'completed').length;
    const totalDiscrepancies = stocktakes.reduce((sum, s) => sum + s.discrepancies.length, 0);
    const unresolvedDiscrepancies = stocktakes.reduce(
      (sum, s) => sum + s.discrepancies.filter(d => d.status === 'unresolved').length, 
      0
    );

    return {
      total,
      planned,
      inProgress,
      completed,
      totalDiscrepancies,
      unresolvedDiscrepancies,
    };
  }, [stocktakes]);

  // تصدير البيانات
  const exportToPDF = () => {
    const doc = new jsPDF();
    
    // إعداد الخط العربي
    doc.addFont('/fonts/Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');
    
    doc.text('تقرير عمليات الجرد', 105, 20, { align: 'center' });
    
    const tableData = filteredStocktakes.map(stocktake => [
      stocktake.id,
      getWarehouseById(stocktake.warehouseId)?.name || 'غير محدد',
      stocktake.type === 'full' ? 'كامل' : stocktake.type === 'partial' ? 'جزئي' : 'دوري',
      stocktake.status === 'planned' ? 'مخطط' : 
      stocktake.status === 'in-progress' ? 'قيد التنفيذ' : 
      stocktake.status === 'completed' ? 'مكتمل' : 
      stocktake.status === 'paused' ? 'متوقف' : 'ملغي',
      `${stocktake.progress}%`,
      stocktake.discrepancies.length.toString(),
      new Date(stocktake.createdAt).toLocaleDateString('ar-SA'),
    ]);

    autoTable(doc, {
      head: [['المعرف', 'المستودع', 'النوع', 'الحالة', 'التقدم', 'التباينات', 'تاريخ الإنشاء']],
      body: tableData,
      startY: 30,
      styles: { font: 'Amiri', fontSize: 10 },
      headStyles: { fillColor: [59, 130, 246] },
    });

    doc.save('stocktaking-report.pdf');
  };

  const exportToExcel = () => {
    const data = filteredStocktakes.map(stocktake => ({
      'المعرف': stocktake.id,
      'المستودع': getWarehouseById(stocktake.warehouseId)?.name || 'غير محدد',
      'النوع': stocktake.type === 'full' ? 'كامل' : stocktake.type === 'partial' ? 'جزئي' : 'دوري',
      'الحالة': stocktake.status === 'planned' ? 'مخطط' : 
                stocktake.status === 'in-progress' ? 'قيد التنفيذ' : 
                stocktake.status === 'completed' ? 'مكتمل' : 
                stocktake.status === 'paused' ? 'متوقف' : 'ملغي',
      'التقدم': `${stocktake.progress}%`,
      'عدد العناصر': (Array.isArray(stocktake.items) ? stocktake.items.length : 0),
      'التباينات': stocktake.discrepancies.length,
      'الوصف': stocktake.description,
      'تاريخ الإنشاء': new Date(stocktake.createdAt).toLocaleDateString('ar-SA'),
      'تاريخ البدء': stocktake.startDate ? new Date(stocktake.startDate).toLocaleDateString('ar-SA') : '-',
      'تاريخ الانتهاء': stocktake.endDate ? new Date(stocktake.endDate).toLocaleDateString('ar-SA') : '-',
    }));

    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'عمليات الجرد');
    XLSX.writeFile(wb, 'stocktaking-report.xlsx');
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: StocktakeStatus) => {
    switch (status) {
      case 'planned': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'paused': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // الحصول على أيقونة الحالة
  const getStatusIcon = (status: StocktakeStatus) => {
    switch (status) {
      case 'planned': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <Play className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* الإحصائيات العامة */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Archive className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">إجمالي العمليات</p>
              <p className="text-2xl font-bold">{stocktakeStats.total}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <Clock className="h-8 w-8 text-gray-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">مخططة</p>
              <p className="text-2xl font-bold">{stocktakeStats.planned}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <Play className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">قيد التنفيذ</p>
              <p className="text-2xl font-bold">{stocktakeStats.inProgress}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">مكتملة</p>
              <p className="text-2xl font-bold">{stocktakeStats.completed}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <AlertTriangle className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">إجمالي التباينات</p>
              <p className="text-2xl font-bold">{stocktakeStats.totalDiscrepancies}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <X className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">تباينات غير محلولة</p>
              <p className="text-2xl font-bold">{stocktakeStats.unresolvedDiscrepancies}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* التبويبات الرئيسية */}
      <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list">قائمة العمليات</TabsTrigger>
          <TabsTrigger value="active">الجرد النشط</TabsTrigger>
          <TabsTrigger value="analysis">التحليل والتقارير</TabsTrigger>
        </TabsList>

        {/* قائمة العمليات */}
        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>عمليات الجرد</CardTitle>
                  <CardDescription>إدارة جميع عمليات جرد المخزون</CardDescription>
                </div>
                <Button onClick={() => setShowCreateModal(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  إنشاء جرد جديد
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* أدوات البحث والفلترة */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="البحث في عمليات الجرد..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={filter.status || ''} onValueChange={(value) => setFilter({...filter, status: value as StocktakeStatus})}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="فلترة بالحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الحالات</SelectItem>
                    <SelectItem value="planned">مخطط</SelectItem>
                    <SelectItem value="in-progress">قيد التنفيذ</SelectItem>
                    <SelectItem value="paused">متوقف</SelectItem>
                    <SelectItem value="completed">مكتمل</SelectItem>
                    <SelectItem value="cancelled">ملغي</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filter.warehouseId || ''} onValueChange={(value) => setFilter({...filter, warehouseId: value})}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="فلترة بالمستودع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع المستودعات</SelectItem>
                    {warehouses.map((warehouse) => (
                      <SelectItem key={warehouse.id} value={warehouse.id}>{warehouse.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* جدول عمليات الجرد */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>المعرف</TableHead>
                      <TableHead>المستودع</TableHead>
                      <TableHead>النوع</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>التقدم</TableHead>
                      <TableHead>التباينات</TableHead>
                      <TableHead>تاريخ الإنشاء</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStocktakes.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          لا توجد عمليات جرد
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredStocktakes.map((stocktake) => (
                        <TableRow key={stocktake.id}>
                          <TableCell className="font-medium">{stocktake.id}</TableCell>
                          <TableCell>{getWarehouseById(stocktake.warehouseId)?.name || 'غير محدد'}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {stocktake.type === 'full' ? 'كامل' : 
                               stocktake.type === 'partial' ? 'جزئي' : 'دوري'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(stocktake.status)}>
                              {getStatusIcon(stocktake.status)}
                              <span className="mr-1">
                                {stocktake.status === 'planned' ? 'مخطط' : 
                                 stocktake.status === 'in-progress' ? 'قيد التنفيذ' : 
                                 stocktake.status === 'completed' ? 'مكتمل' : 
                                 stocktake.status === 'paused' ? 'متوقف' : 'ملغي'}
                              </span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Progress value={stocktake.progress} className="w-[60px]" />
                              <span className="text-sm font-medium">{stocktake.progress}%</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {stocktake.discrepancies.length > 0 ? (
                              <Badge variant="destructive">{stocktake.discrepancies.length}</Badge>
                            ) : (
                              <span className="text-green-600">0</span>
                            )}
                          </TableCell>
                          <TableCell>{new Date(stocktake.createdAt).toLocaleDateString('ar-SA')}</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedStocktake(stocktake);
                                  setShowDetailsModal(true);
                                }}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              {stocktake.status === 'planned' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleStartStocktake(stocktake.id)}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                              )}
                              {stocktake.status === 'in-progress' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handlePauseStocktake(stocktake.id)}
                                >
                                  <Pause className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                إجمالي {filteredStocktakes.length} عملية جرد
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={exportToPDF}>
                  <FileDown className="h-4 w-4 mr-2" />
                  تصدير PDF
                </Button>
                <Button variant="outline" onClick={exportToExcel}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  تصدير Excel
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* الجرد النشط */}
        <TabsContent value="active" className="space-y-4">
          {activeStocktake ? (
            <div className="space-y-4">
              {/* معلومات الجرد النشط */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>الجرد النشط: {activeStocktake.id}</span>
                    <div className="flex space-x-2">
                      <Button variant="outline" onClick={() => handlePauseStocktake(activeStocktake.id)}>
                        <Pause className="h-4 w-4 mr-2" />
                        إيقاف مؤقت
                      </Button>
                      <Button onClick={() => handleCompleteStocktake(activeStocktake.id)}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        إكمال الجرد
                      </Button>
                    </div>
                  </CardTitle>
                  <CardDescription>
                    المستودع: {getWarehouseById(activeStocktake.warehouseId)?.name} | 
                    النوع: {activeStocktake.type === 'full' ? 'كامل' : activeStocktake.type === 'partial' ? 'جزئي' : 'دوري'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Progress value={activeStocktake.progress} className="flex-1" />
                    <span className="text-sm font-medium">{activeStocktake.progress}%</span>
                  </div>
                </CardContent>
              </Card>

              {/* أدوات المسح */}
              <Card>
                <CardHeader>
                  <CardTitle>مسح الأجهزة</CardTitle>
                  <CardDescription>امسح الرقم التسلسلي أو الباركود للجهاز</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="scannedCode">الرقم التسلسلي/الباركود</Label>
                      <Input
                        id="scannedCode"
                        value={scannedCode}
                        onChange={(e) => setScannedCode(e.target.value)}
                        placeholder="امسح أو اكتب الرقم"
                        onKeyDown={(e) => e.key === 'Enter' && handleAddStocktakeItem()}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="physicalQuantity">الكمية الفعلية</Label>
                      <Input
                        id="physicalQuantity"
                        type="number"
                        value={physicalQuantity}
                        onChange={(e) => setPhysicalQuantity(e.target.value)}
                        placeholder="1"
                        min="0"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button onClick={handleAddStocktakeItem} className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        إضافة عنصر
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* العناصر المجردة */}
              <Card>
                <CardHeader>
                  <CardTitle>العناصر المجردة ({(Array.isArray(activeStocktake.items) ? activeStocktake.items.length : 0)})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>الجهاز</TableHead>
                          <TableHead>الكمية المسجلة</TableHead>
                          <TableHead>الكمية الفعلية</TableHead>
                          <TableHead>التباين</TableHead>
                          <TableHead>وقت الفحص</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {(Array.isArray(activeStocktake.items) ? activeStocktake.items.length  : 0) === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="h-24 text-center">
                              لم يتم جرد أي أجهزة بعد
                            </TableCell>
                          </TableRow>
                        ) : (
                          (Array.isArray(activeStocktake.items) ? activeStocktake.items : []).map((item) => {
                            const device = getDeviceById(item.deviceId);
                            return (
                              <TableRow key={item.id}>
                                <TableCell>{device?.serialNumber || 'غير موجود'}</TableCell>
                                <TableCell>{item.systemQuantity}</TableCell>
                                <TableCell>{item.physicalQuantity}</TableCell>
                                <TableCell>
                                  {item.variance !== 0 ? (
                                    <Badge variant={item.variance > 0 ? "default" : "destructive"}>
                                      {item.variance > 0 ? '+' : ''}{item.variance}
                                    </Badge>
                                  ) : (
                                    <span className="text-green-600">0</span>
                                  )}
                                </TableCell>
                                <TableCell>{new Date(item.checkedAt).toLocaleString('ar-SA')}</TableCell>
                              </TableRow>
                            );
                          })
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-16">
                <ClipboardList className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">لا توجد عملية جرد نشطة</h3>
                <p className="text-muted-foreground text-center mb-4">
                  يمكنك بدء عملية جرد جديدة من قائمة العمليات
                </p>
                <Button onClick={() => setSelectedTab('list')}>
                  العودة إلى قائمة العمليات
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* التحليل والتقارير */}
        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>تحليل عمليات الجرد</CardTitle>
              <CardDescription>إحصائيات وتقارير مفصلة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* توزيع الحالات */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">توزيع حالات العمليات</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span>مخططة</span>
                      <Badge>{stocktakeStats.planned}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>قيد التنفيذ</span>
                      <Badge>{stocktakeStats.inProgress}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>مكتملة</span>
                      <Badge>{stocktakeStats.completed}</Badge>
                    </div>
                  </div>
                </div>

                {/* إحصائيات التباينات */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">إحصائيات التباينات</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span>إجمالي التباينات</span>
                      <Badge variant="destructive">{stocktakeStats.totalDiscrepancies}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>غير محلولة</span>
                      <Badge variant="destructive">{stocktakeStats.unresolvedDiscrepancies}</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* نافذة إنشاء عملية جرد جديدة */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>إنشاء عملية جرد جديدة</DialogTitle>
            <DialogDescription>
              املأ المعلومات المطلوبة لإنشاء عملية جرد جديدة
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="warehouseId">المستودع *</Label>
              <Select value={formData.warehouseId} onValueChange={(value) => setFormData({...formData, warehouseId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر المستودع" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.map((warehouse) => (
                    <SelectItem key={warehouse.id} value={warehouse.id}>{warehouse.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">نوع الجرد *</Label>
              <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value as any})}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الجرد" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full">جرد كامل</SelectItem>
                  <SelectItem value="partial">جرد جزئي</SelectItem>
                  <SelectItem value="cycle">جرد دوري</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">الوصف</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="وصف عملية الجرد"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="assigneeId">المسؤول *</Label>
              <Input
                id="assigneeId"
                value={formData.assigneeId}
                onChange={(e) => setFormData({...formData, assigneeId: e.target.value})}
                placeholder="معرف المسؤول"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="scheduledDate">تاريخ الجدولة</Label>
              <Input
                id="scheduledDate"
                type="datetime-local"
                value={formData.scheduledDate}
                onChange={(e) => setFormData({...formData, scheduledDate: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                placeholder="ملاحظات إضافية"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              إلغاء
            </Button>
            <Button onClick={handleCreateStocktake}>
              إنشاء عملية الجرد
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نافذة تفاصيل عملية الجرد */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>تفاصيل عملية الجرد</DialogTitle>
            <DialogDescription>
              معلومات مفصلة عن عملية الجرد المحددة
            </DialogDescription>
          </DialogHeader>
          {selectedStocktake && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">المعرف</Label>
                  <p className="text-sm">{selectedStocktake.id}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">المستودع</Label>
                  <p className="text-sm">{getWarehouseById(selectedStocktake.warehouseId)?.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">النوع</Label>
                  <p className="text-sm">
                    {selectedStocktake.type === 'full' ? 'كامل' : 
                     selectedStocktake.type === 'partial' ? 'جزئي' : 'دوري'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">الحالة</Label>
                  <Badge className={getStatusColor(selectedStocktake.status)}>
                    {selectedStocktake.status === 'planned' ? 'مخطط' : 
                     selectedStocktake.status === 'in-progress' ? 'قيد التنفيذ' : 
                     selectedStocktake.status === 'completed' ? 'مكتمل' : 
                     selectedStocktake.status === 'paused' ? 'متوقف' : 'ملغي'}
                  </Badge>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">التقدم</Label>
                <div className="flex items-center space-x-2 mt-1">
                  <Progress value={selectedStocktake.progress} className="flex-1" />
                  <span className="text-sm font-medium">{selectedStocktake.progress}%</span>
                </div>
              </div>
              {selectedStocktake.notes && (
                <div>
                  <Label className="text-sm font-medium">الملاحظات</Label>
                  <p className="text-sm text-muted-foreground">{selectedStocktake.notes}</p>
                </div>
              )}
              {selectedStocktake.discrepancies.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">التباينات ({selectedStocktake.discrepancies.length})</Label>
                  <div className="mt-2 space-y-2">
                    {selectedStocktake.discrepancies.map((discrepancy) => (
                      <div key={discrepancy.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <p className="text-sm font-medium">
                            {getDeviceById(discrepancy.deviceId)?.serialNumber || 'غير موجود'}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {discrepancy.type === 'surplus' ? 'فائض' : 'نقص'}: {discrepancy.variance}
                          </p>
                        </div>
                        <Badge variant={discrepancy.status === 'resolved' ? 'default' : 'destructive'}>
                          {discrepancy.status === 'resolved' ? 'محلول' : 'غير محلول'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetailsModal(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
