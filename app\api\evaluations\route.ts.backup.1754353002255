import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع أوامر التقييم مع include مختلف حسب النوع
    const includeOptions = view === 'simple' 
      ? { items: false } // لا نحتاج items في العرض البسيط
      : { items: true };

    const evaluations = await prisma.evaluationOrder.findMany({
      include: includeOptions,
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(evaluations);
  } catch (error) {
    console.error('Failed to fetch evaluations:', error);
    return NextResponse.json({ error: 'Failed to fetch evaluations' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newEvaluation = await request.json();

    // دالة تنظيف البيانات من null bytes محسنة مع معالجة النصوص العربية
    const sanitizeString = (str: any): string | null => {
      if (str === null || str === undefined) return null;
      if (typeof str !== 'string') return String(str);
      
      // تنظيف أساسي من null bytes وأحرف التحكم
      let cleaned = str
        .replace(/\0/g, '')             // null bytes
        .replace(/[\x00-\x08]/g, '')    // أحرف تحكم منخفضة
        .replace(/[\x0B\x0C]/g, '')     // تابع أحرف تحكم
        .replace(/[\x0E-\x1F]/g, '')    // أحرف تحكم أخرى
        .replace(/[\x7F-\x9F]/g, '')    // أحرف تحكم عالية
        .trim();
      
      return cleaned || null;
    };

    // دالة تنظيف التاريخ
    const sanitizeDate = (dateStr: any): string | null => {
      if (!dateStr) return null;
      if (typeof dateStr !== 'string') return null;
      
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return null;
        return date.toISOString();
      } catch {
        return null;
      }
    };

    // تنظيف البيانات الأساسية
    const cleanedEvaluation = {
      ...newEvaluation,
      orderId: sanitizeString(newEvaluation.orderId),
      employeeName: sanitizeString(newEvaluation.employeeName),
      notes: sanitizeString(newEvaluation.notes),
      warehouseName: sanitizeString(newEvaluation.warehouseName),
      acknowledgedBy: sanitizeString(newEvaluation.acknowledgedBy),
      date: sanitizeDate(newEvaluation.date),
      acknowledgedDate: sanitizeDate(newEvaluation.acknowledgedDate),
      items: newEvaluation.items ? newEvaluation.items.map((item: any) => ({
        ...item,
        deviceId: sanitizeString(item.deviceId) || '',
        model: sanitizeString(item.model) || '',
        externalGrade: sanitizeString(item.externalGrade) || '',
        screenGrade: sanitizeString(item.screenGrade) || '',
        networkGrade: sanitizeString(item.networkGrade) || '',
        finalGrade: sanitizeString(item.finalGrade) || '',
        fault: sanitizeString(item.fault),
        damageType: sanitizeString(item.damageType)
      })) : []
    };

    // Basic validation
    if (!cleanedEvaluation.orderId || !cleanedEvaluation.employeeName) {
      return NextResponse.json(
        { error: 'Order ID and employee name are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if evaluation order already exists
      const existingEvaluation = await tx.evaluationOrder.findUnique({
        where: { orderId: cleanedEvaluation.orderId }
      });

      if (existingEvaluation) {
        throw new Error('Evaluation order with this ID already exists');
      }

      // إعداد البيانات للإنشاء
      const createData = {
        orderId: cleanedEvaluation.orderId,
        employeeName: cleanedEvaluation.employeeName || authResult.user!.username,
        date: cleanedEvaluation.date || new Date(),
        notes: cleanedEvaluation.notes || null,
        status: cleanedEvaluation.status || 'معلق',
        acknowledgedBy: cleanedEvaluation.acknowledgedBy || null,
        acknowledgedDate: cleanedEvaluation.acknowledgedDate || null,
        warehouseName: cleanedEvaluation.warehouseName || null,
      };

      // إنشاء أمر التقييم في قاعدة البيانات
      const evaluation = await tx.evaluationOrder.create({
        data: createData
      });

      // Create evaluationOrder items
      const createdItems = [];
      if (cleanedEvaluation.items && Array.isArray(cleanedEvaluation.items)) {
        for (const item of cleanedEvaluation.items) {
          const createdItem = await tx.evaluationOrderItem.create({
            data: {
              evaluationOrderId: evaluation.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              externalGrade: item.externalGrade || '',
              screenGrade: item.screenGrade || '',
              networkGrade: item.networkGrade || '',
              finalGrade: item.finalGrade || '',
              fault: item.fault || null,
              damageType: item.damageType || null
            }
          });
          createdItems.push(createdItem);
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created evaluation order: ${evaluation.orderId}`,
        tableName: 'evaluationOrder',
        recordId: evaluation.id.toString()
      });

      // إرجاع الأمر مع العناصر
      return {
        ...evaluation,
        items: createdItems
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create evaluation:', error);

    if (error instanceof Error && error.message === 'Evaluation order with this ID already exists') {
      return NextResponse.json({ error: 'Evaluation order with this ID already exists' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create evaluation' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedEvaluation = await request.json();

    // دالة تنظيف البيانات من null bytes
    const sanitizeString = (str: any): string | null => {
      if (!str || typeof str !== 'string') return str;
      return str.replace(/\0/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim() || null;
    };

    // تنظيف البيانات
    const cleanedEvaluation = {
      ...updatedEvaluation,
      orderId: sanitizeString(updatedEvaluation.orderId),
      employeeName: sanitizeString(updatedEvaluation.employeeName),
      notes: sanitizeString(updatedEvaluation.notes),
      warehouseName: sanitizeString(updatedEvaluation.warehouseName),
      acknowledgedBy: sanitizeString(updatedEvaluation.acknowledgedBy),
    };

    if (!cleanedEvaluation.id) {
      return NextResponse.json(
        { error: 'Evaluation ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if evaluation exists
      const existingEvaluation = await tx.evaluationOrder.findUnique({
        where: { id: cleanedEvaluation.id }
      });

      if (!existingEvaluation) {
        throw new Error('Evaluation not found');
      }

      // Update the evaluation
      const evaluation = await tx.evaluationOrder.update({
        where: { id: cleanedEvaluation.id },
        data: {
          orderId: cleanedEvaluation.orderId || existingEvaluation.orderId,
          employeeName: cleanedEvaluation.employeeName || existingEvaluation.employeeName,
          date: cleanedEvaluation.date || existingEvaluation.date,
          notes: cleanedEvaluation.notes !== undefined ? cleanedEvaluation.notes : existingEvaluation.notes,
          status: cleanedEvaluation.status || existingEvaluation.status,
          acknowledgedBy: cleanedEvaluation.acknowledgedBy !== undefined ? cleanedEvaluation.acknowledgedBy : existingEvaluation.acknowledgedBy,
          acknowledgedDate: cleanedEvaluation.acknowledgedDate !== undefined ? cleanedEvaluation.acknowledgedDate : existingEvaluation.acknowledgedDate,
          warehouseName: cleanedEvaluation.warehouseName !== undefined ? cleanedEvaluation.warehouseName : existingEvaluation.warehouseName,
        }
      });

      // تحديث العناصر - حذف القديمة وإنشاء الجديدة
      const updatedItems = [];
      if (updatedEvaluation.items && Array.isArray(updatedEvaluation.items)) {
        // حذف العناصر القديمة
        await tx.evaluationOrderItem.deleteMany({
          where: { evaluationOrderId: updatedEvaluation.id }
        });

        // إنشاء العناصر الجديدة
        for (const item of updatedEvaluation.items) {
          const createdItem = await tx.evaluationOrderItem.create({
            data: {
              evaluationOrderId: evaluation.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              externalGrade: item.externalGrade || '',
              screenGrade: item.screenGrade || '',
              networkGrade: item.networkGrade || '',
              finalGrade: item.finalGrade || '',
              fault: item.fault || null,
              damageType: item.damageType || null
            }
          });
          updatedItems.push(createdItem);
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated evaluation order: ${evaluation.orderId}`,
        tableName: 'evaluationOrder',
        recordId: evaluation.id.toString()
      });

      // إرجاع الأمر مع العناصر المحدثة
      return {
        ...evaluation,
        items: updatedItems
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update evaluation:', error);

    if (error instanceof Error && error.message === 'Evaluation not found') {
      return NextResponse.json({ error: 'Evaluation not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update evaluation' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Evaluation ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if evaluation exists
      const existingEvaluation = await tx.evaluationOrder.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingEvaluation) {
        throw new Error('Evaluation not found');
      }

      // Delete the evaluation
      await tx.evaluationOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted evaluation order: ${existingEvaluation.orderId}`,
        tableName: 'evaluationOrder',
        recordId: id.toString()
      });

      return { message: 'Evaluation deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete evaluation:', error);

    if (error instanceof Error && error.message === 'Evaluation not found') {
      return NextResponse.json({ error: 'Evaluation not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete evaluation' }, { status: 500 });
  }
}
