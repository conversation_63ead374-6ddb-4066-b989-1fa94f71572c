/**
 * Fix Remaining Components Script
 * Date: 2025-08-04
 * Description: Fix all remaining components and pages with date issues
 */

const fs = require('fs');
const path = require('path');

// Remaining components and pages to fix
const remainingFiles = [
  // Main pages
  'app/(main)/grading/2page.tsx',
  'app/(main)/inventory/page_backup.tsx',
  'app/(main)/maintenance/page.tsx',
  'app/(main)/maintenance-transfer/page.tsx',
  'app/(main)/sales/page.tsx',
  'app/(main)/supply/page.tsx',
  'app/(main)/returns/page.tsx',
  'app/(main)/stocktaking/page.tsx',
  'app/(main)/stocktaking/page_new.tsx',
  'app/(main)/test-export/page.tsx',
  'app/(main)/settings/appearance-settings.tsx',
  'app/(main)/accept-devices/page.tsx',
  'app/(main)/audit-logs/page.tsx',
  
  // Report pages
  'app/(main)/reports/client-reports/page.tsx',
  'app/(main)/reports/employee-reports/page.tsx',
  'app/(main)/reports/grading-reports/page.tsx',
  'app/(main)/reports/maintenance-reports/page.tsx',
  'app/(main)/reports/supplier-reports/page.tsx',
  
  // Components
  'components/AttachmentsViewer.tsx',
  'components/database-management.tsx',
  'components/database-management-backup.tsx',
  'components/database-management-new.tsx',
  'components/evaluation-cleanup.tsx',
  'components/ReportPreview.tsx',
  'components/requests/AdvancedSearch.tsx',
  'components/requests/EscalationDashboard.tsx',
  'components/requests/RequestConversation.tsx',
  'components/ui/chart.tsx',
  'components/ui/print-export-buttons.tsx',
  
  // Context files
  'context/stocktake-store.tsx',
  
  // Lib files
  'lib/export-utils/enhanced-html-export.ts',
  'lib/network.ts',
  'lib/notification-service.ts',
  'lib/print-templates/index.ts',
  'lib/search-service.ts',
  'lib/template-service.ts'
];

// Comprehensive fixes for remaining files
const comprehensiveFixes = [
  // Fix new Date().toISOString() patterns
  {
    search: /new Date\(\)\.toISOString\(\)\.slice\(0, 10\)/g,
    replace: 'new Date().toISOString().slice(0, 10)',
    description: 'الاحتفاظ بـ slice للتواريخ (مطلوب للـ inputs)'
  },
  {
    search: /new Date\(\)\.toISOString\(\)\.slice\(0, 16\)/g,
    replace: 'new Date().toISOString().slice(0, 16)',
    description: 'الاحتفاظ بـ slice للتاريخ والوقت (مطلوب للـ inputs)'
  },
  {
    search: /date: new Date\(\)\.toISOString\(\)\.slice\(0, 16\)/g,
    replace: 'date: new Date().toISOString().slice(0, 16)',
    description: 'الاحتفاظ بـ slice في الـ forms'
  },
  {
    search: /supplyDate: new Date\(\)\.toISOString\(\)\.slice\(0, 16\)/g,
    replace: 'supplyDate: new Date().toISOString().slice(0, 16)',
    description: 'الاحتفاظ بـ slice في supplyDate'
  },
  
  // Fix other toISOString patterns that should be removed
  {
    search: /timestamp: new Date\(\)\.toISOString\(\)/g,
    replace: 'timestamp: new Date()',
    description: 'استخدام Date object للـ timestamp'
  },
  {
    search: /createdAt: new Date\(\)\.toISOString\(\)/g,
    replace: 'createdAt: new Date()',
    description: 'استخدام Date object للـ createdAt'
  },
  {
    search: /uploadedAt: [^.]*\.toISOString\(\)/g,
    replace: 'uploadedAt: new Date()',
    description: 'استخدام Date object للـ uploadedAt'
  },
  {
    search: /lastSavedAt: new Date\(\)\.toISOString\(\)/g,
    replace: 'lastSavedAt: new Date()',
    description: 'استخدام Date object للـ lastSavedAt'
  },
  {
    search: /completedAt: new Date\(\)\.toISOString\(\)/g,
    replace: 'completedAt: new Date()',
    description: 'استخدام Date object للـ completedAt'
  },
  {
    search: /startTime: new Date\(\)\.toISOString\(\)/g,
    replace: 'startTime: new Date()',
    description: 'استخدام Date object للـ startTime'
  },
  
  // Fix toLocaleDateString patterns
  {
    search: /\.toLocaleDateString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString العربية'
  },
  {
    search: /\.toLocaleDateString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString السعودية'
  },
  {
    search: /\.toLocaleDateString\('en-US'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString الإنجليزية'
  },
  {
    search: /\.toLocaleString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleString العربية'
  },
  {
    search: /\.toLocaleString\('ar-SA'\)/g,
    replace: '',
    description: 'إزالة toLocaleString السعودية'
  },
  {
    search: /\.toLocaleString\('en-US'\)/g,
    replace: '',
    description: 'إزالة toLocaleString الإنجليزية'
  },
  
  // Fix manual date formatting
  {
    search: /toDate\.setDate\(toDate\.getDate\(\) \+ 1\);/g,
    replace: 'toDate = new Date(toDate.getTime() + 24 * 60 * 60 * 1000);',
    description: 'تحسين عمليات التاريخ'
  },
  
  // Fix string date types in function parameters
  {
    search: /const formatDateTime = \(dateTimeString: string\): string/g,
    replace: 'const formatDateTime = (dateTime: Date | string): string',
    description: 'تحسين نوع معامل formatDateTime'
  },
  {
    search: /function formatArabicDate\(date: Date \| string\): string/g,
    replace: 'function formatArabicDate(date: Date): string',
    description: 'تحسين دالة formatArabicDate'
  },
  {
    search: /const updateRequestStatus = async \(requestId: number, status: string, notes\?: string\)/g,
    replace: 'const updateRequestStatus = async (requestId: number, status: string, notes?: string)',
    description: 'تحسين دالة updateRequestStatus'
  },
  
  // Fix interface date types
  {
    search: /lastMessageDate: string;/g,
    replace: 'lastMessageDate: Date;',
    description: 'تحويل lastMessageDate إلى Date'
  },
  {
    search: /maintenanceStartDate: string;/g,
    replace: 'maintenanceStartDate: Date;',
    description: 'تحويل maintenanceStartDate إلى Date'
  },
  {
    search: /dateFrom: string;/g,
    replace: 'dateFrom: Date;',
    description: 'تحويل dateFrom إلى Date'
  },
  {
    search: /dateTo: string;/g,
    replace: 'dateTo: Date;',
    description: 'تحويل dateTo إلى Date'
  },
  {
    search: /createdAt: string;/g,
    replace: 'createdAt: Date;',
    description: 'تحويل createdAt إلى Date'
  },
  {
    search: /updatedAt: string;/g,
    replace: 'updatedAt: Date;',
    description: 'تحويل updatedAt إلى Date'
  }
];

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixRemainingComponents() {
  console.log('🔧 إصلاح المكونات والصفحات المتبقية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of remainingFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${remainingFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply fixes
        if (applyFixes(filePath, comprehensiveFixes)) {
          totalFixed += comprehensiveFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 10 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${remainingFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح المكونات والصفحات المتبقية:');
    console.log('='.repeat(55));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${remainingFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 أول 15 ملف معدل:');
      filesModified.slice(0, 15).forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
      
      if (filesModified.length > 15) {
        console.log(`... و ${filesModified.length - 15} ملف آخر`);
      }
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: remainingFiles.length,
      fixes: comprehensiveFixes
    };

    fs.writeFileSync('remaining-components-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح المكونات في: remaining-components-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح المكونات والصفحات المتبقية بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح في المكونات المتبقية');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح المكونات المتبقية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixRemainingComponents()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح المكونات والصفحات المتبقية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح المكونات المتبقية:', error);
      process.exit(1);
    });
}

module.exports = { fixRemainingComponents };
