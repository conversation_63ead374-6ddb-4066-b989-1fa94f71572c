import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// مستويات التصعيد
const ESCALATION_LEVELS: Record<string, any[]> = {
  'عادي': [
    { level: 1, timeThreshold: 24 * 60 },
    { level: 2, timeThreshold: 48 * 60 }
  ],
  'طاريء': [
    { level: 1, timeThreshold: 4 * 60 },
    { level: 2, timeThreshold: 8 * 60 }
  ],
  'طاريء جدا': [
    { level: 1, timeThreshold: 60 },
    { level: 2, timeThreshold: 2 * 60 }
  ]
};

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const now = new Date();

    // جلب الطلبات المعلقة
    const pendingRequests = await prisma.employeeRequest.findMany({
      where: {
        status: {
          in: ['قيد المراجعة', 'قيد المراجعة المتقدمة']
        }
      },
      orderBy: {
        requestDate: 'asc'
      }
    });

    // معالجة الطلبات وحساب معلومات التصعيد
    const overdueRequests = pendingRequests
      .map(request => {
        const requestDate = new Date(request.requestDate);
        const minutesSinceRequest = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60));
        
        const escalationLevels = ESCALATION_LEVELS[request.priority] || ESCALATION_LEVELS['عادي'];
        
        // تحديد مستوى التصعيد الحالي
        let currentEscalationLevel = 0;
        let nextEscalationIn = 0;
        
        for (let i = 0; i < escalationLevels.length; i++) {
          const level = escalationLevels[i];
          if (minutesSinceRequest >= level.timeThreshold) {
            currentEscalationLevel = level.level;
          } else {
            nextEscalationIn = level.timeThreshold - minutesSinceRequest;
            break;
          }
        }

        // إذا تجاوز جميع مستويات التصعيد
        if (currentEscalationLevel === escalationLevels[escalationLevels.length - 1].level) {
          nextEscalationIn = 0; // لا يوجد تصعيد تالي
        }

        return {
          id: request.id,
          requestNumber: request.requestNumber,
          employeeName: request.employeeName,
          requestType: request.requestType,
          priority: request.priority,
          requestDate: request.requestDate,
          minutesOverdue: Math.max(0, minutesSinceRequest - (escalationLevels[0]?.timeThreshold || 0)),
          escalationLevel: currentEscalationLevel,
          nextEscalationIn: Math.max(0, nextEscalationIn)
        };
      })
      .filter(request => {
        // فلترة الطلبات المتأخرة فقط
        const escalationLevels = ESCALATION_LEVELS[request.priority] || ESCALATION_LEVELS['عادي'];
        const firstThreshold = escalationLevels[0]?.timeThreshold || 0;
        
        const requestDate = new Date(request.requestDate);
        const minutesSinceRequest = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60));
        
        return minutesSinceRequest >= firstThreshold;
      })
      .sort((a, b) => {
        // ترتيب حسب الأولوية ثم حسب الوقت
        const priorityOrder = { 'طاريء جدا': 3, 'طاريء': 2, 'عادي': 1 };
        const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 1;
        const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 1;
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority; // الأولوية الأعلى أولاً
        }
        
        return b.minutesOverdue - a.minutesOverdue; // الأكثر تأخيراً أولاً
      });

    return NextResponse.json(overdueRequests);
  } catch (error) {
    console.error('خطأ في جلب الطلبات المتأخرة:', error);
    return NextResponse.json({ error: 'Failed to fetch overdue requests' }, { status: 500 });
  }
}
