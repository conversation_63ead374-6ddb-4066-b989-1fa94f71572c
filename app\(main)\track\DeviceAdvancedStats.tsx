"use client";

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';
import { 
  Package, 
  Eye, 
  Wrench, 
  ShoppingCart, 
  Undo2, 
  MapPin, 
  TrendingUp, 
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar
} from 'lucide-react';

interface TimelineEvent {
  id?: string;
  title: string;
  description: string;
  date: Date;
  color: string;
  user?: string;
  details?: any;
}

interface DeviceStatsProps {
  events: TimelineEvent[];
  deviceData?: any;
}

const DeviceAdvancedStats: React.FC<DeviceStatsProps> = ({ events, deviceData }) => {
  const stats = useMemo(() => {
    const totalEvents = events.length;
    const supplyEvents = events.filter(e => e.title.includes('توريد')).length;
    const evaluationEvents = events.filter(e => e.title.includes('فحص') || e.title.includes('تقييم')).length;
    const maintenanceEvents = events.filter(e => e.title.includes('صيانة')).length;
    const saleEvents = events.filter(e => e.title.includes('بيع')).length;
    const returnEvents = events.filter(e => e.title.includes('إرجاع')).length;
    const transferEvents = events.filter(e => e.title.includes('تحويل')).length;
    const replacementEvents = events.filter(e => e.title.includes('بديل')).length;

    // حساب الفترات الزمنية
    const sortedEvents = [...events].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const firstEvent = sortedEvents[0];
    const lastEvent = sortedEvents[sortedEvents.length - 1];
    
    let deviceAge = 0;
    let lastActivityDays = 0;
    
    if (firstEvent && lastEvent) {
      const firstDate = new Date(firstEvent.date);
      const lastDate = new Date(lastEvent.date);
      const now = new Date();
      
      deviceAge = Math.floor((now.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24));
      lastActivityDays = Math.floor((now.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24));
    }

    // تحليل الاتجاهات
    const recentEvents = events.filter(e => {
      const eventDate = new Date(e.date);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return eventDate >= thirtyDaysAgo;
    }).length;

    const previousMonthEvents = events.filter(e => {
      const eventDate = new Date(e.date);
      const sixtyDaysAgo = new Date();
      const thirtyDaysAgo = new Date();
      sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000);
      thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return eventDate >= sixtyDaysAgo && eventDate < thirtyDaysAgo;
    }).length;

    const trend = recentEvents > previousMonthEvents ? 'up' : 
                  recentEvents < previousMonthEvents ? 'down' : 'stable';

    // حالة الجهاز
    const hasMaintenanceIssues = maintenanceEvents > 0;
    const hasRecentActivity = lastActivityDays < 7;
    const isActive = hasRecentActivity && !hasMaintenanceIssues;

    return {
      totalEvents,
      supplyEvents,
      evaluationEvents,
      maintenanceEvents,
      saleEvents,
      returnEvents,
      transferEvents,
      replacementEvents,
      deviceAge,
      lastActivityDays,
      recentEvents,
      trend,
      isActive,
      hasMaintenanceIssues,
      hasRecentActivity
    };
  }, [events]);

  const statCards = [
    {
      title: 'إجمالي العمليات',
      value: stats.totalEvents,
      icon: <Package className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'عمليات التوريد',
      value: stats.supplyEvents,
      icon: <Package className="w-6 h-6" />,
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-50',
      borderColor: 'border-cyan-200'
    },
    {
      title: 'عمليات التقييم',
      value: stats.evaluationEvents,
      icon: <Eye className="w-6 h-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200'
    },
    {
      title: 'عمليات الصيانة',
      value: stats.maintenanceEvents,
      icon: <Wrench className="w-6 h-6" />,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200'
    },
    {
      title: 'عمليات البيع',
      value: stats.saleEvents,
      icon: <ShoppingCart className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'عمليات الإرجاع',
      value: stats.returnEvents,
      icon: <Undo2 className="w-6 h-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      title: 'عمليات التحويل',
      value: stats.transferEvents,
      icon: <MapPin className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      title: 'عمليات الاستبدال',
      value: stats.replacementEvents,
      icon: <Package className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  ];

  const getTrendIcon = () => {
    switch (stats.trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  const getStatusBadge = () => {
    if (stats.isActive) {
      return (
        <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="w-3 h-3" />
          نشط
        </Badge>
      );
    } else if (stats.hasMaintenanceIssues) {
      return (
        <Badge className="bg-amber-100 text-amber-800 flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          يحتاج صيانة
        </Badge>
      );
    } else {
      return (
        <Badge className="bg-gray-100 text-gray-800 flex items-center gap-1">
          <Clock className="w-3 h-3" />
          خامل
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* بطاقة الحالة العامة */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-white">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-bold text-gray-900">
              حالة الجهاز
            </CardTitle>
            {getStatusBadge()}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 flex items-center justify-center gap-2">
                <Calendar className="w-6 h-6" />
                {stats.deviceAge}
              </div>
              <p className="text-sm text-gray-600 mt-1">يوم في النظام</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 flex items-center justify-center gap-2">
                <Clock className="w-6 h-6" />
                {stats.lastActivityDays}
              </div>
              <p className="text-sm text-gray-600 mt-1">يوم آخر نشاط</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 flex items-center justify-center gap-2">
                {getTrendIcon()}
                {stats.recentEvents}
              </div>
              <p className="text-sm text-gray-600 mt-1">عملية آخر 30 يوم</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 flex items-center justify-center gap-2">
                <Package className="w-6 h-6" />
                {stats.totalEvents}
              </div>
              <p className="text-sm text-gray-600 mt-1">إجمالي العمليات</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إحصائيات العمليات */}
      <div className="stats-grid">
        {statCards.map((stat, index) => (
          <Card 
            key={index} 
            className={`stat-card hover:shadow-lg transition-all duration-300 ${stat.bgColor} ${stat.borderColor} border-2`}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-2">
                    {stat.title}
                  </p>
                  <div className={`stat-number ${stat.color}`}>
                    {stat.value}
                  </div>
                </div>
                <div className={`${stat.color}`}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* مؤشرات الأداء */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-bold text-gray-900">
            مؤشرات الأداء
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {stats.totalEvents > 0 ? Math.round((stats.supplyEvents / stats.totalEvents) * 100) : 0}%
              </div>
              <p className="text-sm text-gray-600">نسبة عمليات التوريد</p>
            </div>
            <div className="text-center p-4 bg-amber-50 rounded-lg">
              <div className="text-3xl font-bold text-amber-600 mb-2">
                {stats.totalEvents > 0 ? Math.round((stats.maintenanceEvents / stats.totalEvents) * 100) : 0}%
              </div>
              <p className="text-sm text-gray-600">نسبة عمليات الصيانة</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {stats.totalEvents > 0 ? Math.round(((stats.saleEvents + stats.transferEvents) / stats.totalEvents) * 100) : 0}%
              </div>
              <p className="text-sm text-gray-600">نسبة التسليم الناجح</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DeviceAdvancedStats;
