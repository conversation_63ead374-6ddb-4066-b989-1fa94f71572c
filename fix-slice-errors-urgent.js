/**
 * Fix Slice Errors Urgent Script
 * Date: 2025-08-04
 * Description: Fix all new Date().slice() errors immediately
 */

const fs = require('fs');
const path = require('path');

// Files with slice errors that need immediate fixing
const sliceErrorFiles = [
  'app/(main)/grading/page.tsx',
  'app/(main)/inventory/page.tsx',
  'app/(main)/maintenance/page.tsx',
  'app/(main)/maintenance-transfer/page.tsx',
  'app/(main)/returns/page.tsx'
];

// Critical fixes for slice errors
const sliceErrorFixes = [
  // Fix new Date().slice() patterns
  {
    search: /new Date\(\)\.slice\(0, 16\)/g,
    replace: 'new Date().toISOString().slice(0, 16)',
    description: 'إصلاح new Date().slice(0, 16) إلى new Date().toISOString().slice(0, 16)'
  },
  {
    search: /new Date\(\)\.slice\(0, 10\)/g,
    replace: 'new Date().toISOString().slice(0, 10)',
    description: 'إصلاح new Date().slice(0, 10) إلى new Date().toISOString().slice(0, 10)'
  },
  
  // Fix specific patterns in different contexts
  {
    search: /defaultValue=\{new Date\(\)\.slice\(0, 16\)\}/g,
    replace: 'defaultValue={new Date().toISOString().slice(0, 16)}',
    description: 'إصلاح defaultValue مع slice'
  },
  {
    search: /value=\{formState\.date \|\| new Date\(\)\.slice\(0, 16\)\}/g,
    replace: 'value={formState.date || new Date().toISOString().slice(0, 16)}',
    description: 'إصلاح value مع slice'
  },
  {
    search: /date: new Date\(\)\.slice\(0, 16\)/g,
    replace: 'date: new Date().toISOString().slice(0, 16)',
    description: 'إصلاح date property مع slice'
  },
  {
    search: /setOrderDate\(new Date\(\)\.slice\(0, 16\)\)/g,
    replace: 'setOrderDate(new Date().toISOString().slice(0, 16))',
    description: 'إصلاح setOrderDate مع slice'
  },
  {
    search: /setDeliveryOrderDate\(new Date\(\)\.slice\(0, 16\)\)/g,
    replace: 'setDeliveryOrderDate(new Date().toISOString().slice(0, 16))',
    description: 'إصلاح setDeliveryOrderDate مع slice'
  },
  {
    search: /setOrderDate\(draft\.orderDate \|\| new Date\(\)\.slice\(0, 16\)\)/g,
    replace: 'setOrderDate(draft.orderDate || new Date().toISOString().slice(0, 16))',
    description: 'إصلاح setOrderDate مع draft'
  },
  {
    search: /setDeliveryOrderDate\(draft\.deliveryOrderDate \|\| new Date\(\)\.slice\(0, 16\)\)/g,
    replace: 'setDeliveryOrderDate(draft.deliveryOrderDate || new Date().toISOString().slice(0, 16))',
    description: 'إصلاح setDeliveryOrderDate مع draft'
  },
  
  // Fix in file names and exports
  {
    search: /doc\.save\(`inventory_report_\$\{new Date\(\)\.slice\(0, 10\)\}\.pdf`\)/g,
    replace: 'doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`)',
    description: 'إصلاح doc.save مع slice'
  },
  {
    search: /`inventory_report_\$\{new Date\(\)\.slice\(0, 10\)\}\.xlsx`/g,
    replace: '`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`',
    description: 'إصلاح اسم ملف Excel مع slice'
  },
  {
    search: /doc\.save\(`\$\{deviceDetails\?\.title\.replace\(\/\\s\+\/g, '_'\)\}_\$\{new Date\(\)\.slice\(0, 10\)\}\.pdf`\)/g,
    replace: 'doc.save(`${deviceDetails?.title.replace(/\\s+/g, \'_\')}_${new Date().toISOString().slice(0, 10)}.pdf`)',
    description: 'إصلاح doc.save للجهاز مع slice'
  },
  {
    search: /`\$\{deviceDetails\?\.title\.replace\(\/\\s\+\/g, '_'\)\}_\$\{new Date\(\)\.slice\(0, 10\)\}\.xlsx`/g,
    replace: '`${deviceDetails?.title.replace(/\\s+/g, \'_\')}_${new Date().toISOString().slice(0, 10)}.xlsx`',
    description: 'إصلاح اسم ملف Excel للجهاز مع slice'
  },
  
  // Fix in onClick handlers
  {
    search: /onClick=\{\(\) => setFormState\(s => \(\{ \.\.\.s, date: new Date\(\)\.slice\(0, 16\) \}\)\)\}/g,
    replace: 'onClick={() => setFormState(s => ({ ...s, date: new Date().toISOString().slice(0, 16) }))}',
    description: 'إصلاح onClick handler مع slice'
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixSliceErrors() {
  console.log('🚨 إصلاح أخطاء slice الحرجة...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of sliceErrorFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${sliceErrorFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        if (applyFixes(filePath, sliceErrorFixes)) {
          totalFixed += sliceErrorFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح أخطاء slice:');
    console.log('='.repeat(40));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${sliceErrorFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: sliceErrorFiles.length,
      fixes: sliceErrorFixes
    };

    fs.writeFileSync('slice-errors-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح slice في: slice-errors-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح جميع أخطاء slice بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على أخطاء slice للإصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح أخطاء slice:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixSliceErrors()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح أخطاء slice');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح أخطاء slice:', error);
      process.exit(1);
    });
}

module.exports = { fixSliceErrors };
