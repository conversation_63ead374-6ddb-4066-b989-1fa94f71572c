<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API لرقم SO التالي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .token-input {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API لرقم SO التالي</h1>
        
        <div class="test-section">
            <h3>🔑 إعداد التوكن</h3>
            <p>أدخل التوكن الخاص بك:</p>
            <input type="text" id="tokenInput" class="token-input" placeholder="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...">
            <button onclick="saveToken()">حفظ التوكن</button>
            <div id="tokenResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 اختبار 1: طلب رقم SO التالي</h3>
            <p>اختبار الـ GET request لـ /api/sales/next-so</p>
            <button onclick="testNextSO()">طلب رقم SO جديد</button>
            <div id="nextSOResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 اختبار 2: طلب عدة أرقام SO</h3>
            <p>اختبار التسلسل عبر طلبات متعددة</p>
            <button onclick="testMultipleRequests()">طلب 3 أرقام SO</button>
            <div id="multipleResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 اختبار 3: التحقق من التسلسل</h3>
            <p>التحقق من أن الأرقام متسلسلة بشكل صحيح</p>
            <button onclick="testSequence()">اختبار التسلسل</button>
            <div id="sequenceResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let authToken = '';

        function saveToken() {
            const tokenInput = document.getElementById('tokenInput');
            authToken = tokenInput.value.trim();
            const resultDiv = document.getElementById('tokenResult');
            
            if (authToken) {
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ تم حفظ التوكن بنجاح';
                resultDiv.style.display = 'block';
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ يرجى إدخال التوكن';
                resultDiv.style.display = 'block';
            }
        }

        function getAuthHeaders() {
            if (!authToken) {
                throw new Error('التوكن غير محفوظ. يرجى إدخال التوكن أولاً.');
            }
            
            return {
                'Authorization': authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            };
        }

        async function testNextSO() {
            const resultDiv = document.getElementById('nextSOResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري الطلب...';

            try {
                const response = await fetch('/api/sales/next-so', {
                    method: 'GET',
                    headers: getAuthHeaders()
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ <strong>نجح الطلب!</strong><br>
                        📋 Status: ${response.status}<br>
                        🔢 SO Number: ${data.soNumber}<br>
                        📄 Response: ${JSON.stringify(data, null, 2)}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        ❌ <strong>فشل الطلب!</strong><br>
                        📋 Status: ${response.status}<br>
                        📄 Error: ${JSON.stringify(data, null, 2)}
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ <strong>خطأ في الشبكة!</strong><br>
                    📄 Error: ${error.message}
                `;
            }
        }

        async function testMultipleRequests() {
            const resultDiv = document.getElementById('multipleResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري طلب 3 أرقام SO...';

            try {
                const requests = [];
                for (let i = 0; i < 3; i++) {
                    requests.push(
                        fetch('/api/sales/next-so', {
                            method: 'GET',
                            headers: getAuthHeaders()
                        })
                    );
                }

                const responses = await Promise.all(requests);
                const results = [];

                for (let i = 0; i < responses.length; i++) {
                    const response = responses[i];
                    const data = await response.json();
                    results.push({
                        status: response.status,
                        data: data
                    });
                }

                let html = '📊 <strong>نتائج الطلبات المتعددة:</strong><br>';
                results.forEach((result, index) => {
                    html += `
                        🔸 الطلب ${index + 1}: 
                        Status ${result.status} - 
                        SO: ${result.data.soNumber || result.data.error}<br>
                    `;
                });

                resultDiv.className = 'result success';
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ <strong>خطأ في الطلبات المتعددة!</strong><br>
                    📄 Error: ${error.message}
                `;
            }
        }

        async function testSequence() {
            const resultDiv = document.getElementById('sequenceResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '🔄 جاري اختبار التسلسل...';

            try {
                // طلب أول رقم
                const response1 = await fetch('/api/sales/next-so', {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                const data1 = await response1.json();

                // طلب ثاني رقم
                const response2 = await fetch('/api/sales/next-so', {
                    method: 'GET',
                    headers: getAuthHeaders()
                });
                const data2 = await response2.json();

                if (response1.ok && response2.ok) {
                    const num1 = parseInt(data1.soNumber.replace('SO-', ''));
                    const num2 = parseInt(data2.soNumber.replace('SO-', ''));
                    
                    const isSequential = (num2 - num1) === 1;
                    
                    resultDiv.className = isSequential ? 'result success' : 'result error';
                    resultDiv.innerHTML = `
                        ${isSequential ? '✅' : '❌'} <strong>اختبار التسلسل ${isSequential ? 'نجح' : 'فشل'}!</strong><br>
                        🔢 الرقم الأول: ${data1.soNumber} (${num1})<br>
                        🔢 الرقم الثاني: ${data2.soNumber} (${num2})<br>
                        📈 الفرق: ${num2 - num1} (يجب أن يكون 1)<br>
                        ${isSequential ? '🎉 التسلسل يعمل بشكل صحيح!' : '⚠️ هناك مشكلة في التسلسل!'}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        ❌ <strong>فشل في الحصول على الأرقام!</strong><br>
                        📋 Response 1: ${response1.status}<br>
                        📋 Response 2: ${response2.status}
                    `;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    ❌ <strong>خطأ في اختبار التسلسل!</strong><br>
                    📄 Error: ${error.message}
                `;
            }
        }
    </script>
</body>
</html>
