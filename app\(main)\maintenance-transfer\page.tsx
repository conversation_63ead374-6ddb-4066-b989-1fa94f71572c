
'use client';

import { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import { DarkModeToggle } from './DarkModeToggle';
import './enhanced-styles.css';
import type {
  Device,
  SystemSettings,
  MaintenanceOrder,
  MaintenanceReceiptOrder,
  EmployeeRequestType,
  EmployeeRequestPriority,
  WarehouseTransfer,
  User,
  DeviceStatus,
} from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Send,
  FileText,
  Upload,
  Trash2,
  Printer,
  FileDown,
  FolderOpen,
  PlusCircle,
  Trash,
  MessageSquareQuote,
  X,
  PackageCheck,
  Save,
  ScanLine,
  ArrowLeft,
  ArrowLeftRight,
  FileSpreadsheet,
  FilePen,
  Eye,
  Wrench,
  Inbox,
  CheckCircle,
  Package
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import AttachmentsViewer from '@/components/AttachmentsViewer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ActionGuard } from '@/components/PermissionGuard';
import * as XLSX from 'xlsx';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';
// دالة تنسيق الأرقام
function formatNumber(value) {
  return new Intl.NumberFormat('ar-EG').format(value);
}

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// نوع بيانات المرفقات
interface AttachmentFile {
  originalName: string;
  fileName: string;
  filePath: string;
  size: number;
  type: string;
  uploadedAt: string;
}

const initialSendFormState = {
  referenceNumber: '',
  notes: '',
  date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM
  employeeName: '', // سيتم تعيينه تلقائياً من المستخدم الحالي
};

const initialRequestFormState = {
  requestType: 'تعديل' as EmployeeRequestType,
  priority: 'عادي' as EmployeeRequestPriority,
  notes: '',
  attachmentName: '',
};

type DetailsModalData = {
  title: string;
  items: Device[];
};

type MaintenanceOrdersModalData = {
  title: string;
  orders: any[];
};

type MaintenanceProgressModalData = {
  title: string;
  orders: any[];
};

type DeviceMaintenanceDetailsModalData = {
  title: string;
  devices: any[];
};

type MaintenanceOrdersModalData = {
  title: string;
  orders: any[];
};

type OrderProgressDetails = {
  order: MaintenanceOrder;
  completedItems: Device[];
  remainingItems: Device[];
  receipts: MaintenanceReceiptOrder[];
};


export default function MaintenanceTransferPage() {
  const store = useStore();
  const {
    devices,
    maintenanceOrders,
    maintenanceReceiptOrders,
    systemSettings,
    currentUser,
    users,
    warehouses,
    addEmployeeRequest,
    addMaintenanceOrder,
    updateMaintenanceOrder,
    deleteMaintenanceOrder,
    addMaintenanceReceiptOrder,
    updateMaintenanceReceiptOrder,
    deleteMaintenanceReceiptOrder,
    maintenanceHistory,
    acknowledgeMaintenanceLog,
    updateDeviceStatus,
    warehouseTransfers,
    completeWarehouseTransfer,
    checkMaintenanceOrderRelations,
    checkDeviceRelationsInMaintenance,
    getAuthHeader,
    addActivity,
    isLoading,
  } = store;
  const { toast } = useToast();

  // فحص الصلاحيات
  const canView = currentUser?.permissions?.maintenanceTransfer?.view ?? false;
  const canCreate = currentUser?.permissions?.maintenanceTransfer?.create ?? false;
  const canEdit = currentUser?.permissions?.maintenanceTransfer?.edit ?? false;
  const canDelete = currentUser?.permissions?.maintenanceTransfer?.delete ?? false;

  // منع الوصول للصفحة بدون صلاحية العرض
  if (!canView) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600">غير مصرح لك بالوصول</h2>
          <p className="text-gray-500 mt-2">ليس لديك صلاحية لعرض صفحة مناقلات الصيانة</p>
        </div>
      </div>
    );
  }

  // عرض مؤشر التحميل
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">جاري تحميل بيانات مناقلات الصيانة...</p>
        </div>
      </div>
    );
  }

  const [transferId, setTransferId] = useState('');
  const [formState, setFormState] = useState(initialSendFormState);
  const [itemsToSend, setItemsToSend] = useState<Device[]>([]);
  const [imeiInput, setImeiInput] = useState('');
  const [selectedMaintenanceEmployee, setSelectedMaintenanceEmployee] = useState<
    number | null
  >(null);
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<string>('');
  const [loadedOrder, setLoadedOrder] = useState<MaintenanceOrder | null>(
    null
    );
  const [isLoadOrderDialogOpen, setIsLoadOrderDialogOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<MaintenanceOrder | null>(
    null
    );
  const [receiptOrderToDelete, setReceiptOrderToDelete] = useState<MaintenanceReceiptOrder | null>(
    null
    );
  const [requestOrder, setRequestOrder] = useState<MaintenanceOrder | null>(
    null
    );
  const [requestFormData, setRequestFormData] = useState(
    initialRequestFormState
    );
  const [detailsModal, setDetailsModal] = useState<DetailsModalData | null>(null);
  const [maintenanceOrdersModal, setMaintenanceOrdersModal] = useState<MaintenanceOrdersModalData | null>(null);
  const [maintenanceProgressModal, setMaintenanceProgressModal] = useState<MaintenanceProgressModalData | null>(null);
  const [deviceMaintenanceDetailsModal, setDeviceMaintenanceDetailsModal] = useState<DeviceMaintenanceDetailsModalData | null>(null);
  const [orderProgressDetails, setOrderProgressDetails] =
    useState<OrderProgressDetails | null>(null);

  const [receivingTransfer, setReceivingTransfer] =
    useState<WarehouseTransfer | null>(null);
  const [scannedImeis, setScannedImeis] = useState<Set<string>>(new Set());
  const [receiveImeiInput, setReceiveImeiInput] = useState('');
  const [isCompletedTransfersDialogOpen, setIsCompletedTransfersDialogOpen] =
    useState(false);
  const [selectedReceiptOrder, setSelectedReceiptOrder] = useState<MaintenanceReceiptOrder | null>(null);
  const [isDraftsDialogOpen, setIsDraftsDialogOpen] = useState(false);
  const [loadedReceiptOrder, setLoadedReceiptOrder] =
    useState<MaintenanceReceiptOrder | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // متغيرات حالة وضع الإنشاء
  const [isCreatingSend, setIsCreatingSend] = useState(false);
  const [isCreatingReceive, setIsCreatingReceive] = useState(false);

  // متغيرات حالة نظام المسودات - الإرسال
  const [isSendDraft, setIsSendDraft] = useState(false);
  const [isSendDraftWarningOpen, setIsSendDraftWarningOpen] = useState(false);
  const [existingSendDraft, setExistingSendDraft] = useState<MaintenanceOrder | null>(null);

  // متغيرات حالة نظام المسودات - الاستلام
  const [isReceiveDraft, setIsReceiveDraft] = useState(false);
  const [isReceiveDraftWarningOpen, setIsReceiveDraftWarningOpen] = useState(false);
  const [existingReceiveDraft, setExistingReceiveDraft] = useState<MaintenanceReceiptOrder | null>(null);

  // مفاتيح localStorage للمسودات
  const SEND_DRAFT_KEY = 'maintenanceSendDraft';
  const RECEIVE_DRAFT_KEY = 'maintenanceReceiveDraft';

  // متغير لإعادة رسم المكون عند تغيير المسودات
  const [draftRefresh, setDraftRefresh] = useState(0);
  const [hasSendDraft, setHasSendDraft] = useState(false);
  const [hasReceiveDraft, setHasReceiveDraft] = useState(false);

  // متغيرات حالة المرفقات - الإرسال
  const [sendAttachments, setSendAttachments] = useState<AttachmentFile[]>([]);
  const [isSendAttachmentsModalOpen, setIsSendAttachmentsModalOpen] = useState(false);
  const sendAttachmentsInputRef = useRef<HTMLInputElement>(null);

  // متغيرات حالة المرفقات - الاستلام
  const [receiveAttachments, setReceiveAttachments] = useState<AttachmentFile[]>([]);
  const [isReceiveAttachmentsModalOpen, setIsReceiveAttachmentsModalOpen] = useState(false);
  const receiveAttachmentsInputRef = useRef<HTMLInputElement>(null);

  // تعيين المخزن الافتراضي
  useEffect(() => {
    if (warehouses.length > 0 && !selectedWarehouseId) {
      // اختيار المخزن الرئيسي كافتراضي إذا وُجد
      const mainWarehouse = warehouses.find((w) => w.type === 'رئيسي');
      if (mainWarehouse) {
        setSelectedWarehouseId(mainWarehouse.id.toString());
      } else {
        // اختيار أول مخزن متاح
        setSelectedWarehouseId(warehouses[0].id.toString());
      }
    }
  }, [warehouses, selectedWarehouseId]);

  // تعريف receiptFormState قبل استخدامه في useEffect
  const [receiptId, setReceiptId] = useState('');
  const [receiptFormState, setReceiptFormState] = useState({
    date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM
    referenceNumber: '',
    employeeName: currentUser?.name || '', // ربط المستخدم تلقائياً
  });
  // Temporary permissions (remove when auth system is implemented)
  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };


  // تعيين اسم المستخدم تلقائياً ومنع تعديله
  useEffect(() => {
    if (currentUser?.name && (!formState.employeeName || formState.employeeName === '')) {
      setFormState(prev => ({
        ...prev,
        employeeName: currentUser.name
      }));
    }

    // تحديث receiptFormState أيضاً
    if (currentUser?.name && (!receiptFormState.employeeName || receiptFormState.employeeName === '')) {
      setReceiptFormState(prev => ({
        ...prev,
        employeeName: currentUser.name
      }));
    }
  }, [currentUser, formState.employeeName, receiptFormState.employeeName]);

  // دالة تنسيق التاريخ والوقت
  // استخدم formatDateTime من date-utils بدلاً من هذه الدالة

  const sendFileInputRef = useRef<HTMLInputElement>(null);
  const requestAttachmentInputRef = useRef<HTMLInputElement>(null);
  const [itemsToReceive, setItemsToReceive] = useState<Device[]>([]);
  const receiveFileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Generate new receipt ID based on existing orders
    const maxReceiptNumber = maintenanceReceiptOrders.reduce((max, order) => {
      const orderNum = parseInt(order.receiptNumber.replace('REC-', ''));
      return !isNaN(orderNum) && orderNum > max ? orderNum : max;
    }, 0);
    
    const newReceiptId = `REC-${maxReceiptNumber + 1}`;
    setReceiptId(newReceiptId);
  }, [maintenanceReceiptOrders]);

  // نظام الترقيم التسلسلي المحسن - يحافظ على نفس الرقم عند الحفظ
  useEffect(() => {
    if (loadedOrder) {
      // عند تحميل أمر موجود، نحافظ على رقمه الأصلي
      setTransferId(loadedOrder.orderNumber);
    } else if (!transferId || transferId === '') {
      // فقط عند عدم وجود رقم أمر، نولد رقماً جديداً
      // استخدام نمط MTRANS- بدلاً من MAINT- لتجنب التضارب مع صفحة الصيانة
      const allOrders = [...maintenanceOrders];
      let maxOrderNumber = 0;

      // البحث عن أعلى رقم موجود بنمط MTRANS-
      allOrders.forEach(order => {
        const match = order.orderNumber.match(/MTRANS-(\d+)$/);
        if (match) {
          const orderNum = parseInt(match[1]);
          if (!isNaN(orderNum) && orderNum > maxOrderNumber) {
            maxOrderNumber = orderNum;
          }
        }
      });

      const newId = maxOrderNumber + 1;
      setTransferId(`MTRANS-${newId}`);
    }
  }, [maintenanceOrders, loadedOrder, transferId]);

  const resetPage = useCallback((type: 'send' | 'receive') => {
    if (type === 'send') {
      setFormState({
        ...initialSendFormState,
        employeeName: currentUser?.name || '', // تعيين اسم المستخدم تلقائياً
        date: new Date().toISOString().slice(0, 16) // تحديث التاريخ والوقت
      });
      setItemsToSend([]);
      setImeiInput('');
      setLoadedOrder(null);
      setSelectedMaintenanceEmployee(null);
      setIsCreatingSend(false); // ← العودة إلى وضع القراءة فقط
      setSendAttachments([]); // ← تنظيف المرفقات

      // إعادة تعيين المخزن إلى الافتراضي
      if (warehouses.length > 0) {
        const mainWarehouse = warehouses.find((w) => w.type === 'رئيسي');
        if (mainWarehouse) {
          setSelectedWarehouseId(mainWarehouse.id.toString());
        } else {
          setSelectedWarehouseId(warehouses[0].id.toString());
        }
      }

      // إعادة تعيين رقم الأمر لتوليد رقم جديد فقط عند الحاجة
      setTransferId(''); // سيؤدي هذا إلى تشغيل useEffect لتوليد رقم جديد
    } else {
      setItemsToReceive([]);
      setReceiptFormState({
        date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM
        referenceNumber: '',
        employeeName: currentUser?.name || '', // تعيين اسم المستخدم تلقائياً
      });
      setLoadedReceiptOrder(null);
      setIsCreatingReceive(false); // ← العودة إلى وضع القراءة فقط
      setReceiveAttachments([]); // ← تنظيف المرفقات

      // إعادة تعيين رقم الإيصال لتوليد رقم جديد فقط عند الحاجة
      setReceiptId(''); // سيؤدي هذا إلى تشغيل useEffect لتوليد رقم جديد

      // إعادة تعيين المخزن إلى الافتراضي
      if (warehouses.length > 0) {
        const mainWarehouse = warehouses.find((w) => w.type === 'رئيسي');
        if (mainWarehouse) {
          setSelectedWarehouseId(mainWarehouse.id.toString());
        } else {
          setSelectedWarehouseId(warehouses[0].id.toString());
        }
      }

      // Generate new receipt ID based on existing orders
      const maxReceiptNumber = maintenanceReceiptOrders.reduce((max, order) => {
        const orderNum = parseInt(order.receiptNumber.replace('REC-', ''));
        return !isNaN(orderNum) && orderNum > max ? orderNum : max;
      }, 0);

      const newReceiptId = `REC-${maxReceiptNumber + 1}`;
      setReceiptId(newReceiptId);

      // تحديث التاريخ الافتراضي
      setReceiptFormState(prev => ({
        ...prev,
        date: new Date().toISOString().slice(0, 16)
      }));
    }
  }, [maintenanceOrders, maintenanceReceiptOrders, warehouses]);

  // دوال نظام المسودات - الإرسال
  const saveSendDraft = () => {
    if (itemsToSend.length === 0 && !formState.referenceNumber && !selectedMaintenanceEmployee) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لا توجد بيانات كافية للحفظ كمسودة.',
      });
      return;
    }

    const draftData = {
      formState,
      itemsToSend,
      selectedMaintenanceEmployee,
      selectedWarehouseId,
      transferId,
      sendAttachments,
      timestamp: new Date(),
    };

    try {
      localStorage.setItem(SEND_DRAFT_KEY, JSON.stringify(draftData));
      setIsSendDraft(true);
      setDraftRefresh(prev => prev + 1); // إعادة رسم المكون
      toast({
        title: 'تم حفظ المسودة',
        description: `تم حفظ بيانات أمر الإرسال رقم ${transferId} كمسودة بنجاح.`,
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ المسودة. يرجى المحاولة مرة أخرى.',
      });
    }
  };

  const checkExistingSendDrafts = () => {
    try {
      const draftData = localStorage.getItem(SEND_DRAFT_KEY);
      if (draftData) {
        const draft = JSON.parse(draftData);
        const draftOrder: MaintenanceOrder = {
          id: 0,
          orderNumber: draft.transferId || 'جديد',
          referenceNumber: draft.formState?.referenceNumber || '',
          date: draft.formState?.date || new Date(),
          employeeName: currentUser?.name || '',
          maintenanceEmployeeId: draft.selectedMaintenanceEmployee,
          maintenanceEmployeeName: users.find(u => u.id === draft.selectedMaintenanceEmployee)?.name,
          items: draft.itemsToSend || [],
          notes: draft.formState?.notes || '',
          status: 'wip',
          source: 'warehouse',
          createdAt: draft.timestamp,
        };
        setExistingSendDraft(draftOrder);
        setIsSendDraftWarningOpen(true);
        return true;
      }
    } catch (error) {
      localStorage.removeItem(SEND_DRAFT_KEY);
    }
    return false;
  };

  const continueSendDraft = () => {
    try {
      const draftData = localStorage.getItem(SEND_DRAFT_KEY);
      if (draftData) {
        const draft = JSON.parse(draftData);

        setFormState(draft.formState || initialSendFormState);
        setItemsToSend(draft.itemsToSend || []);
        setSelectedMaintenanceEmployee(draft.selectedMaintenanceEmployee || null);
        setSelectedWarehouseId(draft.selectedWarehouseId || selectedWarehouseId);
        setTransferId(draft.transferId || transferId);
        setSendAttachments(draft.sendAttachments || []);

        setIsCreatingSend(true);
        setIsSendDraftWarningOpen(false);
        setIsSendDraft(true);

        toast({
          title: 'تم تحميل المسودة',
          description: `تم تحميل مسودة أمر الإرسال رقم ${draft.transferId || 'جديد'}`,
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في تحميل المسودة',
        description: 'حدث خطأ أثناء تحميل المسودة',
      });
    }
  };

  const deleteSendDraftAndProceed = () => {
    try {
      localStorage.removeItem(SEND_DRAFT_KEY);
      setIsSendDraftWarningOpen(false);
      setExistingSendDraft(null);
      setIsSendDraft(false);
      setDraftRefresh(prev => prev + 1); // إعادة رسم المكون
      proceedWithNewSendOrder();

      toast({
        title: 'تم حذف المسودة',
        description: 'تم حذف المسودة السابقة وبدء أمر جديد',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في حذف المسودة',
        description: 'حدث خطأ أثناء حذف المسودة',
      });
    }
  };

  const proceedWithNewSendOrder = () => {
    resetPage('send');
    setIsCreatingSend(true);
    toast({
      title: 'وضع الإنشاء',
      description: 'تم تفعيل وضع إنشاء أمر إرسال جديد للصيانة',
    });
  };

  // دوال بدء وضع الإنشاء
  const startCreatingSend = () => {
    if (checkExistingSendDrafts()) {
      return; // إيقاف العملية وعرض التنبيه
    }
    proceedWithNewSendOrder();
  };

  // دوال نظام المسودات - الاستلام
  const saveReceiveDraft = () => {
    if (itemsToReceive.length === 0 && !receiptFormState.referenceNumber) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لا توجد بيانات كافية للحفظ كمسودة.',
      });
      return;
    }

    const draftData = {
      receiptFormState,
      itemsToReceive,
      receiptId,
      receiveAttachments,
      selectedWarehouseId,
      timestamp: new Date(),
    };

    try {
      localStorage.setItem(RECEIVE_DRAFT_KEY, JSON.stringify(draftData));
      setIsReceiveDraft(true);
      setDraftRefresh(prev => prev + 1); // إعادة رسم المكون
      toast({
        title: 'تم حفظ المسودة',
        description: `تم حفظ بيانات أمر الاستلام رقم ${receiptId} كمسودة بنجاح.`,
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ المسودة. يرجى المحاولة مرة أخرى.',
      });
    }
  };

  const checkExistingReceiveDrafts = () => {
    try {
      const draftData = localStorage.getItem(RECEIVE_DRAFT_KEY);
      if (draftData) {
        const draft = JSON.parse(draftData);
        const draftOrder: MaintenanceReceiptOrder = {
          id: 0,
          receiptNumber: draft.receiptId || 'جديد',
          referenceNumber: draft.receiptFormState?.referenceNumber || '',
          date: draft.receiptFormState?.date || new Date(),
          employeeName: currentUser?.name || '',
          items: draft.itemsToReceive || [],
          notes: '',
          createdAt: draft.timestamp,
        };
        setExistingReceiveDraft(draftOrder);
        setIsReceiveDraftWarningOpen(true);
        return true;
      }
    } catch (error) {
      localStorage.removeItem(RECEIVE_DRAFT_KEY);
    }
    return false;
  };

  const continueReceiveDraft = () => {
    try {
      const draftData = localStorage.getItem(RECEIVE_DRAFT_KEY);
      if (draftData) {
        const draft = JSON.parse(draftData);

        setReceiptFormState(draft.receiptFormState || {
          date: new Date().toISOString().slice(0, 16),
          referenceNumber: '',
        });
        setItemsToReceive(draft.itemsToReceive || []);
        setReceiptId(draft.receiptId || receiptId);
        setReceiveAttachments(draft.receiveAttachments || []);
        setSelectedWarehouseId(draft.selectedWarehouseId || selectedWarehouseId);

        setIsCreatingReceive(true);
        setIsReceiveDraftWarningOpen(false);
        setIsReceiveDraft(true);

        toast({
          title: 'تم تحميل المسودة',
          description: `تم تحميل مسودة أمر الاستلام رقم ${draft.receiptId || 'جديد'}`,
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في تحميل المسودة',
        description: 'حدث خطأ أثناء تحميل المسودة',
      });
    }
  };

  const deleteReceiveDraftAndProceed = () => {
    try {
      localStorage.removeItem(RECEIVE_DRAFT_KEY);
      setIsReceiveDraftWarningOpen(false);
      setExistingReceiveDraft(null);
      setIsReceiveDraft(false);
      setDraftRefresh(prev => prev + 1); // إعادة رسم المكون
      proceedWithNewReceiveOrder();

      toast({
        title: 'تم حذف المسودة',
        description: 'تم حذف المسودة السابقة وبدء أمر جديد',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في حذف المسودة',
        description: 'حدث خطأ أثناء حذف المسودة',
      });
    }
  };

  const proceedWithNewReceiveOrder = () => {
    resetPage('receive');
    setIsCreatingReceive(true);
    toast({
      title: 'وضع الإنشاء',
      description: 'تم تفعيل وضع إنشاء أمر استلام جديد من الصيانة',
    });
  };

  const startCreatingReceive = () => {
    if (checkExistingReceiveDrafts()) {
      return; // إيقاف العملية وعرض التنبيه
    }
    proceedWithNewReceiveOrder();
  };

  // دوال رفع الملفات - الإرسال
  const handleSendFileUpload = async (files: FileList) => {
    try {
      const formData = new FormData();
      Array.from(files).forEach(file => formData.append('files', file));
      formData.append('section', 'maintenance');

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: getAuthHeader(),
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setSendAttachments(prev => [...prev, ...result.files]);
        toast({
          title: 'تم إرفاق الملفات',
          description: result.message,
        });
      } else {
        toast({
          title: 'خطأ في رفع الملفات',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ في رفع الملفات',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    }
  };

  // دوال رفع الملفات - الاستلام
  const handleReceiveFileUpload = async (files: FileList) => {
    try {
      const formData = new FormData();
      Array.from(files).forEach(file => formData.append('files', file));
      formData.append('section', 'maintenance');

      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: getAuthHeader(),
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setReceiveAttachments(prev => [...prev, ...result.files]);
        toast({
          title: 'تم إرفاق الملفات',
          description: result.message,
        });
      } else {
        toast({
          title: 'خطأ في رفع الملفات',
          description: result.error,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ في رفع الملفات',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    }
  };

  // دالة تحميل المرفقات من قاعدة البيانات
  const loadSendAttachments = (order: MaintenanceOrder) => {
    if (order.attachmentName) {
      const fileNames = order.attachmentName.split(';').filter(name => name.trim());
      const convertedAttachments: AttachmentFile[] = fileNames.map(fileName => ({
        originalName: fileName,
        fileName: fileName,
        filePath: `/attachments/maintenance/${fileName}`,
        size: 0,
        type: 'application/octet-stream',
        uploadedAt: order.createdAt || new Date()
      }));
      setSendAttachments(convertedAttachments);
    } else {
      setSendAttachments([]);
    }
  };

  const loadReceiveAttachments = (order: MaintenanceReceiptOrder) => {
    if (order.attachmentName) {
      const fileNames = order.attachmentName.split(';').filter(name => name.trim());
      const convertedAttachments: AttachmentFile[] = fileNames.map(fileName => ({
        originalName: fileName,
        fileName: fileName,
        filePath: `/attachments/maintenance/${fileName}`,
        size: 0,
        type: 'application/octet-stream',
        uploadedAt: order.createdAt || new Date()
      }));
      setReceiveAttachments(convertedAttachments);
    } else {
      setReceiveAttachments([]);
    }
  };

  const handleSaveTransfer = useCallback(async () => {
    // التحقق من البيانات الأساسية
    if (itemsToSend.length === 0) {
      toast({
        variant: 'destructive',
        title: 'لم يتم تحديد أجهزة',
        description: 'يرجى إضافة جهاز واحد على الأقل لإرساله.',
      });
      return;
    }

    if (!selectedMaintenanceEmployee) {
      toast({
        variant: 'destructive',
        title: 'لم يتم تحديد موظف الصيانة',
        description: 'يرجى اختيار موظف الصيانة المسؤول عن هذه الأجهزة.',
      });
      return;
    }

    // التحقق من عدم تكرار رقم الأمر (فقط للأوامر الجديدة)
    if (!loadedOrder) {
      const existingOrder = maintenanceOrders.find(order => order.orderNumber === transferId);
      if (existingOrder) {
        toast({
          variant: 'destructive',
          title: 'رقم أمر مكرر',
          description: `رقم الأمر ${transferId} موجود بالفعل. يرجى المحاولة مرة أخرى.`,
        });
        // إعادة توليد رقم جديد بنمط MTRANS-
        setTransferId('');
        return;
      }
    }

    // التحقق من حالة الأجهزة - السماح بإعادة الإرسال إذا كان الجهاز في المخزن
    const devicesInActiveMaintenanceStatus = itemsToSend.filter(device => {
      // فحص حالة الجهاز الحالية - منع الإرسال فقط إذا كان في حالة صيانة نشطة
      const activeMaintenanceStatuses = [
        'بانتظار استلام في الصيانة',
        'قيد الإصلاح',
        'بانتظار تسليم من الصيانة'
      ];
      return activeMaintenanceStatuses.includes(device.status);
    });

    if (devicesInActiveMaintenanceStatus.length > 0) {
      toast({
        variant: 'destructive',
        title: 'أجهزة في حالة صيانة نشطة',
        description: `الأجهزة التالية في حالة صيانة نشطة ولا يمكن إعادة إرسالها: ${devicesInActiveMaintenanceStatus.map(d => `${d.id} (${d.status})`).join(', ')}`,
      });
      return;
    }

    const maintenanceEmployee = selectedMaintenanceEmployee
      ? users.find((user) => user.id === selectedMaintenanceEmployee)
      : null;

    // Update device status to "awaiting receipt in maintenance" 
    // Only update status for devices that are currently in warehouse
    itemsToSend.forEach(device => {
      // Only update status if device is currently in warehouse or returned from maintenance
      if (device.status === 'متاح للبيع' || device.status === 'تحتاج صيانة' || device.status === 'معيب' || device.status === 'تالف') {
        updateDeviceStatus(device.id, 'بانتظار استلام في الصيانة');
      }
    });

    const selectedWarehouse = warehouses.find(w => w.id.toString() === selectedWarehouseId);
    const warehouseInfo = selectedWarehouse ? `المخزن: ${selectedWarehouse.name}` : '';
    const notesWithWarehouse = [warehouseInfo, formState.notes].filter(Boolean).join(' | ');

    const orderData = {
      orderNumber: transferId, // الحفاظ على نفس رقم الأمر
      referenceNumber: formState.referenceNumber,
      date: new Date(formState.date),
      employeeName: formState.employeeName || currentUser?.name || 'غير معروف', // استخدام اسم المستخدم من النموذج
      maintenanceEmployeeId: selectedMaintenanceEmployee ?? undefined,
      maintenanceEmployeeName: maintenanceEmployee?.name || 'غير معروف',
      items: itemsToSend,
      notes: notesWithWarehouse,
      attachmentName: sendAttachments.map(file => file.fileName).join(';'),
      status: 'wip' as const,
      source: 'warehouse' as const,
    };

    if (loadedOrder) {
      // Update existing order
      updateMaintenanceOrder({ ...orderData, id: loadedOrder.id });

      // تسجيل النشاط
      addActivity({
        type: 'maintenance',
        description: `تم تحديث أمر إرسال الصيانة ${transferId} - ${itemsToSend.length} جهاز`,
      });

      toast({
        title: 'تم التحديث بنجاح',
        description: 'تم تحديث أمر إرسال الصيانة.',
      });
    } else {
      // Create new order
      try {
        await addMaintenanceOrder(orderData);

        // تسجيل النشاط
        addActivity({
          type: 'maintenance',
          description: `تم إرسال ${itemsToSend.length} أجهزة للصيانة - أمر رقم ${transferId}`,
        });

        toast({
          title: 'تم الإرسال بنجاح',
          description: `تم إرسال ${itemsToSend.length} أجهزة إلى قائمة انتظار الصيانة.`,
        });

        // تنظيف المسودة بعد الحفظ الناجح
        localStorage.removeItem(SEND_DRAFT_KEY);
        setIsSendDraft(false);
        setDraftRefresh(prev => prev + 1); // إعادة رسم المكون

        // إعادة تعيين الصفحة بعد تأخير قصير للسماح بتحديث البيانات
        setTimeout(() => {
          resetPage('send');
        }, 100);
      } catch (error) {
        console.error('Error creating maintenance order:', error);
        toast({
          variant: 'destructive',
          title: 'خطأ في الحفظ',
          description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ أمر الصيانة.',
        });
        return;
      }
    }
  }, [
    itemsToSend,
    selectedMaintenanceEmployee,
    users,
    currentUser,
    loadedOrder,
    addMaintenanceOrder,
    updateMaintenanceOrder,
    toast,
    updateDeviceStatus,
    resetPage,
    transferId,
    formState.referenceNumber,
    formState.date,
    formState.notes,
  ]);


  const devicesReturnedFromMaintenanceAwaitingReceipt = useMemo(() => {
    // الأجهزة العائدة من الصيانة وبانتظار الاستلام في المخزن
    return devices.filter((d) => d.status === 'بانتظار استلام في المخزن');
  }, [devices]);

  const devicesSentToMaintenanceAwaitingReceipt = useMemo(
    () => devices.filter((d) => d.status === 'بانتظار استلام في الصيانة'),
    [devices]
    );

  // الأجهزة المرسلة للمخزن (نفس الأجهزة العائدة من الصيانة لكن بعرض مختلف)
  const devicesSentToWarehouseAwaitingReceipt = useMemo(() => {
    // نفس الأجهزة العائدة من الصيانة لكن بمنظور "مرسلة للمخزن"
    return devices.filter((d) => d.status === 'بانتظار استلام في المخزن');
  }, [devices]);

  const devicesCurrentlyInMaintenance = useMemo(
    () => devices.filter((d) => d.status === 'قيد الإصلاح'),
    [devices]
    );
  
  const outgoingMaintenanceOrders = useMemo(() => {
    return maintenanceOrders.filter(order => order.source === 'warehouse')
      .map(order => {
        // التأكد من أن items هو array
        const items = Array.isArray(order.items) ? order.items : [];
        const allItemsReceived = items.every(item => item.status !== 'بانتظار استلام في الصيانة');
        return {
          ...order,
          items, // التأكد من أن items هو array
          statusText: allItemsReceived ? 'تم الاستلام' : 'بانتظار الاستلام'
        };
      })
      .sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [maintenanceOrders]);

  const orderProgressSummary = useMemo(() => {
    return outgoingMaintenanceOrders.map(order => {
      const orderItems = Array.isArray(order.items) ? order.items : [];
      const returnedDevices = maintenanceReceiptOrders
        .flatMap(receipt => Array.isArray(receipt.items) ? receipt.items.map(item => item.id) : [])
        .filter(deviceId => orderItems.some(orderItem => orderItem.id === deviceId));

      const returnedCount = new Set(returnedDevices).size;
      const remainingCount = orderItems.length - returnedCount;
      
      return {
        order,
        returnedCount,
        remainingCount,
        isCompleted: remainingCount === 0
      };
    });
  }, [outgoingMaintenanceOrders, maintenanceReceiptOrders]);

  // فحص وجود المسودات
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setHasSendDraft(!!localStorage.getItem(SEND_DRAFT_KEY));
      setHasReceiveDraft(!!localStorage.getItem(RECEIVE_DRAFT_KEY));
    }
  }, [draftRefresh, SEND_DRAFT_KEY, RECEIVE_DRAFT_KEY]);

  const handleAddItemToSend = () => {
    if (!imeiInput) return;
    const device = devices.find((d) => d.id === imeiInput);
    if (!device) {
      toast({
        title: 'جهاز غير موجود',
        description: 'هذا الرقم التسلسلي غير مسجل في النظام.',
        variant: 'destructive',
      });
      return;
    }
    if (itemsToSend.some((item) => item.id === imeiInput)) {
      toast({
        title: 'الجهاز مضاف بالفعل',
        description: 'هذا الجهاز موجود بالفعل في أمر النقل الحالي.',
        variant: 'destructive',
      });
      return;
    }

    // تحسين فحص حالات الجهاز - فحص شامل للحالات التي لا يمكن فيها إرسال الجهاز للصيانة
    if (device.status === 'بانتظار تسليم من الصيانة' ||
        device.status === 'بانتظار استلام في الصيانة' ||
        device.status === 'قيد الإصلاح') {

      let errorMessage = '';
      if (device.status === 'بانتظار استلام في الصيانة') {
        errorMessage = 'هذا الجهاز تم إرساله للصيانة وبانتظار الاستلام من قسم الصيانة.';
      } else if (device.status === 'قيد الإصلاح') {
        errorMessage = 'هذا الجهاز قيد الإصلاح حالياً في قسم الصيانة.';
      } else if (device.status === 'بانتظار تسليم من الصيانة') {
        errorMessage = 'هذا الجهاز تم إصلاحه وبانتظار التسليم من قسم الصيانة.';
      }

      toast({
        title: 'حالة الجهاز غير مناسبة',
        description: errorMessage,
        variant: 'destructive',
      });
      return;
    }

    // فحص إضافي للأجهزة العائدة من الصيانة وبانتظار الاستلام
    const isDeviceReturnedFromMaintenance = devicesReturnedFromMaintenanceAwaitingReceipt.some(d => d.id === device.id);
    if (isDeviceReturnedFromMaintenance) {
      toast({
        title: 'حالة الجهاز غير مناسبة',
        description: 'هذا الجهاز عائد من الصيانة وبانتظار الاستلام، لا يمكن إرساله مرة أخرى للصيانة.',
        variant: 'destructive',
      });
      return;
    }

    // قائمة الحالات المسموحة للإرسال إلى الصيانة
    const allowedStatuses: DeviceStatus[] = [
      'متاح للبيع',
      'بانتظار إرسال للصيانة',
      'تحتاج صيانة',
      'معيب',
      'تالف',
    ];
    if (!allowedStatuses.includes(device.status)) {
      toast({
        title: 'حالة الجهاز غير مناسبة',
        description: `لا يمكن إرسال هذا الجهاز للصيانة، حالته الحالية: ${device.status}`,
        variant: 'destructive',
      });
      return;
    }

    // إضافة الجهاز بعد اجتياز جميع الفحوصات
    setItemsToSend((prev) => [...prev, device]);
    setImeiInput('');
    toast({
      title: 'تمت الإضافة',
      description: `تمت إضافة جهاز ${device.model} للأمر.`,
    });
  };

  const handleSendFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    let invalidStatusCount = 0;
    let inDraftCount = 0;
    let inMaintenanceCount = 0; // عداد للأجهزة قيد الصيانة
    const newItems: Device[] = [];
    const existingDeviceIds = new Set(itemsToSend.map((item) => item.id));
    const allowedStatuses: DeviceStatus[] = [
      'متاح للبيع',
      'بانتظار إرسال للصيانة',
      'تحتاج صيانة',
      'معيب',
      'تالف',
    ];

    for (const imei of lines) {
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }
      const device = devices.find((d) => d.id === imei);
      if (!device) {
        invalidCount++;
        continue;
      }

      // فحص إضافي للحالات
      if (device.status === 'بانتظار تسليم من الصيانة' || 
          device.status === 'بانتظار استلام في الصيانة' || 
          device.status === 'قيد الإصلاح') {
        inMaintenanceCount++;
        continue;
      }

      // فحص إضافي للأجهزة العائدة من الصيانة وبانتظار الاستلام
      const isDeviceReturnedFromMaintenance = devicesReturnedFromMaintenanceAwaitingReceipt.some(d => d.id === device.id);
      if (isDeviceReturnedFromMaintenance) {
        inMaintenanceCount++;
        continue;
      }

      if (!allowedStatuses.includes(device.status)) {
        invalidStatusCount++;
        continue;
      }

      newItems.push(device);
      existingDeviceIds.add(imei);
      addedCount++;
    }
    if (newItems.length > 0) setItemsToSend((prev) => [...prev, ...newItems]);
    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${addedCount} جهازًا. تم تخطي ${invalidCount} جهازًا (غير صالح)، ${duplicateCount} جهازًا (مكرر)، ${invalidStatusCount} جهازًا (حالة غير مناسبة)، ${inDraftCount} جهازًا (في مسودة)، و ${inMaintenanceCount} جهازًا (قيد/بانتظار الصيانة).`,
    });
    if (event.target) event.target.value = '';
  };

  const handleRemoveItemToSend = (deviceId: string) => {
    if (loadedOrder) {
      const relationCheck = checkDeviceRelationsInMaintenance(deviceId, loadedOrder.id);

      if (relationCheck.hasRelations) {
        toast({
          variant: 'destructive',
          title: 'لا يمكن حذف الجهاز',
          description: `الجهاز ${deviceId} مستخدم في: ${relationCheck.relatedOperations.join(', ')}`,
        });
        return;
      }
    }

    setItemsToSend((prev) => prev.filter((item) => item.id !== deviceId));
  };

  const handleLoadOrder = (order: MaintenanceOrder) => {
    // تحميل بيانات الأمر أو المسودة في الواجهة
    setLoadedOrder(order);

    // ضمان استخدام نفس رقم الأمر الأصلي
    setTransferId(order.orderNumber);

    setFormState({
      referenceNumber: order.referenceNumber || '',
      notes: order.notes || '',
      date: (order.date ? new Date(order.date).toISOString().slice(0, 16) : new Date().toISOString().slice(0, 16)), // مطلوب لـ HTML input format
    });

    // تحميل الأجهزة من الأمر
    setItemsToSend(Array.isArray(order.items) ? order.items : []);

    // تحميل موظف الصيانة المسؤول (إن وجد)
    setSelectedMaintenanceEmployee(order.maintenanceEmployeeId || null);

    // تحميل المرفقات
    loadSendAttachments(order);

    // تعطيل وضع الإنشاء عند تحميل أمر موجود
    setIsCreatingSend(false);

    // إغلاق نافذة تحميل الأوامر
    setIsLoadOrderDialogOpen(false);
    setIsDraftsDialogOpen(false);

    // لا نحذف المسودة عند التحميل، فقط عند إكمال المسودة وتحويلها إلى أمر نشط
    // من خلال استدعاء handleSaveTransfer(false) لإكمال المسودة

    // إظهار رسالة توضيحية للمستخدم
    toast({
      title: 'تم تحميل الأمر',
      description: 'تم تحميل الأمر، يمكنك مراجعته وتعديله.',
    });
  };

  const handleDeleteOrder = () => {
    if (orderToDelete) {
      try {
        const relationCheck = checkMaintenanceOrderRelations(orderToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setOrderToDelete(null);
          return;
        }

        deleteMaintenanceOrder(orderToDelete.id);
        toast({
          variant: 'destructive',
          title: 'تم الحذف',
          description: `تم حذف أمر الصيانة ${orderToDelete.orderNumber}.`,
        });
        if (loadedOrder?.id === orderToDelete.id) {
          resetPage('send');
        }
        setOrderToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setOrderToDelete(null);
      }
    }
  };

  const handleDeleteReceiptOrder = () => {
    if (receiptOrderToDelete) {
      try {
        deleteMaintenanceReceiptOrder(receiptOrderToDelete.id);
        toast({
          variant: 'destructive',
          title: 'تم الحذف',
          description: `تم حذف أمر الاستلام ${receiptOrderToDelete.receiptNumber}.`,
        });
        if (loadedReceiptOrder?.id === receiptOrderToDelete.id) {
          resetPage('receive');
        }
        setReceiptOrderToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setReceiptOrderToDelete(null);
      }
    }
  };

  const handleSendRequest = () => {
    if (!requestOrder) return;
    addEmployeeRequest({
      relatedOrderType: 'maintenance',
      relatedOrderId: requestOrder.id,
      relatedOrderDisplayId: requestOrder.orderNumber,
      requestType: requestFormData.requestType,
      priority: requestFormData.priority,
      notes: requestFormData.notes,
      attachmentName: requestFormData.attachmentName,
    });
    toast({
      title: 'تم إرسال الطلب',
      description: 'تم إرسال ملاحظتك إلى الإدارة بنجاح.',
    });
    setRequestOrder(null);
  };

  const handleAddReceiptItem = () => {
    if (!receiveImeiInput) return;
    const device = devicesReturnedFromMaintenanceAwaitingReceipt.find(
      (d) => d.id === receiveImeiInput
    );
    if (!device) {
      toast({
        variant: 'destructive',
        title: 'جهاز غير صحيح',
        description: 'هذا الجهاز غير موجود في قائمة انتظار الاستلام.',
      });
      return;
    }
    if (itemsToReceive.some((item) => item.id === receiveImeiInput)) {
      toast({
        variant: 'destructive',
        title: 'مكرر',
        description: 'تم استلام هذا الجهاز بالفعل.',
      });
      return;
    }
    setItemsToReceive((prev) => [...prev, device]);
    setReceiveImeiInput('');
  };

  const handleReceiveFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    const newItems = lines
      .map((imei) =>
        devicesReturnedFromMaintenanceAwaitingReceipt.find(
          (d) => d.id === imei && !itemsToReceive.some((r) => r.id === imei),
        ),
      )
      .filter((d): d is Device => !!d);

    if (newItems.length > 0)
      setItemsToReceive((prev) => [...prev, ...newItems]);

    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${newItems.length} جهازًا. تم تخطي ${lines.length - newItems.length} جهازًا (غير صالح أو مكرر).`,
    });

    if (event.target) event.target.value = '';
  };

  const handleRemoveReceiptItem = (imei: string) => {
    setItemsToReceive((prev) => prev.filter((item) => item.id !== imei));
  };

  const handleSaveReceipt = async () => {
    // التحقق من البيانات الأساسية
    if (itemsToReceive.length === 0) {
      toast({ variant: 'destructive', title: 'لا توجد أجهزة للاستلام' });
      return;
    }

    // التحقق من عدم تكرار رقم الأمر (فقط للأوامر الجديدة)
    if (!loadedReceiptOrder) {
      const existingOrder = maintenanceReceiptOrders.find(order => order.receiptNumber === receiptId);
      if (existingOrder) {
        toast({
          variant: 'destructive',
          title: 'رقم أمر مكرر',
          description: `رقم الأمر ${receiptId} موجود بالفعل. يرجى المحاولة مرة أخرى.`,
        });
        // إعادة توليد رقم جديد
        setReceiptId('');
        return;
      }
    }

    if (loadedReceiptOrder) {
      const updatedOrderData = {
        ...loadedReceiptOrder,
        referenceNumber: receiptFormState.referenceNumber,
        date: new Date(receiptFormState.date),
        items: itemsToReceive,
      };
      updateMaintenanceReceiptOrder(updatedOrderData);

      // تسجيل النشاط
      addActivity({
        type: 'maintenance',
        description: `تم تحديث أمر استلام الصيانة ${loadedReceiptOrder.receiptNumber} - ${itemsToReceive.length} جهاز`,
      });

      toast({
        title: 'تم التحديث بنجاح',
        description: `تم تحديث أمر الاستلام ${loadedReceiptOrder.receiptNumber}.`,
      });
    } else {
      // Create the receipt order
      const receiptOrder: Omit<MaintenanceReceiptOrder, 'id'> = {
        receiptNumber: receiptId,
        referenceNumber: receiptFormState.referenceNumber,
        date: new Date(receiptFormState.date),
        employeeName: receiptFormState.employeeName || currentUser?.name || 'غير معروف',
        items: itemsToReceive,
        notes: '',
        attachmentName: receiveAttachments.map(file => file.fileName).join(';'),
      };

      // Save the receipt order
      try {
        await addMaintenanceReceiptOrder(receiptOrder);

        // Update device statuses and acknowledge maintenance logs
        itemsToReceive.forEach((device) => {
          acknowledgeMaintenanceLog(device.id);
          if (device.status === 'بانتظار استلام في المخزن') {
            updateDeviceStatus(device.id, 'متاح للبيع');
          }
        });

        // تسجيل النشاط
        addActivity({
          type: 'maintenance',
          description: `تم استلام ${itemsToReceive.length} أجهزة من الصيانة - أمر رقم ${receiptId}`,
        });

        toast({
          title: 'تم الاستلام وحفظ الأمر',
          description: `تم حفظ أمر الاستلام ${receiptId} وتم استلام ${itemsToReceive.length} أجهزة بنجاح.`,
        });

        // تنظيف المسودة بعد الحفظ الناجح
        localStorage.removeItem(RECEIVE_DRAFT_KEY);
        setIsReceiveDraft(false);
        setDraftRefresh(prev => prev + 1); // إعادة رسم المكون

        // إعادة تعيين الصفحة بعد تأخير قصير للسماح بتحديث البيانات
        setTimeout(() => {
          resetPage('receive');
        }, 100);
      } catch (error) {
        console.error('Error creating maintenance receipt order:', error);
        toast({
          variant: 'destructive',
          title: 'خطأ في الحفظ',
          description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ أمر الاستلام.',
        });
        return;
      }
    }
  };

  const handleEditReceiptOrder = (order: MaintenanceReceiptOrder) => {
    setLoadedReceiptOrder(order);
    setReceiptId(order.receiptNumber);
    setReceiptFormState({
      date: (order.date ? new Date(order.date).toISOString().slice(0, 16) : new Date().toISOString().slice(0, 16)), // مطلوب لـ HTML input format
      referenceNumber: order.referenceNumber || '',
      employeeName: order.employeeName || currentUser?.name || '',
    });
    // تحويل items إلى Device format
    // order.items قد يكون JSON string أو array بالفعل
    let itemsArray = [];
    if (order.items) {
      if (typeof order.items === 'string') {
        try {
          itemsArray = JSON.parse(order.items);
        } catch (e) {
          console.error('Error parsing items JSON:', e);
          itemsArray = [];
        }
      } else if (Array.isArray(order.items)) {
        itemsArray = order.items;
      }
    }

    const convertedItems = itemsArray.map((item: any, index: number) => ({
      id: item.deviceId || item.id || `temp-${index}`,
      model: item.model || '',
      status: 'بانتظار استلام في المخزن' as const,
      storage: '',
      price: 0,
      condition: 'جديد' as const,
      dateAdded: new Date(),
      warehouseId: selectedWarehouseId ? parseInt(selectedWarehouseId) : 1,
      fault: item.fault || '',
      result: item.result || '',
      damage: item.damage || '',
      notes: item.notes || ''
    }));
    setItemsToReceive(convertedItems);
    setIsCompletedTransfersDialogOpen(false);
    setActiveTab('receive-from-maintenance');
    setIsCreatingReceive(false); // تعطيل وضع الإنشاء عند تحميل أمر موجود

    // تحميل المرفقات
    loadReceiveAttachments(order);
    toast({
      title: 'تم تحميل أمر الاستلام',
      description: 'يمكنك الآن تعديل أمر الاستلام.',
    });
  };

  const handleScanReceiveItem = () => {
    if (!receivingTransfer || !receiveImeiInput) return;

    const transferItems = Array.isArray(receivingTransfer.items) ? receivingTransfer.items : [];
    const itemExistsInTransfer = transferItems.some(
      (item) => item.deviceId === receiveImeiInput
    );
    if (!itemExistsInTransfer) {
      toast({
        variant: 'destructive',
        title: 'جهاز غير صحيح',
        description: 'هذا الرقم التسلسلي لا ينتمي لأمر التحويل الحالي.',
      });
      setReceiveImeiInput('');
      return;
    }

    if (scannedImeis.has(receiveImeiInput)) {
      toast({
        variant: 'destructive',
        title: 'مكرر',
        description: 'تم استلام هذا الجهاز بالفعل.',
      });
      setReceiveImeiInput('');
      return;
    }

    setScannedImeis((prev) => new Set(prev).add(receiveImeiInput));
    setReceiveImeiInput('');
  };

  const handleCompleteReception = () => {
    if (!receivingTransfer) return;
    completeWarehouseTransfer(receivingTransfer.id);
    toast({
      title: 'اكتمل الاستلام',
      description: 'تم تحديث مواقع الأجهزة في المخزون.',
    });
    setReceivingTransfer(null);
    setScannedImeis(new Set());
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(settings.companyNameAr, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.addressAr, 190, 22, { align: 'right' });
      doc.text(settings.phone, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.footerTextAr) {
        doc.text(
          settings.footerTextAr,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' }
    );
      }
    };
    return { addHeader, addFooter };
  };

  const handlePrint = async (
    type: 'send' | 'receive',
    action: 'print' | 'download' = 'print',
  ) => {
    const items = type === 'send' ? itemsToSend : itemsToReceive;
    if (items.length === 0) return;

    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const title =
      type === 'send' ? 'أمر نقل إلى الصيانة' : 'إيصال استلام من الصيانة';
    const orderId = type === 'send' ? transferId : receiptId;

    doc.setFontSize(18);
    doc.text(title, 190, 45, { align: 'right' });

    doc.setFontSize(12);
    doc.text(`رقم الأمر: ${orderId}`, 190, 52, { align: 'right' });
    doc.text(
      `التاريخ: ${formatDateTime(type === 'send' ? formState.date : receiptFormState.date)}`,
      190,
      59,
      { align: 'right' }
    );

    autoTable(doc, {
      startY: 70,
      head: [['الحالة الحالية', 'الموديل', 'الرقم التسلسلي']],
      body: items.map((item) => [item.status, item.model, item.id]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      didDrawPage: addFooter,
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`${type}-order-${orderId}.pdf`);
    }
  };

  const handleExportExcel = async (type: 'send' | 'receive') => {
    const items = type === 'send' ? itemsToSend : itemsToReceive;
    if (items.length === 0) return;

    const sheetData = items.map((item) => ({
      'رقم الأمر': type === 'send' ? transferId : receiptId,
      التاريخ: type === 'send' ? formState.date : receiptFormState.date,
      'الرقم المرجعي':
        type === 'send'
          ? formState.referenceNumber
          : receiptFormState.referenceNumber,
      'الرقم التسلسلي': item.id,
      الموديل: item.model,
      الحالة: item.status,
    }));

    const worksheet = XLSX.utils.json_to_sheet(sheetData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير النقل');
    XLSX.writeFile(workbook, `Transfer-Report-${Date.now()}.xlsx`);
  };

  const handleViewDetails = (title: string, items: Device[]) => {
    setDetailsModal({ title, items });
  };

  const handleViewMaintenanceOrders = (title: string, orders: any[]) => {
    // تصفية الأوامر غير المكتملة فقط
    const incompleteOrders = orders.filter(order => {
      const details = getMaintenanceOrderDetails(order);
      return details.orderStatus !== 'تم الاستلام';
    });
    setMaintenanceOrdersModal({ title, orders: incompleteOrders });
  };

  // دالة لحساب تفاصيل أمر الصيانة
  const getMaintenanceOrderDetails = (order: any) => {
    const orderItems = Array.isArray(order.items) ? order.items : [];
    const totalDevices = orderItems.length;

    // الأجهزة التي تم استلامها في الصيانة (قيد الإصلاح أو مكتملة)
    const receivedDevices = orderItems.filter((item: any) => {
      const device = devices.find(d => d.id === item.id);
      return device && (device.status === 'قيد الإصلاح' || device.status === 'بانتظار تسليم من الصيانة');
    });

    // الأجهزة بانتظار الاستلام في الصيانة
    const pendingDevices = orderItems.filter((item: any) => {
      const device = devices.find(d => d.id === item.id);
      return device && device.status === 'بانتظار استلام في الصيانة';
    });

    // تحديد حالة الأمر
    let orderStatus = 'بانتظار استلام';
    if (receivedDevices.length === totalDevices) {
      orderStatus = 'تم الاستلام';
    } else if (receivedDevices.length > 0) {
      orderStatus = 'استلام جزئي';
    }

    return {
      totalDevices,
      receivedCount: receivedDevices.length,
      pendingCount: pendingDevices.length,
      orderStatus,
      receivedDevices,
      pendingDevices,
      warehouseName: order.warehouseName || 'غير محدد'
    };
  };

  const handleViewProgressSummary = (title: string, summaries: any[]) => {
    // تصفية الأوامر التي تم استلامها أو استلام جزء منها ولكن ليست مكتملة
    const incompleteReceivedOrders = summaries.filter(summary =>
      summary.returnedCount > 0 && !summary.isCompleted
    );
    setMaintenanceProgressModal({ title, orders: incompleteReceivedOrders });
  };

  // دالة لحساب تفاصيل متابعة الصيانة المحسنة
  const getMaintenanceProgressDetails = (orderSummary: any, storeData: any) => {
    const order = orderSummary.order;
    const orderItems = Array.isArray(order.items) ? order.items : [];
    const totalSent = orderItems.length;

    // الأجهزة المستلمة في الصيانة
    const receivedDevices = orderItems.filter((item: any) => {
      const device = devices.find(d => d.id === item.id);
      return device && (device.status === 'قيد الإصلاح' || device.status === 'بانتظار تسليم من الصيانة');
    });

    // الأجهزة قيد الإصلاح
    const underRepairDevices = orderItems.filter((item: any) => {
      const device = devices.find(d => d.id === item.id);
      return device && device.status === 'قيد الإصلاح';
    });

    // الأجهزة التي تم إصلاحها وتسليمها للمخزن
    const completedDevices = orderItems.filter((item: any) => {
      const device = devices.find(d => d.id === item.id);
      return device && device.status === 'بانتظار تسليم من الصيانة';
    });

    // الأجهزة المتبقية (لم يتم استلامها بعد)
    const remainingDevices = orderItems.filter((item: any) => {
      const device = devices.find(d => d.id === item.id);
      return device && device.status === 'بانتظار استلام في الصيانة';
    });

    // البحث عن تاريخ الاستلام من أوامر الاستلام
    const receiptOrder = storeData.maintenanceReceipts?.find(receipt =>
      receipt.originalOrderId === order.id
    );

    return {
      totalSent,
      receivedCount: receivedDevices.length,
      underRepairCount: underRepairDevices.length,
      completedCount: completedDevices.length,
      remainingCount: remainingDevices.length,
      receiptDate: receiptOrder?.date || null,
      receivingEmployee: receiptOrder?.employeeName || 'غير محدد',
      warehouseName: order.warehouseName || 'غير محدد',
      orderItems: orderItems.map((item: any) => {
        const device = devices.find(d => d.id === item.id);
        const deviceReceipt = storeData.maintenanceReceipts?.find(receipt =>
          Array.isArray(receipt.items) && receipt.items.some(rItem => rItem.id === item.id)
        );
        const deviceDelivery = storeData.maintenanceDeliveries?.find(delivery =>
          Array.isArray(delivery.items) && delivery.items.some(dItem => dItem.id === item.id)
        );

        return {
          ...item,
          currentStatus: device?.status || 'غير معروف',
          fault: item.fault || 'غير محدد',
          receiptDate: deviceReceipt?.date || null,
          receiptOrderNumber: deviceReceipt?.receiptNumber || null,
          deliveryOrderNumber: deviceDelivery?.deliveryNumber || null,
          deliveryDate: deviceDelivery?.date || null,
          deliveryStatus: deviceDelivery ? (deviceDelivery.repairStatus || 'تم الإصلاح') : null,
          receivingEmployee: deviceDelivery?.receivingEmployee || null,
          receivingWarehouse: deviceDelivery?.receivingWarehouse || null
        };
      })
    };
  };
  
  const handleViewOrderProgress = (summary: typeof orderProgressSummary[0]) => {
      const orderItems = Array.isArray(summary.order.items) ? summary.order.items : [];
      const completedItems = devices.filter(d =>
        orderItems.some(oi => oi.id === d.id) &&
        maintenanceReceiptOrders.some(ro => Array.isArray(ro.items) && ro.items.some(ri => ri.id === d.id))
      );
      const remainingItems = orderItems.filter(item =>
        !completedItems.some(ci => ci.id === item.id)
      );
      const receipts = maintenanceReceiptOrders.filter(ro =>
        Array.isArray(ro.items) && ro.items.some(item => orderItems.some(oi => oi.id === item.id))
      );

      setOrderProgressDetails({
          order: summary.order,
          completedItems,
          remainingItems,
          receipts
      });
  };

  if (receivingTransfer) {
    const transferItems = Array.isArray(receivingTransfer.items) ? receivingTransfer.items : [];
    const itemsToReceiveList = transferItems.filter(
      (item) => !scannedImeis.has(item.deviceId)
    );
    const itemsReceivedList = transferItems.filter((item) =>
      scannedImeis.has(item.deviceId)
    );

    return (
      <div className="space-y-4 animate-in fade-in-50">
        <Button
          variant="outline"
          onClick={() => {
            setReceivingTransfer(null);
            setScannedImeis(new Set());
          }}
        >
          <ArrowLeft className="ml-2 h-4 w-4" /> الرجوع إلى قائمة التحويلات
        </Button>
        <Card>
          <CardHeader>
            <CardTitle>
              استلام أمر التحويل: {receivingTransfer.transferNumber}
            </CardTitle>
            <CardDescription>
              من:{' '}
              <Badge variant="secondary">
                {receivingTransfer.fromWarehouseName}
              </Badge>{' '}
              إلى:{' '}
              <Badge variant="secondary">
                {receivingTransfer.toWarehouseName}
              </Badge>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex w-full max-w-sm items-center space-x-2 space-x-reverse">
              <Input
                placeholder="امسح أو أدخل الرقم التسلسلي..."
                value={receiveImeiInput}
                onChange={(e) => setReceiveImeiInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleScanReceiveItem()}
              />
              <ActionGuard pageKey="maintenanceTransfer" action="create">
                <Button onClick={handleScanReceiveItem}>
                  <ScanLine className="ml-2 h-4 w-4" /> استلام
                </Button>
              </ActionGuard>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>
                    أجهزة بانتظار الاستلام ({itemsToReceiveList.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الموديل</TableHead>
                        <TableHead>الرقم التسلسلي</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {itemsToReceiveList.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={2} className="text-center h-24">
                            تم استلام كل الأجهزة.
                          </TableCell>
                        </TableRow>
                      ) : (
                        itemsToReceiveList.map((item) => (
                          <TableRow key={item.deviceId}>
                            <TableCell>{item.model}</TableCell>
                            <TableCell dir="ltr">{item.deviceId}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>
                    أجهزة تم استلامها ({itemsReceivedList.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الموديل</TableHead>
                        <TableHead>الرقم التسلسلي</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {itemsReceivedList.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={2} className="text-center h-24">
                            لم يتم استلام أي جهاز بعد.
                          </TableCell>
                        </TableRow>
                      ) : (
                        itemsReceivedList.map((item) => (
                          <TableRow
                            key={item.deviceId}
                            className="bg-green-500/10"
                          >
                            <TableCell>{item.model}</TableCell>
                            <TableCell dir="ltr">{item.deviceId}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </CardContent>
          <CardFooter>
            <ActionGuard pageKey="maintenanceTransfer" action="edit">
              <Button
                onClick={handleCompleteReception}
                disabled={scannedImeis.size !== (Array.isArray(receivingTransfer.items) ? receivingTransfer.items.length : 0)}
              >
                تأكيد استلام الدفعة كاملة ({scannedImeis.size}/
                {Array.isArray(receivingTransfer.items) ? receivingTransfer.items.length : 0})
              </Button>
            </ActionGuard>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="maintenance-transfer-page">
        {/* رأس الصفحة المحسن */}
        <div className="header-card mb-6">
          <div className="p-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                  <ArrowLeftRight className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">
                    مناقلات قسم الصيانة المتقدمة
                  </h1>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                    إدارة شاملة لإرسال واستلام الأجهزة من وإلى قسم الصيانة (منظور المخزن)
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* زر الوضع الليلي */}
                <DarkModeToggle
                  size="md"
                  variant="outline"
                  className="enhanced-button"
                />
              </div>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="enhanced-tabs w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="enhanced-tab-trigger">
              <ArrowLeftRight className="ml-2 h-4 w-4" /> 📊 نظرة عامة
            </TabsTrigger>
            <TabsTrigger value="send-to-maintenance" className="enhanced-tab-trigger">
              <Send className="ml-2 h-4 w-4" /> 📤 إرسال إلى الصيانة
            </TabsTrigger>
            <TabsTrigger value="receive-from-maintenance" className="enhanced-tab-trigger">
              <PackageCheck className="ml-2 h-4 w-4" /> 📥 استلام من الصيانة
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="enhanced-transfer-card send-section animate-fade-in-up cursor-pointer" onClick={() => handleViewDetails('الأجهزة قيد الإصلاح', devicesCurrentlyInMaintenance)}>
                    <CardHeader className="bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-950/20 dark:to-blue-950/20 py-4">
                        <CardTitle className="text-lg text-cyan-800 dark:text-cyan-200 flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 to-blue-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                                <Wrench className="h-5 w-5" />
                            </div>
                            <div>
                                <div className="font-bold">أجهزة قيد الإصلاح</div>
                                <div className="text-xs text-cyan-600 dark:text-cyan-300 font-normal mt-1">الأجهزة قيد الصيانة حالياً</div>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                        <p className="text-3xl font-bold text-cyan-600 dark:text-cyan-400">{devicesCurrentlyInMaintenance.length}</p>
                    </CardContent>
                </Card>
                <Card className="enhanced-transfer-card receive-section animate-fade-in-up cursor-pointer" onClick={() => handleViewDetails('الأجهزة العائدة من الصيانة (بانتظار الاستلام)', devicesReturnedFromMaintenanceAwaitingReceipt)}>
                    <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-4">
                        <CardTitle className="text-lg text-green-800 dark:text-green-200 flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                                <Send className="h-5 w-5" />
                            </div>
                            <div>
                                <div className="font-bold">أجهزة عائدة من الصيانة</div>
                                <div className="text-xs text-green-600 dark:text-green-300 font-normal mt-1">بانتظار الاستلام من المخزن</div>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                        <p className="text-3xl font-bold text-green-600 dark:text-green-400">{devicesReturnedFromMaintenanceAwaitingReceipt.length}</p>
                    </CardContent>
                </Card>
                <Card className="enhanced-transfer-card progress-section animate-fade-in-up cursor-pointer" onClick={() => handleViewDetails('الأجهزة المرسلة للصيانة (بانتظار الاستلام)', devicesSentToMaintenanceAwaitingReceipt)}>
                    <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20 py-4">
                        <CardTitle className="text-lg text-orange-800 dark:text-orange-200 flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-amber-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                                <Inbox className="h-5 w-5" />
                            </div>
                            <div>
                                <div className="font-bold">أجهزة مرسلة للصيانة</div>
                                <div className="text-xs text-orange-600 dark:text-orange-300 font-normal mt-1">بانتظار الاستلام في الصيانة</div>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                        <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{devicesSentToMaintenanceAwaitingReceipt.length}</p>
                    </CardContent>
                </Card>
            </div>

            {/* قسم إضافي للأجهزة المرسلة للمخزن */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                <Card className="enhanced-transfer-card warehouse-section animate-fade-in-up cursor-pointer" onClick={() => handleViewDetails('الأجهزة المرسلة للمخزن (بانتظار الاستلام)', devicesSentToWarehouseAwaitingReceipt)}>
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 py-4">
                        <CardTitle className="text-lg text-blue-800 dark:text-blue-200 flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                                <Package className="h-5 w-5" />
                            </div>
                            <div>
                                <div className="font-bold">أجهزة مرسلة للمخزن</div>
                                <div className="text-xs text-blue-600 dark:text-blue-300 font-normal mt-1">بانتظار الاستلام في المخزن</div>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                        <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{devicesSentToWarehouseAwaitingReceipt.length}</p>
                    </CardContent>
                </Card>
            </div>

            {/* قسم إضافي لأوامر الصيانة */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                <Card className="enhanced-transfer-card history-section animate-fade-in-up cursor-pointer" onClick={() => handleViewMaintenanceOrders('أوامر الإرسال إلى الصيانة', outgoingMaintenanceOrders)}>
                    <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 py-4">
                        <CardTitle className="text-lg text-purple-800 dark:text-purple-200 flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                                📋
                            </div>
                            <div>
                                <div className="font-bold">أوامر الإرسال إلى الصيانة</div>
                                <div className="text-xs text-purple-600 dark:text-purple-300 font-normal mt-1">سجل جميع أوامر الإرسال للصيانة</div>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                        <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{outgoingMaintenanceOrders.length}</p>
                    </CardContent>
                </Card>

                <Card className="enhanced-transfer-card progress-section animate-fade-in-up cursor-pointer" onClick={() => handleViewProgressSummary('متابعة أوامر الصيانة', orderProgressSummary)}>
                    <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20 py-4">
                        <CardTitle className="text-lg text-orange-800 dark:text-orange-200 flex items-center">
                            <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-amber-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">
                                📊
                            </div>
                            <div>
                                <div className="font-bold">متابعة أوامر الصيانة</div>
                                <div className="text-xs text-orange-600 dark:text-orange-300 font-normal mt-1">تقدم أوامر الصيانة النشطة</div>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                        <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{orderProgressSummary.length}</p>
                    </CardContent>
                </Card>
            </div>
          </TabsContent>

          <TabsContent value="send-to-maintenance" className="space-y-4 pt-4">
            {!isCreatingSend && !loadedOrder && canCreate && (
              <div className="text-sm text-muted-foreground bg-blue-50 px-3 py-2 rounded-md border border-blue-200">
                💡 اضغط على "أمر جديد" لبدء إنشاء أمر إرسال جديد للصيانة
              </div>
            )}
            <div className="flex flex-wrap items-center justify-between gap-2">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-bold">
                  {loadedOrder
                    ? `تعديل أمر الصيانة ${loadedOrder.orderNumber}`
                    : 'أمر إرسال جديد للصيانة'}
                </h2>
                {isSendDraft && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-600 border-orange-300">
                    📝 مسودة محفوظة
                  </Badge>
                )}
              </div>
              <div className="flex gap-2">
                {canCreate && (
                  <Button onClick={startCreatingSend}>
                    <PlusCircle className="ml-2 h-4 w-4" /> أمر جديد
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => setIsLoadOrderDialogOpen(true)}
                >
                  <FolderOpen className="ml-2 h-4 w-4" /> تحميل أمر سابق
                </Button>
                {hasSendDraft && (
                  <Button
                    variant="outline"
                    onClick={continueSendDraft}
                    className="border-orange-300 text-orange-600 hover:bg-orange-50"
                  >
                    <FileDown className="ml-2 h-4 w-4" /> تحميل مسودة
                  </Button>
                )}
                <Button
                  variant="secondary"
                  onClick={() => setRequestOrder(loadedOrder)}
                  disabled={!loadedOrder}
                >
                  <MessageSquareQuote className="ml-2 h-4 w-4" /> إرسال ملاحظة
                </Button>
                {canDelete && (
                  <Button
                    variant="destructive"
                    onClick={() => setOrderToDelete(loadedOrder)}
                    disabled={!loadedOrder}
                  >
                    <Trash className="ml-2 h-4 w-4" /> حذف الأمر
                  </Button>
                )}
              </div>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>بيانات أمر النقل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center gap-1 w-auto">
                    <Label htmlFor="transferId" className="whitespace-nowrap">
                      رقم الأمر:
                    </Label>
                    <Input
                      id="transferId"
                      value={transferId}
                      disabled
                      className="w-32"
                    />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label
                      htmlFor="referenceNumber"
                      className="whitespace-nowrap"
                    >
                      رقم مرجعي:
                    </Label>
                    <Input
                      id="referenceNumber"
                      value={formState.referenceNumber}
                      onChange={(e) =>
                        setFormState((s) => ({
                          ...s,
                          referenceNumber: e.target.value,
                        }))
                      }
                      placeholder="اختياري"
                      className="w-32"
                      disabled={!isCreatingSend && !loadedOrder}
                    />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label
                      htmlFor="maintenanceEmployee"
                      className="whitespace-nowrap"
                    >
                      موظف الصيانة:
                    </Label>
                    <Select
                      value={selectedMaintenanceEmployee?.toString() || ''}
                      onValueChange={(value) =>
                        setSelectedMaintenanceEmployee(parseInt(value))
                      }
                      disabled={!isCreatingSend && !loadedOrder}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="اختر موظف الصيانة" />
                      </SelectTrigger>
                      <SelectContent>
                        {users
                          .filter((user: User) => user.role === 'صيانة')
                          .map((user: User) => (
                            <SelectItem key={user.id} value={user.id.toString()}>
                              {user.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label htmlFor="transferDate" className="whitespace-nowrap">
                      التاريخ:
                    </Label>
                    <Input
                      id="transferDate"
                      type="datetime-local"
                      value={formState.date}
                      onChange={(e) =>
                        setFormState((s) => ({ ...s, date: e.target.value }))
                      }
                      className="w-40 font-mono"
                      style={{ direction: 'ltr' }}
                      disabled={!isCreatingSend && !loadedOrder}
                    />
                  </div>

                  {/* قسم المرفقات */}
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap text-xs">المرفقات:</Label>
                    <input
                      type="file"
                      ref={sendAttachmentsInputRef}
                      className="hidden"
                      multiple
                      onChange={(e) => {
                        if (e.target.files) {
                          handleSendFileUpload(e.target.files);
                        }
                        if (e.target) e.target.value = '';
                      }}
                      disabled={!canCreate || (!isCreatingSend && !loadedOrder)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => sendAttachmentsInputRef.current?.click()}
                      className="border-indigo-300 text-indigo-600 hover:bg-indigo-50 h-8 px-2 text-xs"
                      disabled={!canCreate || (!isCreatingSend && !loadedOrder)}
                    >
                      <Upload className="ml-1 h-3 w-3" />
                      رفع ({sendAttachments.length})
                    </Button>
                    {sendAttachments.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsSendAttachmentsModalOpen(true)}
                        className="border-blue-300 text-blue-600 hover:bg-blue-50 h-8 w-8 p-0"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label htmlFor="transferUser" className="whitespace-nowrap">
                      الموظف:
                    </Label>
                    <Input
                      id="transferUser"
                      value={formState.employeeName || currentUser?.name || 'مدير النظام'}
                      disabled // منع تعديل اسم المستخدم
                      className="w-40 bg-gray-50"
                      title="اسم المستخدم محدد تلقائياً ولا يمكن تعديله"
                    />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label htmlFor="warehouse" className="whitespace-nowrap">
                      المخزن:
                    </Label>
                    <Select
                      value={selectedWarehouseId}
                      onValueChange={setSelectedWarehouseId}
                      disabled={!isCreatingSend && !loadedOrder}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="اختر المخزن" />
                      </SelectTrigger>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                            {warehouse.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>إضافة أجهزة للأمر</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Label htmlFor="imei-input" className="whitespace-nowrap">
                    إدخال IMEI:
                  </Label>
                  <Input
                    id="imei-input"
                    placeholder="يدوي أو مسح باركود..."
                    value={imeiInput}
                    onChange={(e) => setImeiInput(e.target.value)}
                    onKeyDown={(e) =>
                      e.key === 'Enter' && handleAddItemToSend()
                    }
                    className="w-48"
                    disabled={!canCreate || (!isCreatingSend && !loadedOrder)}
                  />
                  <ActionGuard pageKey="maintenanceTransfer" action="create">
                    <Button
                      onClick={handleAddItemToSend}
                      size="sm"
                      disabled={!canCreate || (!isCreatingSend && !loadedOrder)}
                    >
                      إضافة
                    </Button>
                  </ActionGuard>
                  <input
                    ref={sendFileInputRef}
                    type="file"
                    className="hidden"
                    onChange={handleSendFileImport}
                    accept=".txt"
                  />
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => sendFileInputRef.current?.click()}
                    title="استيراد من ملف"
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  الأجهزة في الأمر الحالي ({itemsToSend.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border max-h-96 overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الرقم التسلسلي</TableHead>
                        <TableHead>الموديل</TableHead>
                        <TableHead>الحالة الحالية</TableHead>
                        <TableHead>إجراء</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {itemsToSend.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={4} className="h-24 text-center">
                            لم يتم إضافة أجهزة بعد.
                          </TableCell>
                        </TableRow>
                      ) : (
                        itemsToSend.map((device) => (
                          <TableRow key={device.id}>
                            <TableCell dir="ltr">{device.id}</TableCell>
                            <TableCell>{device.model}</TableCell>
                            <TableCell>
                              <Badge variant="secondary">{device.status}</Badge>
                            </TableCell>
                            <TableCell>
                              <ActionGuard pageKey="maintenanceTransfer" action="delete">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() =>
                                    handleRemoveItemToSend(device.id)
                                  }
                                  disabled={!isCreatingSend && !loadedOrder}
                                >
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </ActionGuard>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex-wrap justify-start gap-2">
                {((canCreate && isCreatingSend) || (canEdit && loadedOrder)) && (
                  <Button
                    onClick={() => handleSaveTransfer()}
                    disabled={itemsToSend.length === 0}
                  >
                    <Send className="ml-2 h-4 w-4" />
                    {loadedOrder ? 'تحديث الأمر' : 'إرسال إلى الصيانة'} (
                    {itemsToSend.length})
                  </Button>
                )}

                {/* أزرار المسودات */}
                {canCreate && (isCreatingSend || loadedOrder) && (
                  <Button
                    variant="outline"
                    onClick={saveSendDraft}
                    disabled={itemsToSend.length === 0 && !formState.referenceNumber && !selectedMaintenanceEmployee}
                    className="border-orange-300 text-orange-600 hover:bg-orange-50"
                  >
                    <Save className="ml-2 h-4 w-4" /> حفظ مسودة
                  </Button>
                )}

                <Button
                  variant="outline"
                  onClick={() => handlePrint('send', 'print')}
                  disabled={itemsToSend.length === 0}
                >
                  <Printer className="ml-2 h-4 w-4" /> طباعة الأمر
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handlePrint('send', 'download')}
                  disabled={itemsToSend.length === 0}
                >
                  <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportExcel('send')}
                  disabled={itemsToSend.length === 0}
                >
                  <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent
            value="receive-from-maintenance"
            className="space-y-4 pt-4"
          >
            {!isCreatingReceive && !loadedReceiptOrder && canCreate && (
              <div className="text-sm text-muted-foreground bg-blue-50 px-3 py-2 rounded-md border border-blue-200">
                💡 اضغط على "إيصال جديد" لبدء إنشاء أمر استلام جديد من الصيانة
              </div>
            )}
            <div className="flex flex-wrap items-center justify-between gap-2">
              <div className="flex items-center gap-3">
                <h2 className="text-xl font-bold">إيصال استلام من الصيانة</h2>
                {isReceiveDraft && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-300">
                    📝 مسودة محفوظة
                  </Badge>
                )}
              </div>
              <div className="flex gap-2">
                {canCreate && (
                  <Button onClick={startCreatingReceive}>
                    <PlusCircle className="ml-2 h-4 w-4" /> إيصال جديد
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => setIsCompletedTransfersDialogOpen(true)}
                >
                  <FolderOpen className="ml-2 h-4 w-4" /> عرض الأوامر السابقة
                </Button>
                {canDelete && loadedReceiptOrder && (
                  <Button
                    variant="destructive"
                    onClick={() => setReceiptOrderToDelete(loadedReceiptOrder)}
                  >
                    <Trash className="ml-2 h-4 w-4" /> حذف الأمر
                  </Button>
                )}
                {hasReceiveDraft && (
                  <Button
                    variant="outline"
                    onClick={continueReceiveDraft}
                    className="border-amber-300 text-amber-600 hover:bg-amber-50"
                  >
                    <FileDown className="ml-2 h-4 w-4" /> تحميل مسودة
                  </Button>
                )}
              </div>
            </div>
            <Card>
              <CardHeader>
                <CardTitle>بيانات إيصال الاستلام</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap">رقم الإيصال:</Label>
                    <Input value={receiptId} disabled className="w-32" />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap">رقم مرجعي:</Label>
                    <Input
                      value={receiptFormState.referenceNumber}
                      onChange={(e) =>
                        setReceiptFormState((s) => ({
                          ...s,
                          referenceNumber: e.target.value,
                        }))
                      }
                      placeholder="اختياري"
                      className="w-32"
                      disabled={!isCreatingReceive && !loadedReceiptOrder}
                    />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap">التاريخ:</Label>
                    <Input
                      type="datetime-local"
                      value={receiptFormState.date}
                      onChange={(e) =>
                        setReceiptFormState((s) => ({
                          ...s,
                          date: e.target.value,
                        }))
                      }
                      className="w-40 font-mono"
                      style={{ direction: 'ltr' }}
                      disabled={!isCreatingReceive && !loadedReceiptOrder}
                    />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap">الموظف:</Label>
                    <Input
                      value={receiptFormState.employeeName || currentUser?.name || 'مدير النظام'}
                      disabled // منع تعديل اسم المستخدم
                      className="w-40 bg-gray-50"
                      title="اسم المستخدم محدد تلقائياً ولا يمكن تعديله"
                    />
                  </div>

                  {/* قسم المرفقات */}
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap text-xs">المرفقات:</Label>
                    <input
                      type="file"
                      ref={receiveAttachmentsInputRef}
                      className="hidden"
                      multiple
                      onChange={(e) => {
                        if (e.target.files) {
                          handleReceiveFileUpload(e.target.files);
                        }
                        if (e.target) e.target.value = '';
                      }}
                      disabled={!canCreate || (!isCreatingReceive && !loadedReceiptOrder)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => receiveAttachmentsInputRef.current?.click()}
                      className="border-indigo-300 text-indigo-600 hover:bg-indigo-50 h-8 px-2 text-xs"
                      disabled={!canCreate || (!isCreatingReceive && !loadedReceiptOrder)}
                    >
                      <Upload className="ml-1 h-3 w-3" />
                      رفع ({receiveAttachments.length})
                    </Button>
                    {receiveAttachments.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsReceiveAttachmentsModalOpen(true)}
                        className="border-blue-300 text-blue-600 hover:bg-blue-50 h-8 w-8 p-0"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap">الموظف المستلم:</Label>
                    <Input
                      value={currentUser?.name || ''}
                      disabled
                      className="w-40"
                    />
                  </div>
                  <div className="flex items-center gap-1 w-auto">
                    <Label className="whitespace-nowrap">المخزن:</Label>
                    <Select
                      value={selectedWarehouseId}
                      onValueChange={setSelectedWarehouseId}
                      disabled={!isCreatingReceive && !loadedReceiptOrder}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="اختر المخزن" />
                      </SelectTrigger>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                            {warehouse.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>إضافة الأجهزة المستلمة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Label
                    htmlFor="receive-imei-input"
                    className="whitespace-nowrap"
                  >
                    إدخال IMEI:
                  </Label>
                  <Input
                    id="receive-imei-input"
                    placeholder="امسح جهاز من قائمة الانتظار..."
                    value={receiveImeiInput}
                    onChange={(e) => setReceiveImeiInput(e.target.value)}
                    onKeyDown={(e) =>
                      e.key === 'Enter' && handleAddReceiptItem()
                    }
                    className="w-48"
                    disabled={!canCreate || (!isCreatingReceive && !loadedReceiptOrder)}
                  />
                  <Button
                    onClick={handleAddReceiptItem}
                    size="sm"
                    disabled={!canCreate || (!isCreatingReceive && !loadedReceiptOrder)}
                  >
                    إضافة
                  </Button>
                  <input
                    ref={receiveFileInputRef}
                    type="file"
                    className="hidden"
                    onChange={handleReceiveFileImport}
                    accept=".txt"
                  />
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => receiveFileInputRef.current?.click()}
                    title="استيراد من ملف"
                  >
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>
                  الأجهزة في الإيصال ({itemsToReceive.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الموديل</TableHead>
                      <TableHead>IMEI</TableHead>
                      <TableHead>حذف</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {itemsToReceive.length > 0 ? (
                      itemsToReceive.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.model}</TableCell>
                          <TableCell dir="ltr">{item.id}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveReceiptItem(item.id)}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={3} className="h-24 text-center">
                          لم يتم إضافة أجهزة.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <div className="flex flex-wrap justify-start gap-2">
              {((canCreate && isCreatingReceive) || (canEdit && loadedReceiptOrder)) && (
                <Button
                  onClick={handleSaveReceipt}
                  disabled={itemsToReceive.length === 0}
                >
                  <Save className="ml-2 h-4 w-4" />{' '}
                  {loadedReceiptOrder
                    ? 'تحديث الإيصال'
                    : 'حفظ الإيصال وتأكيد الاستلام'}
                </Button>
              )}

              {/* أزرار المسودات */}
              {canCreate && (isCreatingReceive || loadedReceiptOrder) && (
                <Button
                  variant="outline"
                  onClick={saveReceiveDraft}
                  disabled={itemsToReceive.length === 0 && !receiptFormState.referenceNumber}
                  className="border-amber-300 text-amber-600 hover:bg-amber-50"
                >
                  <Save className="ml-2 h-4 w-4" /> حفظ مسودة
                </Button>
              )}

              <Button
                variant="outline"
                onClick={() => handlePrint('receive', 'print')}
                disabled={itemsToReceive.length === 0}
              >
                <Printer className="ml-2 h-4 w-4" /> طباعة
              </Button>
              <Button
                variant="outline"
                onClick={() => handleExportExcel('receive')}
                disabled={itemsToReceive.length === 0}
              >
                <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
              </Button>
              <Button
                variant="destructive"
                onClick={() => resetPage('receive')}
              >
                <X className="ml-2 h-4 w-4" /> إلغاء
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Dialog
        open={isLoadOrderDialogOpen}
        onOpenChange={setIsLoadOrderDialogOpen}
      >
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>تحميل أمر صيانة سابق</DialogTitle>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الأمر</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الموظف</TableHead>
                  <TableHead>عدد الأجهزة</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {maintenanceOrders
                  .filter((o) => o.status === 'wip' && o.source === 'warehouse')
                  .map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>{order.orderNumber}</TableCell>
                      <TableCell>
                        {format(new Date(order.date), 'yyyy-MM-dd')}
                      </TableCell>
                      <TableCell>{order.employeeName}</TableCell>
                      <TableCell>{Array.isArray(order.items) ? order.items.length : 0}</TableCell>
                      <TableCell className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleLoadOrder(order)}
                        >
                          تحميل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>


      <AlertDialog
        open={!!orderToDelete}
        onOpenChange={() => setOrderToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيؤدي هذا الإجراء إلى حذف أمر الصيانة بشكل دائم. سيتم إعادة حالة
              الأجهزة الموجودة فيه إلى حالتها السابقة.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOrder}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* نافذة تأكيد حذف أمر الاستلام */}
      <AlertDialog
        open={!!receiptOrderToDelete}
        onOpenChange={() => setReceiptOrderToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من حذف أمر الاستلام؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيؤدي هذا الإجراء إلى حذف أمر الاستلام بشكل دائم. لا يمكن التراجع عن هذا الإجراء.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteReceiptOrder}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={!!requestOrder} onOpenChange={() => setRequestOrder(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إرسال ملاحظة للإدارة</DialogTitle>
            <DialogDescription>
              بخصوص أمر الصيانة رقم: {requestOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>نوع الطلب</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.requestType}
                  onValueChange={(v: EmployeeRequestType) =>
                    setRequestFormData((s) => ({ ...s, requestType: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تعديل">تعديل</SelectItem>
                    <SelectItem value="إعادة نظر">إعادة نظر</SelectItem>
                    <SelectItem value="حذف">حذف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>الأولوية</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.priority}
                  onValueChange={(v: EmployeeRequestPriority) =>
                    setRequestFormData((s) => ({ ...s, priority: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="عادي">عادي</SelectItem>
                    <SelectItem value="طاريء">طاريء</SelectItem>
                    <SelectItem value="طاريء جدا">طاريء جداً</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label>تفاصيل المشكلة / الملاحظة</Label>
              <Textarea
                placeholder="اشرح المشكلة أو الطلب بالتفصيل..."
                value={requestFormData.notes}
                onChange={(e) =>
                  setRequestFormData((s) => ({ ...s, notes: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label>إرفاق ملف (اختياري)</Label>
              <Input
                type="file"
                ref={requestAttachmentInputRef}
                onChange={(e) =>
                  setRequestFormData((s) => ({
                    ...s,
                    attachmentName: e.target.files?.[0]?.name || '',
                  }))
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSendRequest}>إرسال الطلب</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Dialog
        open={isCompletedTransfersDialogOpen}
        onOpenChange={setIsCompletedTransfersDialogOpen}
      >
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>أوامر الاستلامات السابقة من الصيانة</DialogTitle>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الأمر</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الموظف المستلم</TableHead>
                  <TableHead>عدد الأجهزة</TableHead>
                  <TableHead>الرقم المرجعي</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {maintenanceReceiptOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center h-24">
                      لا توجد أوامر استلام سابقة
                    </TableCell>
                  </TableRow>
                ) : (
                  maintenanceReceiptOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>{order.receiptNumber}</TableCell>
                      <TableCell>
                        {format(new Date(order.date), 'yyyy-MM-dd')}
                      </TableCell>
                      <TableCell>{order.employeeName}</TableCell>
                      <TableCell>{Array.isArray(order.items) ? order.items.length : 0}</TableCell>
                      <TableCell>{order.referenceNumber || '-'}</TableCell>
                      <TableCell className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedReceiptOrder(order)}
                        >
                          استعراض التفاصيل
                        </Button>
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => handleEditReceiptOrder(order)}
                          title="تعديل الأمر"
                        >
                          <FilePen className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نافذة تفاصيل أمر الاستلام */}
      <Dialog
        open={!!selectedReceiptOrder}
        onOpenChange={() => setSelectedReceiptOrder(null)}
      >
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              تفاصيل أمر الاستلام: {selectedReceiptOrder?.receiptNumber}
            </DialogTitle>
            <DialogDescription>
              تاريخ الأمر: {selectedReceiptOrder && format(new Date(selectedReceiptOrder.date), 'yyyy-MM-dd')}
              {selectedReceiptOrder?.referenceNumber && ` | الرقم المرجعي: ${selectedReceiptOrder.referenceNumber}`}
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الرقم التسلسلي</TableHead>
                  <TableHead>الموديل</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>السعة التخزينية</TableHead>
                  <TableHead>السعر</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(Array.isArray(selectedReceiptOrder?.items) ? selectedReceiptOrder.items : []).map((item) => (
                  <TableRow key={item.id}>
                    <TableCell dir="ltr">{item.id}</TableCell>
                    <TableCell>{item.model}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">{item.status}</Badge>
                    </TableCell>
                    <TableCell>{item.storage}</TableCell>
                    <TableCell>{item.price} ر.س</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
        <Dialog open={!!detailsModal} onOpenChange={() => setDetailsModal(null)}>
            <DialogContent className="max-w-2xl">
                <DialogHeader>
                    <DialogTitle>{detailsModal?.title}</DialogTitle>
                </DialogHeader>
                <div className="max-h-[60vh] overflow-y-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>الرقم التسلسلي</TableHead>
                                <TableHead>الموديل</TableHead>
                                <TableHead>الحالة</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {(Array.isArray(detailsModal?.items) ? detailsModal.items : []).map(device => (
                                <TableRow key={device.id}>
                                    <TableCell>{device.id}</TableCell>
                                    <TableCell>{device.model}</TableCell>
                                    <TableCell><Badge variant="outline">{device.status}</Badge></TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
                 <DialogFooter>
                    <Button variant="outline" onClick={() => setDetailsModal(null)}>إغلاق</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        {/* مودال أوامر الصيانة المحسن */}
        <Dialog open={!!maintenanceOrdersModal} onOpenChange={() => setMaintenanceOrdersModal(null)}>
            <DialogContent className="max-w-6xl">
                <DialogHeader>
                    <DialogTitle>{maintenanceOrdersModal?.title}</DialogTitle>
                </DialogHeader>
                <div className="max-h-[70vh] overflow-y-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>رقم الأمر</TableHead>
                                <TableHead>تاريخ الإرسال</TableHead>
                                <TableHead>الموظف المرسل</TableHead>
                                <TableHead>الموظف المرسل إليه</TableHead>
                                <TableHead>المخزن</TableHead>
                                <TableHead>عدد الأجهزة</TableHead>
                                <TableHead>تم الاستلام</TableHead>
                                <TableHead>بانتظار الاستلام</TableHead>
                                <TableHead>الحالة</TableHead>
                                <TableHead>إجراء</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {(Array.isArray(maintenanceOrdersModal?.orders) ? maintenanceOrdersModal.orders : []).map(order => {
                                const details = getMaintenanceOrderDetails(order);
                                return (
                                    <TableRow key={order.id}>
                                        <TableCell className="font-mono">{order.orderNumber}</TableCell>
                                        <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                                            {formatDateTime(order.date)}
                                        </TableCell>
                                        <TableCell>{order.employeeName}</TableCell>
                                        <TableCell>{order.maintenanceEmployeeName || 'غير محدد'}</TableCell>
                                        <TableCell>{details.warehouseName}</TableCell>
                                        <TableCell className="text-center">{details.totalDevices}</TableCell>
                                        <TableCell className="text-center text-green-600 font-semibold">
                                            {details.receivedCount}
                                        </TableCell>
                                        <TableCell className="text-center text-orange-600 font-semibold">
                                            {details.pendingCount}
                                        </TableCell>
                                        <TableCell>
                                            <Badge
                                                variant={details.orderStatus === 'تم الاستلام' ? 'default' :
                                                        details.orderStatus === 'استلام جزئي' ? 'secondary' : 'outline'}
                                                className={details.orderStatus === 'تم الاستلام' ? 'bg-green-100 text-green-800' :
                                                          details.orderStatus === 'استلام جزئي' ? 'bg-yellow-100 text-yellow-800' :
                                                          'bg-orange-100 text-orange-800'}
                                            >
                                                {details.orderStatus === 'تم الاستلام' ? '✅' :
                                                 details.orderStatus === 'استلام جزئي' ? '⏳' : '📋'} {details.orderStatus}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => {
                                                    const orderItems = Array.isArray(order.items) ? order.items : [];
                                                    setDetailsModal({
                                                        title: `تفاصيل الأجهزة - الأمر ${order.orderNumber}`,
                                                        items: orderItems
                                                    });
                                                }}
                                                className="text-xs"
                                            >
                                                👁️ عرض الأجهزة
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                        </TableBody>
                    </Table>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setMaintenanceOrdersModal(null)}>إغلاق</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        {/* مودال متابعة أوامر الصيانة المحسن */}
        <Dialog open={!!maintenanceProgressModal} onOpenChange={() => setMaintenanceProgressModal(null)}>
            <DialogContent className="max-w-7xl">
                <DialogHeader>
                    <DialogTitle>{maintenanceProgressModal?.title}</DialogTitle>
                </DialogHeader>
                <div className="max-h-[70vh] overflow-y-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>رقم الأمر</TableHead>
                                <TableHead>الموظف المستلم</TableHead>
                                <TableHead>المخزن</TableHead>
                                <TableHead>عدد الأجهزة المرسلة</TableHead>
                                <TableHead>عدد الأجهزة المستلمة</TableHead>
                                <TableHead>قيد الإصلاح</TableHead>
                                <TableHead>تم الإصلاح</TableHead>
                                <TableHead>المتبقية</TableHead>
                                <TableHead>تاريخ الاستلام</TableHead>
                                <TableHead>إجراء</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {(Array.isArray(maintenanceProgressModal?.orders) ? maintenanceProgressModal.orders : []).map(orderSummary => {
                                const details = getMaintenanceProgressDetails(orderSummary, store);
                                return (
                                    <TableRow key={orderSummary.order.id}>
                                        <TableCell className="font-mono">{orderSummary.order.orderNumber}</TableCell>
                                        <TableCell>{details.receivingEmployee}</TableCell>
                                        <TableCell>{details.warehouseName}</TableCell>
                                        <TableCell className="text-center font-semibold">{details.totalSent}</TableCell>
                                        <TableCell className="text-center text-blue-600 font-semibold">
                                            {details.receivedCount}
                                        </TableCell>
                                        <TableCell className="text-center text-orange-600 font-semibold">
                                            {details.underRepairCount}
                                        </TableCell>
                                        <TableCell className="text-center text-green-600 font-semibold">
                                            {details.completedCount}
                                        </TableCell>
                                        <TableCell className="text-center text-red-600 font-semibold">
                                            {details.remainingCount}
                                        </TableCell>
                                        <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                                            {details.receiptDate ? formatDateTime(details.receiptDate) : 'لم يتم الاستلام'}
                                        </TableCell>
                                        <TableCell>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => {
                                                    setDeviceMaintenanceDetailsModal({
                                                        title: `تفاصيل الأجهزة - الأمر ${orderSummary.order.orderNumber}`,
                                                        devices: details.orderItems
                                                    });
                                                }}
                                                className="text-xs"
                                            >
                                                👁️ عرض التفاصيل
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                        </TableBody>
                    </Table>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setMaintenanceProgressModal(null)}>إغلاق</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        {/* مودال تفاصيل الأجهزة في الصيانة */}
        <Dialog open={!!deviceMaintenanceDetailsModal} onOpenChange={() => setDeviceMaintenanceDetailsModal(null)}>
            <DialogContent className="max-w-7xl">
                <DialogHeader>
                    <DialogTitle>{deviceMaintenanceDetailsModal?.title}</DialogTitle>
                </DialogHeader>
                <div className="max-h-[70vh] overflow-y-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>الرقم التسلسلي</TableHead>
                                <TableHead>الموديل</TableHead>
                                <TableHead>العطل</TableHead>
                                <TableHead>تاريخ الاستلام</TableHead>
                                <TableHead>رقم أمر الاستلام</TableHead>
                                <TableHead>الحالة</TableHead>
                                <TableHead>رقم أمر التسليم</TableHead>
                                <TableHead>حالة الجهاز عند التسليم</TableHead>
                                <TableHead>تاريخ التسليم</TableHead>
                                <TableHead>الموظف المستلم</TableHead>
                                <TableHead>المخزن المستلم</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {(Array.isArray(deviceMaintenanceDetailsModal?.devices) ? deviceMaintenanceDetailsModal.devices : []).map(device => (
                                <TableRow key={device.id}>
                                    <TableCell className="font-mono">{device.id}</TableCell>
                                    <TableCell>{device.model}</TableCell>
                                    <TableCell>{device.fault}</TableCell>
                                    <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                                        {device.receiptDate ? formatDateTime(device.receiptDate) : 'لم يتم الاستلام'}
                                    </TableCell>
                                    <TableCell className="font-mono">
                                        {device.receiptOrderNumber || 'غير محدد'}
                                    </TableCell>
                                    <TableCell>
                                        <Badge
                                            variant={device.currentStatus === 'قيد الإصلاح' ? 'secondary' :
                                                    device.currentStatus === 'بانتظار تسليم من الصيانة' ? 'default' : 'outline'}
                                            className={device.currentStatus === 'قيد الإصلاح' ? 'bg-orange-100 text-orange-800' :
                                                      device.currentStatus === 'بانتظار تسليم من الصيانة' ? 'bg-green-100 text-green-800' :
                                                      'bg-gray-100 text-gray-800'}
                                        >
                                            {device.currentStatus === 'قيد الإصلاح' ? '🔧' :
                                             device.currentStatus === 'بانتظار تسليم من الصيانة' ? '✅' : '⏳'} {device.currentStatus}
                                        </Badge>
                                    </TableCell>
                                    <TableCell className="font-mono">
                                        {device.deliveryOrderNumber || 'لم يتم التسليم'}
                                    </TableCell>
                                    <TableCell>
                                        {device.deliveryStatus ? (
                                            <Badge
                                                variant={device.deliveryStatus === 'تم الإصلاح' ? 'default' : 'destructive'}
                                                className={device.deliveryStatus === 'تم الإصلاح' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                                            >
                                                {device.deliveryStatus === 'تم الإصلاح' ? '✅' : '❌'} {device.deliveryStatus}
                                            </Badge>
                                        ) : (
                                            <span className="text-gray-500">لم يتم التسليم</span>
                                        )}
                                    </TableCell>
                                    <TableCell className="font-mono" style={{ direction: 'ltr' }}>
                                        {device.deliveryDate ? formatDateTime(device.deliveryDate) : 'لم يتم التسليم'}
                                    </TableCell>
                                    <TableCell>{device.receivingEmployee || 'غير محدد'}</TableCell>
                                    <TableCell>{device.receivingWarehouse || 'غير محدد'}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setDeviceMaintenanceDetailsModal(null)}>إغلاق</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        
        <Dialog open={!!orderProgressDetails} onOpenChange={() => setOrderProgressDetails(null)}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>تفاصيل تقدم الأمر: {orderProgressDetails?.order.orderNumber}</DialogTitle>
                </DialogHeader>
                <Tabs defaultValue="remaining">
                    <TabsList>
                        <TabsTrigger value="remaining">الأجهزة المتبقية ({orderProgressDetails?.remainingItems.length})</TabsTrigger>
                        <TabsTrigger value="completed">الأجهزة المكتملة ({orderProgressDetails?.completedItems.length})</TabsTrigger>
                        <TabsTrigger value="receipts">أوامر الاستلام ({orderProgressDetails?.receipts.length})</TabsTrigger>
                    </TabsList>
                    <TabsContent value="remaining">
                       <Table>
                           <TableHeader>
                               <TableRow>
                                   <TableHead>الرقم التسلسلي</TableHead>
                                   <TableHead>الموديل</TableHead>
                               </TableRow>
                           </TableHeader>
                           <TableBody>
                               {orderProgressDetails?.remainingItems.map(item => (
                                   <TableRow key={item.id}>
                                       <TableCell>{item.id}</TableCell>
                                       <TableCell>{item.model}</TableCell>
                                   </TableRow>
                               ))}
                           </TableBody>
                       </Table>
                    </TabsContent>
                    <TabsContent value="completed">
                        <Table>
                           <TableHeader>
                               <TableRow>
                                   <TableHead>الرقم التسلسلي</TableHead>
                                   <TableHead>الموديل</TableHead>
                                   <TableHead>تاريخ الاستلام</TableHead>
                               </TableRow>
                           </TableHeader>
                           <TableBody>
                               {orderProgressDetails?.completedItems.map(item => (
                                   <TableRow key={item.id}>
                                       <TableCell>{item.id}</TableCell>
                                       <TableCell>{item.model}</TableCell>
                                       <TableCell className="font-mono" style={{ direction: 'ltr' }}>{formatDateTime(item.dateAdded)}</TableCell>
                                   </TableRow>
                               ))}
                           </TableBody>
                       </Table>
                    </TabsContent>
                    <TabsContent value="receipts">
                         <Table>
                           <TableHeader>
                               <TableRow>
                                   <TableHead>رقم أمر الاستلام</TableHead>
                                   <TableHead>التاريخ</TableHead>
                                   <TableHead>عدد الأجهزة</TableHead>
                               </TableRow>
                           </TableHeader>
                           <TableBody>
                               {orderProgressDetails?.receipts.map(receipt => (
                                   <TableRow key={receipt.id}>
                                       <TableCell>{receipt.receiptNumber}</TableCell>
                                       <TableCell className="font-mono" style={{ direction: 'ltr' }}>{formatDateTime(receipt.date)}</TableCell>
                                       <TableCell>{Array.isArray(receipt.items) ? receipt.items.length : 0}</TableCell>
                                   </TableRow>
                               ))}
                           </TableBody>
                       </Table>
                    </TabsContent>
                </Tabs>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setOrderProgressDetails(null)}>إغلاق</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>

        {/* نافذة تنبيه مسودة الإرسال */}
        <AlertDialog open={isSendDraftWarningOpen} onOpenChange={setIsSendDraftWarningOpen}>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center">
                  ⚠️
                </div>
                مسودة أمر إرسال موجودة
              </AlertDialogTitle>
              <AlertDialogDescription className="text-right">
                يوجد أمر إرسال للصيانة غير مكتمل في المسودات:
                <br />
                <strong>رقم الأمر:</strong> {existingSendDraft?.orderNumber}
                <br />
                <strong>تاريخ الإنشاء:</strong> {existingSendDraft?.createdAt ? formatDateTime(existingSendDraft.createdAt) : ''}
                <br />
                <strong>عدد الأجهزة:</strong> {Array.isArray(existingSendDraft?.items) ? existingSendDraft.items.length : 0}
                <br />
                <strong>موظف الصيانة:</strong> {existingSendDraft?.maintenanceEmployeeName || 'غير محدد'}
                <br />
                <br />
                هل تريد استكمال المسودة الموجودة أم إنشاء أمر جديد؟
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex gap-2">
              <AlertDialogCancel>إلغاء</AlertDialogCancel>
              <AlertDialogAction onClick={deleteSendDraftAndProceed} className="bg-red-500 hover:bg-red-600">
                حذف المسودة وإنشاء جديد
              </AlertDialogAction>
              <AlertDialogAction onClick={continueSendDraft} className="bg-green-500 hover:bg-green-600">
                استكمال المسودة
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* نافذة تنبيه مسودة الاستلام */}
        <AlertDialog open={isReceiveDraftWarningOpen} onOpenChange={setIsReceiveDraftWarningOpen}>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center">
                  ⚠️
                </div>
                مسودة أمر استلام موجودة
              </AlertDialogTitle>
              <AlertDialogDescription className="text-right">
                يوجد أمر استلام من الصيانة غير مكتمل في المسودات:
                <br />
                <strong>رقم الإيصال:</strong> {existingReceiveDraft?.receiptNumber}
                <br />
                <strong>تاريخ الإنشاء:</strong> {existingReceiveDraft?.createdAt ? formatDateTime(existingReceiveDraft.createdAt) : ''}
                <br />
                <strong>عدد الأجهزة:</strong> {Array.isArray(existingReceiveDraft?.items) ? existingReceiveDraft.items.length : 0}
                <br />
                <br />
                هل تريد استكمال المسودة الموجودة أم إنشاء أمر جديد؟
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex gap-2">
              <AlertDialogCancel>إلغاء</AlertDialogCancel>
              <AlertDialogAction onClick={deleteReceiveDraftAndProceed} className="bg-red-500 hover:bg-red-600">
                حذف المسودة وإنشاء جديد
              </AlertDialogAction>
              <AlertDialogAction onClick={continueReceiveDraft} className="bg-green-500 hover:bg-green-600">
                استكمال المسودة
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* مكون عرض مرفقات الإرسال */}
        <AttachmentsViewer
          isOpen={isSendAttachmentsModalOpen}
          onClose={() => setIsSendAttachmentsModalOpen(false)}
          attachments={sendAttachments}
          onRemove={(fileName) => {
            setSendAttachments(prev => prev.filter(file => file.fileName !== fileName));
          }}
          canDelete={canCreate && (isCreatingSend || !!loadedOrder)}
          section="maintenance"
        />

        {/* مكون عرض مرفقات الاستلام */}
        <AttachmentsViewer
          isOpen={isReceiveAttachmentsModalOpen}
          onClose={() => setIsReceiveAttachmentsModalOpen(false)}
          attachments={receiveAttachments}
          onRemove={(fileName) => {
            setReceiveAttachments(prev => prev.filter(file => file.fileName !== fileName));
          }}
          canDelete={canCreate && (isCreatingReceive || !!loadedReceiptOrder)}
          section="maintenance"
        />

    </>
  );
}