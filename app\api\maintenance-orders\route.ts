import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع أوامر الصيانة مع ترتيب مختلف حسب النوع
    const orderBy = view === 'simple' 
      ? { id: 'asc' as const }
      : { id: 'desc' as const };

    const maintenanceOrders = await prisma.maintenanceOrder.findMany({
      include: { items: true },
      orderBy
    });

    return NextResponse.json(maintenanceOrders);
  } catch (error) {
    console.error('Failed to fetch maintenance orders:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('📝 Starting maintenance order creation...');
    
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      console.error('❌ Authorization failed:', authResult.error);
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    console.log('✅ Authorization successful:', authResult.user?.username);

    let newOrder;
    try {
      newOrder = await request.json();
      console.log('📦 Received order data:', JSON.stringify(newOrder, null, 2));
    } catch (jsonError) {
      console.error('❌ JSON parsing error:', jsonError);
      return NextResponse.json(
        { error: 'Invalid JSON data provided' },
        { status: 400 }
      );
    }

    // Basic validation
    if (!newOrder.employeeName && !authResult.user!.username) {
      return NextResponse.json(
        { error: 'Employee name is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      let orderNumber = newOrder.orderNumber;

      // إذا لم يتم توفير رقم أمر أو كان الرقم المرسل موجوداً بالفعل، نولد رقماً جديداً
      if (!orderNumber) {
        // تحديد نمط الترقيم بناءً على مصدر الأمر
        const orderPrefix = newOrder.source === 'warehouse' ? 'MTRANS-' : 'MAINT-';
        
        // إنشاء رقم تسلسلي
        const existingOrders = await tx.maintenanceOrder.findMany({
          select: { orderNumber: true },
          orderBy: { id: 'desc' }
        });

        let maxNumber = 0;
        existingOrders.forEach(order => {
          const match = order.orderNumber.match(new RegExp(`${orderPrefix.replace('-', '-')}(\\d+)$`));
          if (match) {
            const num = parseInt(match[1]);
            if (!isNaN(num) && num > maxNumber) {
              maxNumber = num;
            }
          }
        });

        orderNumber = `${orderPrefix}${maxNumber + 1}`;
      } else {
        // التحقق من وجود الرقم المرسل
        const existingOrder = await tx.maintenanceOrder.findUnique({
          where: { orderNumber }
        });

        if (existingOrder) {
          // إذا كان الرقم موجوداً، نولد رقماً جديداً بدلاً من إرجاع خطأ
          const orderPrefix = newOrder.source === 'warehouse' ? 'MTRANS-' : 'MAINT-';
          
          const existingOrders = await tx.maintenanceOrder.findMany({
            select: { orderNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxNumber = 0;
          existingOrders.forEach(order => {
            const match = order.orderNumber.match(new RegExp(`${orderPrefix.replace('-', '-')}(\\d+)$`));
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxNumber) {
                maxNumber = num;
              }
            }
          });

          orderNumber = `${orderPrefix}${maxNumber + 1}`;
        }
      }

      // التحقق من صحة البيانات قبل الإنشاء
      if (!newOrder.employeeName && !authResult.user?.username) {
        throw new Error('Employee name is required');
      }

      // التحضير لإنشاء الأمر (بدون حقل items لأنه علاقة منفصلة)
      const orderData = {
        orderNumber,
        referenceNumber: newOrder.referenceNumber || null,
        date: newOrder.date ? new Date(newOrder.date) : new Date(),
        employeeName: newOrder.employeeName || authResult.user!.username,
        maintenanceEmployeeName: newOrder.maintenanceEmployeeName || null,
        notes: newOrder.notes || null,
        status: newOrder.status || 'wip',
        source: newOrder.source || 'warehouse',
        attachmentName: newOrder.attachmentName || null,
      };

      console.log('📋 Order data to create:', JSON.stringify(orderData, null, 2));

      // Create the maintenance order in the database
      let order;
      try {
        order = await tx.maintenanceOrder.create({
          data: orderData
        });
        console.log('✅ Order created successfully:', order.id);
      } catch (dbError) {
        console.error('❌ Database error creating maintenance order:', dbError);
        throw new Error('Failed to create maintenance order in database: ' + (dbError instanceof Error ? dbError.message : 'Unknown database error'));
      }

      // Create maintenance order items بشكل صحيح
      if (newOrder.items && Array.isArray(newOrder.items)) {
        console.log('📦 Creating maintenance order items...');
        for (const item of newOrder.items) {
          try {
            await tx.maintenanceOrderItem.create({
              data: {
                maintenanceOrderId: order.id,
                deviceId: item.deviceId || item.id || '',
                model: item.model || '',
                fault: item.fault || null,
                notes: item.notes || null
              }
            });
            console.log(`✅ Created item for device: ${item.deviceId || item.id}`);
          } catch (itemError) {
            console.error(`❌ Failed to create item for device ${item.deviceId || item.id}:`, itemError);
            throw new Error(`Failed to create maintenance order item: ${itemError instanceof Error ? itemError.message : 'Unknown error'}`);
          }
        }
      }

      // Update device statuses if items are provided
      if (newOrder.items && Array.isArray(newOrder.items)) {
        console.log('🔄 Updating device statuses...');
        for (const item of newOrder.items) {
          if (item.deviceId || item.id) {
            const deviceId = item.deviceId || item.id;
            try {
              const device = await tx.device.findUnique({
                where: { id: deviceId }
              });

              if (device) {
                await tx.device.update({
                  where: { id: deviceId },
                  data: { status: 'بانتظار استلام في الصيانة' }
                });
                console.log(`✅ Updated device ${deviceId} status`);
              } else {
                console.warn(`⚠️ Device ${deviceId} not found for maintenance order ${order.orderNumber}`);
              }
            } catch (deviceError) {
              console.error(`❌ Failed to update device ${deviceId}:`, deviceError);
              // لا نريد إيقاف العملية بسبب خطأ في تحديث حالة الجهاز
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created maintenance order: ${order.orderNumber}`
      });

      // Return the created order with items from relation
      const orderWithItems = await tx.maintenanceOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems || order;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('❌ Failed to create maintenance order:', error);
    
    // تسجيل تفاصيل الخطأ
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // معالجة أنواع مختلفة من الأخطاء
    if (error instanceof Error) {
      if (error.message === 'Maintenance order with this number already exists') {
        return NextResponse.json({ 
          error: 'Maintenance order with this number already exists',
          details: 'Please try again with a different order number'
        }, { status: 400 });
      }
      
      if (error.message.includes('Employee name is required')) {
        return NextResponse.json({ 
          error: 'Employee name is required',
          details: 'Please provide employee name in the request'
        }, { status: 400 });
      }
      
      if (error.message.includes('Database error')) {
        return NextResponse.json({ 
          error: 'Database operation failed',
          details: error.message
        }, { status: 500 });
      }
    }

    return NextResponse.json({ 
      error: 'Failed to create maintenance order',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    if (!updatedOrder.id) {
      return NextResponse.json(
        { error: 'Maintenance order ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance order exists
      const existingOrder = await tx.maintenanceOrder.findUnique({
        where: { id: updatedOrder.id }
      });

      if (!existingOrder) {
        throw new Error('Maintenance order not found');
      }

      // Update the maintenance order (بدون حقل items)
      const order = await tx.maintenanceOrder.update({
        where: { id: updatedOrder.id },
        data: {
          orderNumber: updatedOrder.orderNumber || existingOrder.orderNumber,
          referenceNumber: updatedOrder.referenceNumber !== undefined ? updatedOrder.referenceNumber : existingOrder.referenceNumber,
          date: updatedOrder.date || existingOrder.date,
          employeeName: updatedOrder.employeeName || existingOrder.employeeName,
          maintenanceEmployeeName: updatedOrder.maintenanceEmployeeName !== undefined ? updatedOrder.maintenanceEmployeeName : existingOrder.maintenanceEmployeeName,
          notes: updatedOrder.notes !== undefined ? updatedOrder.notes : existingOrder.notes,
          status: updatedOrder.status || existingOrder.status,
          source: updatedOrder.source || existingOrder.source,
          attachmentName: updatedOrder.attachmentName !== undefined ? updatedOrder.attachmentName : existingOrder.attachmentName,
        }
      });

      // Update items if provided
      if (updatedOrder.items && Array.isArray(updatedOrder.items)) {
        // Delete existing items
        await tx.maintenanceOrderItem.deleteMany({
          where: { maintenanceOrderId: updatedOrder.id }
        });

        // Create new items
        for (const item of updatedOrder.items) {
          await tx.maintenanceOrderItem.create({
            data: {
              maintenanceOrderId: order.id,
              deviceId: item.deviceId || item.id || '',
              model: item.model || '',
              fault: item.fault || null,
              notes: item.notes || null
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated maintenance order: ${order.orderNumber}`
      });

      // Return order with items
      const orderWithItems = await tx.maintenanceOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update maintenance order:', error);

    if (error instanceof Error && error.message === 'Maintenance order not found') {
      return NextResponse.json({ error: 'Maintenance order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update maintenance order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Maintenance order ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if maintenance order exists
      const existingOrder = await tx.maintenanceOrder.findUnique({
        where: { id: parseInt(id) },
        include: { items: true }
      });

      if (!existingOrder) {
        throw new Error('Maintenance order not found');
      }

      // Update device statuses back to available using items from relation
      if (existingOrder.items && existingOrder.items.length > 0) {
        for (const item of existingOrder.items) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'متاح للبيع' }
              });
            }
          }
        }
      }
      // Delete the maintenance order
      await tx.maintenanceOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted maintenance order: ${existingOrder.orderNumber}`
      });

      return { message: 'Maintenance order deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete maintenance order:', error);

    if (error instanceof Error && error.message === 'Maintenance order not found') {
      return NextResponse.json({ error: 'Maintenance order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete maintenance order' }, { status: 500 });
  }
}
