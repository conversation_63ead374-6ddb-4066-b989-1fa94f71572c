async function testReturnsAPI() {
  try {
    console.log('🧪 Testing Returns API fix...');
    
    // Test simple view
    console.log('\n1. Testing simple view...');
    const simpleResponse = await fetch('http://localhost:9005/api/returns?view=simple');
    
    if (simpleResponse.ok) {
      const simpleData = await simpleResponse.json();
      console.log('✅ Simple view works!');
      console.log(`   Found ${simpleData.length} returns`);
      if (simpleData.length > 0) {
        console.log('   Sample return keys:', Object.keys(simpleData[0]));
      }
    } else {
      console.log('❌ Simple view failed:', simpleResponse.status, simpleResponse.statusText);
      const errorText = await simpleResponse.text();
      console.log('   Error:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run test if fetch is available (browser environment)
if (typeof fetch !== 'undefined') {
  testReturnsAPI();
} else {
  console.log('This test needs to be run in a browser environment or with fetch polyfill');
}
