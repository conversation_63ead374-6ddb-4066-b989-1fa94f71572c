'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  FileText,
  Sparkles,
  Plus,
  Edit,
  Trash2,
  Copy,
  Star,
  Zap,
  BookOpen
} from 'lucide-react';
import { ResponseTemplate, TemplateVariable, TEMPLATE_VARIABLES } from '@/lib/template-service';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface SmartTemplatesProps {
  requestType: string;
  priority: string;
  employeeName: string;
  requestNumber: string;
  adminName: string;
  onTemplateSelect: (content: string, title: string) => void;
  onCreateCustomTemplate?: (template: Omit<ResponseTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

export default function SmartTemplates({
  requestType,
  priority,
  employeeName,
  requestNumber,
  adminName,
  onTemplateSelect,
  onCreateCustomTemplate
}: SmartTemplatesProps) {
  const [templates, setTemplates] = useState<ResponseTemplate[]>([]);
  const [suggestedTemplates, setSuggestedTemplates] = useState<ResponseTemplate[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    category: 'custom' as const,
    title: '',
    content: ''
  });
  const { toast } = useToast();

  // جلب القوالب
  const fetchTemplates = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/response-templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data);
      }
    } catch (error) {
      console.error('خطأ في جلب القوالب:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // جلب القوالب المقترحة
  const fetchSuggestedTemplates = async () => {
    try {
      const response = await fetch(`/api/response-templates/suggest?requestType=${requestType}&priority=${priority}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestedTemplates(data);
      }
    } catch (error) {
      console.error('خطأ في جلب القوالب المقترحة:', error);
    }
  };

  // استبدال المتغيرات في القالب
  const replaceVariables = (content: string): string => {
    const variables = {
      employeeName,
      requestNumber,
      requestType,
      priority,
      adminName,
      currentDate: new Date(),
      currentTime: new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })
    };

    let result = content;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      result = result.replace(regex, value);
    });

    return result;
  };

  // معالجة اختيار القالب
  const handleTemplateSelect = async (template: ResponseTemplate) => {
    const processedContent = replaceVariables(template.content);
    const processedTitle = replaceVariables(template.title);
    
    onTemplateSelect(processedContent, processedTitle);

    // زيادة عداد الاستخدام
    try {
      await fetch(`/api/response-templates/${template.id}/use`, {
        method: 'POST'
      });
    } catch (error) {
      console.error('خطأ في تحديث عداد الاستخدام:', error);
    }

    toast({
      title: 'تم تطبيق القالب',
      description: `تم تطبيق قالب "${template.name}" بنجاح`
    });
  };

  // إنشاء قالب مخصص
  const handleCreateTemplate = async () => {
    if (!newTemplate.name || !newTemplate.title || !newTemplate.content) {
      toast({
        title: 'خطأ',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await fetch('/api/response-templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTemplate)
      });

      if (response.ok) {
        const createdTemplate = await response.json();
        setTemplates(prev => [...prev, createdTemplate]);
        setNewTemplate({ name: '', category: 'custom', title: '', content: '' });
        setShowCreateDialog(false);
        
        toast({
          title: 'تم إنشاء القالب',
          description: 'تم إنشاء القالب المخصص بنجاح'
        });

        if (onCreateCustomTemplate) {
          onCreateCustomTemplate(createdTemplate);
        }
      } else {
        throw new Error('فشل في إنشاء القالب');
      }
    } catch (error) {
      console.error('خطأ في إنشاء القالب:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في إنشاء القالب',
        variant: 'destructive'
      });
    }
  };

  // نسخ محتوى القالب
  const handleCopyTemplate = (content: string) => {
    const processedContent = replaceVariables(content);
    navigator.clipboard.writeText(processedContent);
    toast({
      title: 'تم النسخ',
      description: 'تم نسخ محتوى القالب إلى الحافظة'
    });
  };

  useEffect(() => {
    fetchTemplates();
    fetchSuggestedTemplates();
  }, [requestType, priority]);

  // فلترة القوالب
  const filteredTemplates = templates.filter(template => 
    selectedCategory === 'all' || template.category === selectedCategory
  );

  // أيقونة الفئة
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'approval':
        return <Star className="h-4 w-4 text-green-600" />;
      case 'rejection':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case 'clarification':
        return <BookOpen className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  // لون الفئة
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'approval':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejection':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'clarification':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-4">
      {/* القوالب المقترحة */}
      {suggestedTemplates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Sparkles className="h-5 w-5 text-yellow-600" />
              <span>قوالب مقترحة</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {suggestedTemplates.map((template) => (
                <div
                  key={template.id}
                  className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleTemplateSelect(template)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {getCategoryIcon(template.category)}
                      <span className="font-medium">{template.name}</span>
                      {template.isSystem && (
                        <Badge variant="secondary" className="text-xs">
                          نظام
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Zap className="h-3 w-3 text-yellow-500" />
                      <span className="text-xs text-gray-500">{template.usageCount}</span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {replaceVariables(template.content)}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* جميع القوالب */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <FileText className="h-5 w-5 text-primary" />
              <span>قوالب الردود</span>
            </CardTitle>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="اختر الفئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  <SelectItem value="approval">موافقة</SelectItem>
                  <SelectItem value="rejection">رفض</SelectItem>
                  <SelectItem value="clarification">توضيح</SelectItem>
                  <SelectItem value="custom">مخصص</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="h-4 w-4 ml-1" />
                إنشاء قالب
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-500">جاري التحميل...</p>
            </div>
          ) : filteredTemplates.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>لا توجد قوالب في هذه الفئة</p>
            </div>
          ) : (
            <div className="grid gap-3">
              {filteredTemplates.map((template) => (
                <div
                  key={template.id}
                  className="p-4 border rounded-lg hover:shadow-sm transition-shadow"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {getCategoryIcon(template.category)}
                      <span className="font-medium">{template.name}</span>
                      <Badge className={`text-xs ${getCategoryColor(template.category)}`}>
                        {template.category}
                      </Badge>
                      {template.isSystem && (
                        <Badge variant="secondary" className="text-xs">
                          نظام
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyTemplate(template.content)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleTemplateSelect(template)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <h4 className="font-medium text-sm mb-2">{replaceVariables(template.title)}</h4>
                  <p className="text-sm text-gray-600 whitespace-pre-wrap line-clamp-3">
                    {replaceVariables(template.content)}
                  </p>
                  <div className="flex items-center justify-between mt-3 text-xs text-gray-500">
                    <span>استخدم {template.usageCount} مرة</span>
                    <Button
                      size="sm"
                      onClick={() => handleTemplateSelect(template)}
                      className="h-7"
                    >
                      استخدام
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* dialog إنشاء قالب جديد */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>إنشاء قالب جديد</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="template-name">اسم القالب</Label>
              <Input
                id="template-name"
                value={newTemplate.name}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                placeholder="اسم القالب"
              />
            </div>
            <div>
              <Label htmlFor="template-category">الفئة</Label>
              <Select
                value={newTemplate.category}
                onValueChange={(value) => setNewTemplate(prev => ({ ...prev, category: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="approval">موافقة</SelectItem>
                  <SelectItem value="rejection">رفض</SelectItem>
                  <SelectItem value="clarification">توضيح</SelectItem>
                  <SelectItem value="custom">مخصص</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="template-title">عنوان الرد</Label>
              <Input
                id="template-title"
                value={newTemplate.title}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, title: e.target.value }))}
                placeholder="عنوان الرد"
              />
            </div>
            <div>
              <Label htmlFor="template-content">محتوى القالب</Label>
              <Textarea
                id="template-content"
                value={newTemplate.content}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, content: e.target.value }))}
                placeholder="محتوى القالب..."
                className="min-h-[100px]"
              />
            </div>
            <div className="text-xs text-gray-500">
              <p className="mb-1">المتغيرات المتاحة:</p>
              <div className="flex flex-wrap gap-1">
                {TEMPLATE_VARIABLES.map((variable) => (
                  <Badge key={variable.key} variant="outline" className="text-xs">
                    {`{{${variable.key}}}`}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              إلغاء
            </Button>
            <Button onClick={handleCreateTemplate}>
              إنشاء القالب
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
