const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkDevices() {
  try {
    console.log('🔍 Checking available devices...');
    
    const devices = await prisma.device.findMany({
      take: 10,
      orderBy: { id: 'asc' }
    });
    
    console.log(`\nFound ${devices.length} devices:`);
    devices.forEach((device, index) => {
      console.log(`${index + 1}. ID: ${device.id} | Model: ${device.model} | Status: ${device.status}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkDevices();
