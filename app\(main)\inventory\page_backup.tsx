'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { Device, DeviceStatus, SystemSettings } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Eye,
  Filter,
  ChevronsUpDown,
  Check,
  Printer,
  FileDown,
  FileSpreadsheet,
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

type ModelSummary = {
  model: string;
  total: number;
  available: number;
  maintenance: number;
  inRepair: number;
  sold: number;
  defective: number;
  damaged: number;
};

export default function InventoryPage() {
  const {
    devices,
    warehouses,
    suppliers,
    manufacturers,
    returns,
    systemSettings,
  } = useStore();
  const { toast } = useToast();

  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [detailsModel, setDetailsModel] = useState<ModelSummary | null>(null);
  
  // States for "View All" modal
  const [isAllDevicesModalOpen, setIsAllDevicesModalOpen] = useState(false);
  const [allDevicesSearchQuery, setAllDevicesSearchQuery] = useState('');
  const [allDevicesStatusFilter, setAllDevicesStatusFilter] = useState<string[]>([]);

  // Filtering states
  const [selectedModel, setSelectedModel] = useState('all');
  const [isModelSearchOpen, setIsModelSearchOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [warehouseFilter, setWarehouseFilter] = useState('');
  const [supplierFilter, setSupplierFilter] = useState('');
  const [manufacturerFilter, setManufacturerFilter] = useState('');
  const [returnedOnlyFilter, setReturnedOnlyFilter] = useState(false);
  const [replacedOnlyFilter, setReplacedOnlyFilter] = useState(false);

  const returnedDeviceIds = useMemo(
    () => new Set(returns.flatMap((r) => (Array.isArray(r.items) ? r.items : []).map((i) => i.deviceId)),
    [returns]
    );

  const manufacturerNames = useMemo(
    () =>
      manufacturers.reduce(
        (acc, m) => {
          acc[m.id] = m.name;
          return acc;
        },
        {} as { [key: number]: string },
      ),
    [manufacturers]
    );

  const modelOptions = useMemo(() => {
    return [...new Set(devices.map((d) => d.model))].sort();
  }, [devices]);

  const baseFilteredDevices = useMemo(() => {
    return devices.filter((device) => {
      if (statusFilter.length > 0 && !statusFilter.includes(device.status)) return false;
      if (warehouseFilter && device.warehouseId?.toString() !== warehouseFilter) return false;
      if (supplierFilter && device.supplierId?.toString() !== supplierFilter) return false;
      if (manufacturerFilter) {
        const manuName = manufacturerNames[parseInt(manufacturerFilter, 10)];
        if (
          !manuName ||
          !device.model.toLowerCase().startsWith(manuName.toLowerCase())
        ) return false;
      }
      if (returnedOnlyFilter && !returnedDeviceIds.has(device.id)) return false;
      if (replacedOnlyFilter && !device.replacementInfo) return false;
      return true;
    });
  }, [
    devices,
    statusFilter,
    warehouseFilter,
    supplierFilter,
    manufacturerFilter,
    returnedOnlyFilter,
    replacedOnlyFilter,
    returnedDeviceIds,
    manufacturerNames,
  ]);

  const summaryData = useMemo((): ModelSummary[] => {
    const summary = baseFilteredDevices.reduce(
      (acc, device) => {
        if (!acc[device.model]) {
          acc[device.model] = {
            model: device.model,
            total: 0,
            available: 0,
            maintenance: 0,
            inRepair: 0,
            sold: 0,
            defective: 0,
            damaged: 0,
          };
        }
        const modelSummary = acc[device.model];
        modelSummary.total++;
        switch (device.status) {
          case 'متاح للبيع':
            modelSummary.available++;
            break;
          case 'بانتظار إرسال للصيانة':
            modelSummary.maintenance++;
            break;
          case 'قيد الإصلاح':
            modelSummary.inRepair++;
            break;
          case 'مباع':
            modelSummary.sold++;
            break;
          case 'معيب':
            modelSummary.defective++;
            break;
          case 'تالف':
            modelSummary.damaged++;
            break;
        }
        return acc;
      },
      {} as Record<string, ModelSummary>
    );
    return Object.values(summary).sort((a, b) =>
      a.model.localeCompare(b.model)
    );
  }, [baseFilteredDevices]);

  const filteredSummaryData = useMemo(() => {
    if (selectedModel === 'all') {
      return summaryData;
    }
    return summaryData.filter((item) => item.model === selectedModel);
  }, [summaryData, selectedModel]);

  // Filtered devices for "View All" modal
  const filteredAllDevices = useMemo(() => {
    return devices.filter((device) => {
      // Search filter
      if (allDevicesSearchQuery) {
        const query = allDevicesSearchQuery.toLowerCase();
        if (!device.model.toLowerCase().includes(query) &&
            !device.serialNumber.toLowerCase().includes(query)) {
          return false;
        }
      }
      
      // Status filter
      if (allDevicesStatusFilter.length > 0) {
        if (!allDevicesStatusFilter.includes(device.status)) {
          return false;
        }
      }
      
      return true;
    });
  }, [devices, allDevicesSearchQuery, allDevicesStatusFilter]);

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter((prev) =>
      prev.includes(status)
        ? prev.filter((s) => s !== status)
        : [...prev, status]
    );
  };

  const handleAllDevicesStatusFilterChange = (status: string) => {
    setAllDevicesStatusFilter((prev) =>
      prev.includes(status)
        ? prev.filter((s) => s !== status)
        : [...prev, status]
    );
  };

  const clearFilters = () => {
    setSelectedModel('all');
    setStatusFilter([]);
    setWarehouseFilter('');
    setSupplierFilter('');
    setManufacturerFilter('');
    setReturnedOnlyFilter(false);
    setReplacedOnlyFilter(false);
  };

  const handleOpenDetailsModal = (modelSummary: ModelSummary) => {
    setDetailsModel(modelSummary);
    setIsDetailsModalOpen(true);
  };

  const handleOpenAllDevicesModal = () => {
    setIsAllDevicesModalOpen(true);
    setAllDevicesSearchQuery('');
    setAllDevicesStatusFilter([]);
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(settings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' }
    );
      }
    };
    return { addHeader, addFooter };
  };

  const handleExport = (format: 'pdf' | 'excel') => {
    if (filteredSummaryData.length === 0) {
      toast({ variant: 'destructive', title: 'لا توجد بيانات للتصدير' });
      return;
    }

    const title = `تقرير المخزون ${selectedModel === 'all' ? 'الإجمالي' : selectedModel}`;
    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
      addHeader();
      doc.setFontSize(18).text(title, 190, 45, { align: 'right' });

      const head = [
        [
          'تالف',
          'معيب',
          'مباع',
          'قيد الإصلاح',
          'صيانة',
          'المتاح',
          'الإجمالي',
          'الموديل',
        ],
      ];
      const body = filteredSummaryData.map((item) => [
        item.damaged,
        item.defective,
        item.sold,
        item.inRepair,
        item.maintenance,
        item.available,
        item.total,
        item.model,
      ]);

      autoTable(doc, {
        head: head,
        body: body,
        startY: 55,
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
        columnStyles: {
          0: { halign: 'center' },
          1: { halign: 'center' },
          2: { halign: 'center' },
          3: { halign: 'center' },
          4: { halign: 'center' },
          5: { halign: 'center' },
          6: { halign: 'center' },
        },
        didDrawPage: addFooter,
      });
      doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف
    } else {
      const xlsxData = filteredSummaryData.map((item) => ({
        الموديل: item.model,
        الإجمالي: item.total,
        المتاح: item.available,
        صيانة: item.maintenance,
        'قيد الإصلاح': item.inRepair,
        مباع: item.sold,
        معيب: item.defective,
        تالف: item.damaged,
      }));
      const worksheet = XLSX.utils.json_to_sheet(xlsxData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير المخزون');
      XLSX.writeFile(
        workbook,
        `inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف
    );
    }
  };

  const handleDetailsModalExport = (action: 'print' | 'download') => {
    if (!detailsModel) return;

    const doc = new jsPDF();
    doc.setR2L(true);
    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const title = `تقرير تفصيلي لموديل ${detailsModel.model}`;
    doc.setFontSize(18).text(title, 190, 45, { align: 'right' });

    const summaryBody = [
      ['الإجمالي', detailsModel.total],
      ['متاح للبيع', detailsModel.available],
      ['يحتاج صيانة', detailsModel.maintenance],
      ['قيد الإصلاح', detailsModel.inRepair],
      ['مباع', detailsModel.sold],
      ['معيب', detailsModel.defective],
      ['تالف', detailsModel.damaged],
    ];

    autoTable(doc, {
      startY: 55,
      head: [['الحالة', 'العدد']],
      body: summaryBody,
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      columnStyles: { 1: { halign: 'center' } },
      didDrawPage: addFooter,
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(
        `inventory_details_${detailsModel.model.replace(/\s+/g, '_')}.pdf`
    );
    }
  };

  const getStatusBadgeVariant = (status: DeviceStatus): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'متاح للبيع':
        return 'default';
      case 'مباع':
        return 'secondary';
      case 'معيب':
      case 'تالف':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">
          تقرير المخزون ({filteredSummaryData.length})
        </h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleOpenAllDevicesModal}>
            <Eye className="ml-2 h-4 w-4" /> عرض الكل
          </Button>
          <Button variant="outline" onClick={() => handleExport('pdf')}>
            <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
          </Button>
          <Button variant="outline" onClick={() => handleExport('excel')}>
            <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>تصفية متقدمة</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <Label>بحث بالموديل</Label>
            <Popover
              open={isModelSearchOpen}
              onOpenChange={setIsModelSearchOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isModelSearchOpen}
                  className="w-full justify-between"
                >
                  {selectedModel === 'all' ? 'كل الموديلات' : selectedModel}
                  <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                <Command>
                  <CommandInput placeholder="ابحث عن موديل..." />
                  <CommandList>
                    <CommandEmpty>لا يوجد موديل بهذا الاسم.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        key="all"
                        value="كل الموديلات"
                        onSelect={() => {
                          setSelectedModel('all');
                          setIsModelSearchOpen(false);
                        }}
                      >
                        <Check
                          className={cn(
                            'ml-2 h-4 w-4',
                            selectedModel === 'all'
                              ? 'opacity-100'
                              : 'opacity-0',
                          )}
                        />
                        كل الموديلات
                      </CommandItem>
                      {modelOptions.map((model) => (
                        <CommandItem
                          key={model}
                          value={model}
                          onSelect={() => {
                            setSelectedModel(model);
                            setIsModelSearchOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              'ml-2 h-4 w-4',
                              selectedModel === model
                                ? 'opacity-100'
                                : 'opacity-0',
                            )}
                          />
                          {model}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label>التقييم</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between text-right"
                >
                  <span>
                    {statusFilter.length > 0
                      ? `${statusFilter.length} تقييمات محددة`
                      : 'كل التقييمات'}
                  </span>
                  <Filter className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end">
                <DropdownMenuLabel>تصفية حسب التقييم</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {[
                  'متاح للبيع',
                  'تحتاج صيانة',
                  'قيد الإصلاح',
                  'مرسل للمخزن',
                  'مباع',
                  'معيب',
                  'تالف',
                ].map((status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={statusFilter.includes(status)}
                    onCheckedChange={() => handleStatusFilterChange(status)}
                    className="justify-end"
                  >
                    {status}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="space-y-2">
            <Label htmlFor="warehouse-filter">المخزن</Label>
            <Select
              dir="rtl"
              value={warehouseFilter}
              onValueChange={(value) =>
                setWarehouseFilter(value === 'all' ? '' : value)
              }
            >
              <SelectTrigger id="warehouse-filter">
                <SelectValue placeholder="كل المخازن" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل المخازن</SelectItem>
                {warehouses.map((w) => (
                  <SelectItem key={w.id} value={w.id.toString()}>
                    {w.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="supplier-filter">المورد</Label>
            <Select
              dir="rtl"
              value={supplierFilter}
              onValueChange={(value) =>
                setSupplierFilter(value === 'all' ? '' : value)
              }
            >
              <SelectTrigger id="supplier-filter">
                <SelectValue placeholder="كل الموردين" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الموردين</SelectItem>
                {suppliers.map((s) => (
                  <SelectItem key={s.id} value={s.id.toString()}>
                    {s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="manufacturer-filter">الشركة المصنعة</Label>
            <Select
              dir="rtl"
              value={manufacturerFilter}
              onValueChange={(value) =>
                setManufacturerFilter(value === 'all' ? '' : value)
              }
            >
              <SelectTrigger id="manufacturer-filter">
                <SelectValue placeholder="كل الشركات" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الشركات</SelectItem>
                {manufacturers.map((m) => (
                  <SelectItem key={m.id} value={m.id.toString()}>
                    {m.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2 pb-2">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="returned-only"
                checked={returnedOnlyFilter}
                onCheckedChange={(checked) => setReturnedOnlyFilter(!!checked)}
              />
              <Label htmlFor="returned-only" className="cursor-pointer">
                مرتجعات فقط
              </Label>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="replaced-only"
                checked={replacedOnlyFilter}
                onCheckedChange={(checked) => setReplacedOnlyFilter(!!checked)}
              />
              <Label htmlFor="replaced-only" className="cursor-pointer">
                أجهزة مستبدلة فقط
              </Label>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="ghost" onClick={clearFilters}>
            مسح كل الفلاتر
          </Button>
        </CardFooter>
      </Card>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الموديل</TableHead>
              <TableHead>الإجمالي</TableHead>
              <TableHead>متاح للبيع</TableHead>
              <TableHead>يحتاج صيانة</TableHead>
              <TableHead>إجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSummaryData.length > 0 ? (
              filteredSummaryData.map((item) => (
                <TableRow key={item.model}>
                  <TableCell className="font-medium">{item.model}</TableCell>
                  <TableCell>{item.total}</TableCell>
                  <TableCell>{item.available}</TableCell>
                  <TableCell>{item.maintenance}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleOpenDetailsModal(item)}
                    >
                      عرض التفاصيل
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  لا توجد بيانات تطابق الفلاتر المحددة.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Dialog for model details */}
      <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>تفاصيل موديل: {detailsModel?.model}</DialogTitle>
            <DialogDescription>
              نظرة مفصلة على حالة الأجهزة لهذا الموديل.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex justify-between rounded-md bg-secondary p-2">
              <span>الإجمالي:</span>
              <span className="font-semibold">{detailsModel?.total}</span>
            </div>
            <div className="flex justify-between rounded-md bg-green-500/10 p-2">
              <span>متاح للبيع:</span>
              <span className="font-semibold text-green-400">
                {detailsModel?.available}
              </span>
            </div>
            <div className="flex justify-between rounded-md bg-yellow-500/10 p-2">
              <span>يحتاج صيانة:</span>
              <span className="font-semibold text-yellow-400">
                {detailsModel?.maintenance}
              </span>
            </div>
            <div className="flex justify-between rounded-md bg-blue-500/10 p-2">
              <span>قيد الإصلاح:</span>
              <span className="font-semibold text-blue-400">
                {detailsModel?.inRepair}
              </span>
            </div>
            <div className="flex justify-between rounded-md bg-red-500/10 p-2">
              <span>مباع:</span>
              <span className="font-semibold text-red-400">
                {detailsModel?.sold}
              </span>
            </div>
            <div className="flex justify-between rounded-md bg-orange-500/10 p-2">
              <span>معيب:</span>
              <span className="font-semibold text-orange-400">
                {detailsModel?.defective}
              </span>
            </div>
            <div className="flex justify-between rounded-md bg-destructive/20 p-2">
              <span>تالف:</span>
              <span className="font-semibold text-destructive">
                {detailsModel?.damaged}
              </span>
            </div>
          </div>
          <DialogFooter className="gap-2 sm:justify-start">
            <Button
              variant="outline"
              onClick={() => handleDetailsModalExport('print')}
            >
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
            <Button
              variant="outline"
              onClick={() => handleDetailsModalExport('download')}
            >
              <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
            </Button>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Large Dialog for viewing all devices */}
      <Dialog open={isAllDevicesModalOpen} onOpenChange={setIsAllDevicesModalOpen}>
        <DialogContent className="max-w-[95vw] max-h-[90vh] w-full">
          <DialogHeader>
            <DialogTitle>جميع الأجهزة في المخزون</DialogTitle>
            <DialogDescription>
              عرض شامل لجميع الأجهزة مع إمكانيات البحث والتصفية
            </DialogDescription>
          </DialogHeader>
          
          {/* Search and Filter Controls */}
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <Label htmlFor="search-all">البحث</Label>
              <CommandInput
                id="search-all"
                placeholder="ابحث بالموديل أو الرقم التسلسلي..."
                value={allDevicesSearchQuery}
                onChange={(e) => setAllDevicesSearchQuery(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="w-64">
              <Label>تصفية حسب الحالة</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-right mt-1"
                  >
                    <span>
                      {allDevicesStatusFilter.length > 0
                        ? `${allDevicesStatusFilter.length} حالات محددة`
                        : 'كل الحالات'}
                    </span>
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end">
                  <DropdownMenuLabel>تصفية حسب الحالة</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    'متاح للبيع',
                    'تحتاج صيانة',
                    'قيد الإصلاح',
                    'مرسل للمخزن',
                    'مباع',
                    'معيب',
                    'تالف',
                  ].map((status) => (
                    <DropdownMenuCheckboxItem
                      key={status}
                      checked={allDevicesStatusFilter.includes(status)}
                      onCheckedChange={() => handleAllDevicesStatusFilterChange(status)}
                      className="justify-end"
                    >
                      {status}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Devices Table */}
          <div className="border rounded-lg overflow-auto max-h-[60vh]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الرقم التسلسلي</TableHead>
                  <TableHead>الموديل</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>المخزن</TableHead>
                  <TableHead>المورد</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAllDevices.length > 0 ? (
                  filteredAllDevices.map((device) => (
                    <TableRow key={device.id}>
                      <TableCell className="font-mono">{device.serialNumber}</TableCell>
                      <TableCell className="font-medium">{device.model}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(device.status)}>
                          {device.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {warehouses.find(w => w.id === device.warehouseId)?.name || '-'}
                      </TableCell>
                      <TableCell>
                        {suppliers.find(s => s.id === device.supplierId)?.name || '-'}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      لا توجد أجهزة تطابق معايير البحث.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Footer with summary and actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                عدد الأجهزة: {filteredAllDevices.length}
              </div>
              <div className="text-sm text-muted-foreground font-semibold">
                إجمالي الأجهزة: {devices.length}
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  // Export filtered devices to PDF
                  const doc = new jsPDF();
                  doc.setR2L(true);
                  const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
                  addHeader();
                  doc.setFontSize(18).text('تقرير جميع الأجهزة', 190, 45, { align: 'right' });

                  const head = [['المورد', 'المخزن', 'الحالة', 'الموديل', 'الرقم التسلسلي']];
                  const body = filteredAllDevices.map((device) => [
                    suppliers.find(s => s.id === device.supplierId)?.name || '-',
                    warehouses.find(w => w.id === device.warehouseId)?.name || '-',
                    device.status,
                    device.model,
                    device.serialNumber,
                  ]);

                  autoTable(doc, {
                    head: head,
                    body: body,
                    startY: 55,
                    styles: { font: 'Helvetica', halign: 'right' },
                    headStyles: { halign: 'center', fillColor: [44, 51, 51] },
                    didDrawPage: addFooter,
                  });
                  doc.save(`all_devices_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف
                }}
              >
                <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  // Export filtered devices to Excel
                  const xlsxData = filteredAllDevices.map((device) => ({
                    'الرقم التسلسلي': device.serialNumber,
                    'الموديل': device.model,
                    'الحالة': device.status,
                    'المخزن': warehouses.find(w => w.id === device.warehouseId)?.name || '-',
                    'المورد': suppliers.find(s => s.id === device.supplierId)?.name || '-',
                  }));
                  const worksheet = XLSX.utils.json_to_sheet(xlsxData);
                  const workbook = XLSX.utils.book_new();
                  XLSX.utils.book_append_sheet(workbook, worksheet, 'جميع الأجهزة');
                  XLSX.writeFile(
                    workbook,
                    `all_devices_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف
    );
                }}
              >
                <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
              </Button>
              <DialogClose asChild>
                <Button variant="outline">إغلاق</Button>
              </DialogClose>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}