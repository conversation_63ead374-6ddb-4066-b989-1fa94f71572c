import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// جلب تعليقات طلب معين
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const requestId = parseInt(id);
    
    if (isNaN(requestId)) {
      return NextResponse.json({ error: 'Invalid request ID' }, { status: 400 });
    }

    // جلب التعليقات مع ترتيبها حسب التاريخ ومعلومات المستخدم
    const comments = await prisma.$queryRaw`
      SELECT 
        rc.*,
        u.name as "userName",
        u.username as "userUsername", 
        u.role as "userRole"
      FROM "request_comments" rc
      LEFT JOIN "users" u ON rc."userId" = u.id
      WHERE rc."requestId" = ${requestId}
      ORDER BY rc."createdAt" ASC
    `;

    // تنسيق البيانات لضمان وجود userName
    const formattedComments = comments.map(comment => ({
      ...comment,
      userName: comment.userName || comment.userUsername || 'مستخدم غير معروف',
      userRole: comment.userRole || 'user'
    }));

    return NextResponse.json(formattedComments);
  } catch (error) {
    console.error('خطأ في جلب التعليقات:', error);
    return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
  }
}

// إضافة تعليق جديد
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const requestId = parseInt(id);
    const body = await request.json();
    
    if (isNaN(requestId)) {
      return NextResponse.json({ error: 'Invalid request ID' }, { status: 400 });
    }

    const { comment, commentType = 'comment', isInternal = false } = body;

    if (!comment || !comment.trim()) {
      return NextResponse.json({ error: 'Comment is required' }, { status: 400 });
    }

    // الحصول على معلومات المستخدم (يجب تحسينها لاحقاً مع نظام المصادقة)
    const currentUserId = 1; // مؤقت
    const currentUser = await prisma.user.findUnique({
      where: { id: currentUserId }
    });

    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // إنشاء التعليق مع معلومات المستخدم
    const newComment = await prisma.$queryRaw`
      INSERT INTO "request_comments" (
        "requestId", "userId", "comment", "isInternal"
      ) VALUES (
        ${requestId}, ${currentUserId}, ${comment.trim()}, ${isInternal}
      ) RETURNING *;
    `;

    const commentResult = Array.isArray(newComment) ? newComment[0] : newComment;
    
    // إضافة معلومات المستخدم للاستجابة
    const commentWithUserInfo = {
      ...commentResult,
      userName: currentUser.name || currentUser.username || 'مستخدم غير معروف',
      userRole: currentUser.role || 'user'
    };

    // جلب معلومات الطلب للإشعارات
    const employeeRequest = await prisma.employeeRequest.findUnique({
      where: { id: requestId }
    });

    if (employeeRequest) {
      // تحديث حالة الطلب إذا كان طلب توضيح
      if (commentType === 'clarification_request') {
        await prisma.employeeRequest.update({
          where: { id: requestId },
          data: { status: 'قيد المراجعة المتقدمة' }
        });
      }

      // إرسال إشعارات (تبسيط مؤقت)
      if (commentType === 'clarification_request') {
        await prisma.$queryRaw`
          INSERT INTO "notifications" (
            "userId", "type", "title", "message", 
            "requestId", "priority"
          ) VALUES (
            ${employeeRequest.employeeId}, 'clarification',
            'طلب توضيح على طلبك',
            ${`تم طلب توضيح على طلبك ${employeeRequest.requestNumber}. يرجى مراجعة التعليقات والرد.`},
            ${employeeRequest.id}, 'normal'
          );
        `;
      }
    }

    return NextResponse.json(commentWithUserInfo, { status: 201 });
  } catch (error) {
    console.error('خطأ في إضافة التعليق:', error);
    return NextResponse.json({ error: 'Failed to add comment' }, { status: 500 });
  }
}

// تحديث تعليق
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const requestId = parseInt(params.id);
    const body = await request.json();
    
    if (isNaN(requestId)) {
      return NextResponse.json({ error: 'Invalid request ID' }, { status: 400 });
    }

    const { commentId, comment } = body;

    if (!commentId || !comment || !comment.trim()) {
      return NextResponse.json({ error: 'Comment ID and content are required' }, { status: 400 });
    }

    // التحقق من ملكية التعليق (يجب تحسينها مع نظام المصادقة)
    const currentUserId = 1; // مؤقت
    
    const existingComment = await prisma.requestComment.findFirst({
      where: {
        id: commentId,
        requestId,
        userId: currentUserId
      }
    });

    if (!existingComment) {
      return NextResponse.json({ error: 'Comment not found or unauthorized' }, { status: 404 });
    }

    // تحديث التعليق
    const updatedComment = await prisma.requestComment.update({
      where: { id: commentId },
      data: {
        comment: comment.trim(),
        updatedAt: new Date()
      }
    });

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error('خطأ في تحديث التعليق:', error);
    return NextResponse.json({ error: 'Failed to update comment' }, { status: 500 });
  }
}

// حذف تعليق
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const requestId = parseInt(params.id);
    const { commentId } = await request.json();
    
    if (isNaN(requestId) || !commentId) {
      return NextResponse.json({ error: 'Invalid request ID or comment ID' }, { status: 400 });
    }

    // التحقق من ملكية التعليق أو صلاحية الإدارة
    const currentUserId = 1; // مؤقت
    const currentUser = await prisma.user.findUnique({
      where: { id: currentUserId }
    });

    const existingComment = await prisma.requestComment.findFirst({
      where: {
        id: commentId,
        requestId
      }
    });

    if (!existingComment) {
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    // التحقق من الصلاحية
    const canDelete = existingComment.userId === currentUserId || 
                     currentUser?.role === 'admin' || 
                     currentUser?.role === 'manager';

    if (!canDelete) {
      return NextResponse.json({ error: 'Unauthorized to delete this comment' }, { status: 403 });
    }

    // حذف التعليق
    await prisma.requestComment.delete({
      where: { id: commentId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف التعليق:', error);
    return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 });
  }
}
