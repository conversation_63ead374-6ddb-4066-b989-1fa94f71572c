'use client';

import { useState, useMemo, useEffect, useRef } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import { DarkModeToggle } from './DarkModeToggle';
import './enhanced-styles.css';
import type {
  Return,
  ReturnItem,
  ReturnReason,
  Sale,
  SaleItem,
  Device,
  EmployeeRequestType,
  EmployeeRequestPriority,
  SystemSettings,
} from '@/lib/types';
import AttachmentsViewer from '@/components/AttachmentsViewer';

// نوع ملف المرفق الجديد
interface AttachmentFile {
  originalName: string;
  fileName: string;
  filePath: string;
  size: number;
  type: string;
  uploadedAt: string;
}
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import {
  Save,
  Search,
  X,
  FolderOpen,
  Trash,
  PlusCircle,
  Trash2,
  Printer,
  FileDown,
  Upload,
  FileSpreadsheet,
  Check,
  ChevronsUpDown,
  MessageSquareQuote,
  Eye,
  File,
  FileText,
  Download,
  ArrowUpDown,
  RotateCcw,
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { cn } from '@/lib/utils';
import {
  isAfter,
  addDays,
  addMonths,
  addYears,
  formatDistanceToNowStrict,
} from 'date-fns';
import { ar } from 'date-fns/locale';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

const initialFormState = {
  warehouseId: '',
  clientId: '',
  date: new Date().slice(0, 16), // ✅ استخدام slice(0, 16) للحصول على التاريخ والوقت
  notes: '',
  opReturnNumber: '',
};

const initialSearchState = {
  imei: '',
  foundSale: null as Sale | null,
  foundDevice: null as SaleItem | null,
  warrantyStatus: '',
  returnReason: 'خلل مصنعي' as ReturnReason,
  replacementImei: '',
  isReplacement: false, // هل هذا استبدال أم مرتجع عادي
};



export default function ReturnsPage() {
  const {
    devices,
    sales,
    warehouses,
    clients,
    returns,
    currentUser,
    systemSettings,
    addReturn,
    updateReturn,
    deleteReturn,
    canDeviceBeReturned,
    addEmployeeRequest,
    getAuthHeader, // ✅ إضافة دالة التفويض
    // البيانات المطلوبة لفحص العلاقات
    supplyOrders,
    evaluationOrders,
    warehouseTransfers,
    maintenanceHistory,
  } = useStore();
  const { toast } = useToast();

  const [roNumber, setRoNumber] = useState('');
  const [formState, setFormState] = useState(initialFormState);
  const [searchState, setSearchState] = useState(initialSearchState);
  const [returnItems, setReturnItems] = useState<ReturnItem[]>([]);

  const [loadedReturn, setLoadedReturn] = useState<Return | null>(null);
  const [isLoadOrderDialogOpen, setIsLoadOrderDialogOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<Return | null>(null);
  const [isCancelAlertOpen, setIsCancelAlertOpen] = useState(false);
  const [showAcceptWithoutWarrantyBtn, setShowAcceptWithoutWarrantyBtn] =
    useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // متغيرات جديدة للمرفقات والمسودات والملاحظات
  const [isDraft, setIsDraft] = useState(false);
  const [attachments, setAttachments] = useState<AttachmentFile[]>([]);
  const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = useState(false);
  const [hasDraft, setHasDraft] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [requestReturn, setRequestReturn] = useState<Return | null>(null);
  const [requestFormData, setRequestFormData] = useState({
    requestType: 'تعديل' as EmployeeRequestType,
    priority: 'عادي' as EmployeeRequestPriority,
    notes: '',
    attachmentName: '',
  });
  const [exportColumns, setExportColumns] = useState({
    returnNumber: true,
    date: true,
    client: true,
    warehouse: true,
    serialNumber: true,
    model: true,
    returnReason: true,
    operationType: true,
    replacementDevice: true
  });
  const [hasSavedDraft, setHasSavedDraft] = useState(false);
  // تم حذف متغيرات المعاينة القديمة - يتم استخدام AttachmentsViewer الآن
  
  const attachmentInputRef = useRef<HTMLInputElement>(null);
  const attachmentsInputRef = useRef<HTMLInputElement>(null);

  // فحص الصلاحيات
  const canView = currentUser?.permissions?.returns?.view ?? true;
  const canCreate = currentUser?.permissions?.returns?.create ?? true;
  const canEdit = currentUser?.permissions?.returns?.edit ?? true;
  const canDelete = currentUser?.permissions?.returns?.delete ?? false;
  const canAcceptWithoutWarranty = currentUser?.permissions?.returns?.acceptWithoutWarranty ?? false;

  // وضع الإنشاء - يتم تفعيله عند بدء إنشاء مرتجع جديد
  const [isCreateMode, setIsCreateMode] = useState(false);

  // فحص وجود المسودة
  useEffect(() => {
    const checkDraft = () => {
      const savedDraft = localStorage.getItem('returnDraft');
      setHasDraft(!!savedDraft);
    };

    checkDraft();
    // فحص دوري كل ثانية للتحديث الفوري
    const interval = setInterval(checkDraft, 1000);
    return () => clearInterval(interval);
  }, []);
  // Temporary permissions (remove when auth system is implemented)
  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };


  // ✅ دالة لتنسيق التاريخ والوقت بالأرقام الإنجليزية
  // استخدم formatDateTime من date-utils بدلاً من هذه الدالة

  // تحديث البيانات القديمة لتتضمن الحقول الجديدة
  useEffect(() => {
    const updateOldReturns = () => {
      const updatedReturns = returns.map(returnItem => {
        if (!returnItem.createdAt || !returnItem.employeeName) {
          return {
            ...returnItem,
            employeeName: returnItem.employeeName || 'مدير النظام',
            createdAt: returnItem.createdAt || new Date(),
            attachments: returnItem.attachments || []
          };
        }
        return returnItem;
      });

      // تحديث المتجر إذا كانت هناك تغييرات
      const hasChanges = updatedReturns.some((updated, index) =>
        updated.createdAt !== returns[index]?.createdAt ||
        updated.employeeName !== returns[index]?.employeeName
      );

      if (hasChanges) {
        console.log('تم تحديث البيانات القديمة للمرتجعات لتتضمن الحقول الجديدة');
      }
    };

    if (returns.length > 0) {
      updateOldReturns();
    }
  }, [returns]);

  // فحص صلاحية العرض
  if (!canView) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-600">
              <path d="M18 6L6 18M6 6l12 12"/>
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">غير مصرح لك</h3>
            <p className="text-gray-600">ليس لديك صلاحية لعرض صفحة المرتجعات</p>
          </div>
        </div>
      </div>
    );
  }

  // وظيفة لتحديد المخزن الافتراضي حسب صلاحيات المستخدم
  const determineDefaultWarehouse = (): string => {
    if (!currentUser || !warehouses.length) return '';
    
    // إذا كان لدى المستخدم صلاحية الوصول لمخازن محددة
    if (currentUser.warehouseAccess && currentUser.warehouseAccess.length > 0) {
      const userAccessWarehouses = warehouses.filter(w => currentUser.warehouseAccess?.includes(w.id));
      // إذا كان لديه صلاحية لمخزن واحد فقط، اختر هذا المخزن
      if (userAccessWarehouses.length === 1) {
        return userAccessWarehouses[0].id.toString();
      } 
      // إذا كان لديه صلاحية لعدة مخازن، اختر أولها كافتراضي
      else if (userAccessWarehouses.length > 1) {
        return userAccessWarehouses[0].id.toString();
      }
    }
    
    // إذا كان هناك مخزن افتراضي في إعدادات النظام
    if (systemSettings?.defaultWarehouseId) {
      const defaultWarehouse = warehouses.find(w => w.id === systemSettings.defaultWarehouseId);
      if (defaultWarehouse) {
        return systemSettings.defaultWarehouseId.toString();
      }
    }
    
    // كحل أخير، اختر أول مخزن متاح
    return warehouses.length > 0 ? warehouses[0].id.toString() : '';
  };

  // تطبيق المخزن الافتراضي عند تحميل الصفحة أو تغيير البيانات
  useEffect(() => {
    if (!formState.warehouseId && warehouses.length > 0 && currentUser) {
      const defaultWarehouse = determineDefaultWarehouse();
      if (defaultWarehouse) {
        setFormState(prev => ({ ...prev, warehouseId: defaultWarehouse }));
      }
    }
  }, [warehouses, currentUser, systemSettings]);

  useEffect(() => {
    if (!loadedReturn) {
      // إنشاء رقم مرتجع فريد ومتسلسل مثل صفحة التوريد
      const generateUniqueReturnId = () => {
        // استخراج أكبر رقم موجود من أرقام أوامر المرتجعات
        let maxNumber = 0;
        returns.forEach(returnOrder => {
          if (returnOrder.roNumber && returnOrder.roNumber.startsWith('RO-')) {
            const numberPart = parseInt(returnOrder.roNumber.replace('RO-', ''));
            if (!isNaN(numberPart) && numberPart > maxNumber) {
              maxNumber = numberPart;
            }
          }
        });

        return `RO-${maxNumber + 1}`;
      };

      setRoNumber(generateUniqueReturnId());
    } else {
      setRoNumber(loadedReturn.roNumber);
    }
  }, [returns, loadedReturn]);

  const resetPage = (preserveOpReturnNumber = false) => {
    // ✅ الحفاظ على opReturnNumber إذا طُلب ذلك
    const currentOpReturnNumber = preserveOpReturnNumber ? formState.opReturnNumber : '';

    setFormState({
      ...initialFormState,
      opReturnNumber: currentOpReturnNumber, // ✅ الحفاظ على رقم المرتجع الرسمي إذا كان موجوداً
    });
    setSearchState(initialSearchState);
    setReturnItems([]);
    setLoadedReturn(null);
    setShowAcceptWithoutWarrantyBtn(false);
    setAttachments([]); // ← مسح المرفقات
    setIsCreateMode(false); // ← إلغاء وضع الإنشاء
  };

  const handleCreateNew = () => {
    // ✅ الحفاظ على opReturnNumber عند إنشاء مرتجع جديد
    const hasOpReturnNumber = formState.opReturnNumber && formState.opReturnNumber.trim() !== '';
    resetPage(hasOpReturnNumber); // ✅ تمرير معامل للحفاظ على opReturnNumber
    setIsCreateMode(true); // ← تفعيل وضع الإنشاء
    toast({ title: 'جاهز للبدء', description: 'تم تجهيز أمر مرتجع جديد.' });
  };

  const handleCancel = () => {
    resetPage();
    setIsCancelAlertOpen(false);
  };

  // وظائف المسودة والمرفقات والملاحظات
  const saveDraft = () => {
    try {
      // منع الحفظ كمسودة إذا تم عرض أمر سابق
      if (loadedReturn) {
        toast({
          variant: 'destructive',
          title: 'غير متاح',
          description: 'لا يمكن حفظ أمر موجود كمسودة. استخدم "حفظ" بدلاً من ذلك.',
        });
        return;
      }

      if (returnItems.length === 0 && !formState.clientId) {
        toast({
          variant: 'destructive',
          title: 'لا يمكن الحفظ',
          description: 'لا توجد بيانات كافية للحفظ كمسودة.',
        });
        return;
      }

      const draftData = {
        formState,
        returnItems,
        attachments,
        roNumber,
        timestamp: new Date(),
      };

      localStorage.setItem('returnDraft', JSON.stringify(draftData));
      setHasSavedDraft(true);
      setIsDraft(true);

      toast({
        title: 'تم الحفظ',
        description: 'تم حفظ المسودة بنجاح. يمكنك المتابعة لاحقاً.',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ المسودة.',
      });
    }
  };

  const loadDraft = () => {
    try {
      const savedDraft = localStorage.getItem('returnDraft');
      if (!savedDraft) {
        toast({
          variant: 'destructive',
          title: 'لا توجد مسودة',
          description: 'لم يتم العثور على أي مسودة محفوظة.',
        });
        return;
      }

      const draftData = JSON.parse(savedDraft);
      setFormState(draftData.formState);
      setReturnItems(draftData.returnItems || []);
      setAttachments(draftData.attachments || []);
      setRoNumber(draftData.roNumber);
      setIsDraft(true);
      setHasSavedDraft(true);

      toast({
        title: 'تم التحميل',
        description: 'تم تحميل المسودة المحفوظة بنجاح.',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في التحميل',
        description: 'حدث خطأ أثناء تحميل المسودة.',
      });
    }
  };

  const clearDraft = () => {
    localStorage.removeItem('returnDraft');
    setHasSavedDraft(false);
    setIsDraft(false);
  };

  // تم حذف دوال المعاينة القديمة - يتم استخدام AttachmentsViewer الآن

  const handleLoadReturn = (returnOrder: Return) => {
    setLoadedReturn(returnOrder);
    setIsCreateMode(false); // ← إلغاء وضع الإنشاء عند تحميل مرتجع موجود
    const client = clients.find((c) => c.name === returnOrder.clientName);
    const warehouse = warehouses.find(
      (w) => w.name === returnOrder.warehouseName
    );

    setFormState({
      clientId: client?.id.toString() || '',
      warehouseId: warehouse?.id.toString() || '',
      date: new Date(returnOrder.date).slice(0, 16), // ✅ استخدام slice(0, 16) للحصول على التاريخ والوقت
      notes: returnOrder.notes || '',
      opReturnNumber: returnOrder.opReturnNumber || '',
    });
    setReturnItems(returnOrder.items || []);

    // تحميل المرفقات مع تحويل النوع القديم إلى الجديد
    if (returnOrder.attachments) {
      let attachmentsArray: any[] = [];

      // التعامل مع أنواع البيانات المختلفة
      if (typeof returnOrder.attachments === 'string') {
        try {
          // إذا كان string، حاول تحويله إلى JSON
          attachmentsArray = JSON.parse(returnOrder.attachments);
        } catch (error) {
          console.error('Error parsing attachments JSON:', error);
          attachmentsArray = [];
        }
      } else if (Array.isArray(returnOrder.attachments)) {
        // إذا كان مصفوفة بالفعل
        attachmentsArray = returnOrder.attachments;
      }

      if (attachmentsArray.length > 0 && typeof attachmentsArray[0] === 'string') {
        // تحويل من النوع القديم (string[]) إلى الجديد (AttachmentFile[])
        const convertedAttachments: AttachmentFile[] = attachmentsArray.map(fileName => ({
          originalName: fileName,
          fileName: fileName,
          filePath: `/attachments/returns/${fileName}`,
          size: 0, // حجم غير معروف للملفات القديمة
          type: 'application/octet-stream', // نوع افتراضي
          uploadedAt: returnOrder.createdAt || new Date()
        }));
        setAttachments(convertedAttachments);
      } else {
        // النوع الجديد بالفعل
        setAttachments(attachmentsArray as AttachmentFile[]);
      }
    } else {
      setAttachments([]);
    }

    setIsLoadOrderDialogOpen(false);
    toast({
      title: 'تم التحميل',
      description: `تم تحميل أمر المرتجع ${returnOrder.roNumber}`,
    });
  };

  const handleLoadLastOrder = () => {
    if (returns.length > 0) {
      const sortedReturns = [...returns].sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      handleLoadReturn(sortedReturns[0]);
    } else {
      toast({
        title: 'لا توجد أوامر',
        description: 'لا توجد أوامر مرتجعات سابقة لعرضها.',
        variant: 'destructive',
      });
    }
  };


  // ✅ فحص العلاقات قبل حذف المرتجع - منطق صحيح: فحص العمليات التالية فقط
  const checkReturnRelations = (returnToCheck: Return): { canDelete: boolean; relatedOperations: string[] } => {
    const relatedOperations: string[] = [];

    // فحص الأجهزة في هذا المرتجع
    let imeisInReturn: string[] = [];
    try {
      const items = typeof returnToCheck.items === 'string' ? returnToCheck.items : returnToCheck.items;
      imeisInReturn = Array.isArray(items) ? items.map(item => item.deviceId) : [];
    } catch {
      imeisInReturn = [];
    }

    // ✅ فحص العمليات التالية فقط (التي تعتمد على هذا المرتجع)

    // فحص التحويلات المخزنية التالية - هل تم تحويل أجهزة من هذا المرتجع؟
    const laterTransfers = warehouseTransfers.filter(transfer => {
      try {
        const items = typeof transfer.items === 'string' ? transfer.items : transfer.items;
        return Array.isArray(items) && items.some(item => imeisInReturn.includes(item.deviceId));
      } catch {
        return false;
      }
    });
    if (laterTransfers.length > 0) {
      relatedOperations.push(`${laterTransfers.length} تحويل مخزني تالي`);
    }

    // فحص سجلات الصيانة التالية - هل تم إرسال أجهزة للصيانة بعد المرتجع؟
    const laterMaintenance = maintenanceHistory.filter(maintenance =>
      imeisInReturn.includes(maintenance.deviceId) &&
      new Date(maintenance.createdAt) > new Date(returnToCheck.createdAt)
    );
    if (laterMaintenance.length > 0) {
      relatedOperations.push(`${laterMaintenance.length} سجل صيانة تالي`);
    }

    // فحص المرتجعات التالية - هل تم إرجاع نفس الأجهزة مرة أخرى؟
    const laterReturns = returns.filter(r => {
      if (r.id === returnToCheck.id) return false;
      try {
        const items = typeof r.items === 'string' ? r.items : r.items;
        return Array.isArray(items) &&
               items.some(item => imeisInReturn.includes(item.deviceId)) &&
               new Date(r.createdAt) > new Date(returnToCheck.createdAt);
      } catch {
        return false;
      }
    });
    if (laterReturns.length > 0) {
      relatedOperations.push(`${laterReturns.length} مرتجع تالي`);
    }

    return {
      canDelete: relatedOperations.length === 0,
      relatedOperations
    };
  };

  const handleDeleteReturn = () => {
    if (!orderToDelete) return;

    // فحص العلاقات قبل الحذف
    const { canDelete, relatedOperations } = checkReturnRelations(orderToDelete);

    // تسجيل للتشخيص
    console.log('فحص حذف المرتجع:', {
      returnId: orderToDelete.id,
      roNumber: orderToDelete.roNumber,
      canDelete,
      relatedOperations,
      status: orderToDelete.status,
      itemsCount: orderToDelete.items?.length,
      attachmentsCount: orderToDelete.attachments?.length
    });

    if (!canDelete) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن حذف المرتجع',
        description: `هذا المرتجع مرتبط بالعمليات التالية: ${relatedOperations.join(', ')}. يجب إلغاء هذه العمليات أولاً قبل الحذف.`,
      });
      setOrderToDelete(null);
      return;
    }

    // إذا كان الحذف مسموحاً، تابع العملية
    try {
      deleteReturn(orderToDelete.id);
      toast({
        variant: 'destructive',
        title: 'تم الحذف',
        description: `تم حذف أمر المرتجع ${orderToDelete.roNumber} بنجاح`,
      });
      if (loadedReturn && loadedReturn.id === orderToDelete.id) {
        resetPage();
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في الحذف',
        description: 'حدث خطأ أثناء حذف المرتجع. يرجى المحاولة مرة أخرى.',
      });
    }
    setOrderToDelete(null);
  };

  const calculateWarranty = (sale: Sale) => {
    const saleDate = new Date(sale.date);
    let expiryDate: Date;

    switch (sale.warrantyPeriod) {
      case '3d':
        expiryDate = addDays(saleDate, 3);
        break;
      case '1w':
        expiryDate = addDays(saleDate, 7);
        break;
      case '1m':
        expiryDate = addMonths(saleDate, 1);
        break;
      case '3m':
        expiryDate = addMonths(saleDate, 3);
        break;
      case '6m':
        expiryDate = addMonths(saleDate, 6);
        break;
      case '1y':
        expiryDate = addYears(saleDate, 1);
        break;
      default:
        return 'بدون ضمان';
    }

    const today = new Date();
    if (isAfter(expiryDate, today)) {
      const remaining = formatDistanceToNowStrict(expiryDate, {
        locale: ar,
        addSuffix: true,
      });
      return `في الضمان (ينتهي ${remaining})`;
    } else {
      return `ضمان منتهي (انتهى: ${formatDateTime(expiryDate.toISOString())})`;
    }
  };

  const handleSearchDevice = () => {
    if (!searchState.imei) return;

    // التحقق من إمكانية إرجاع الجهاز
    const validation = canDeviceBeReturned(searchState.imei);
    if (!validation.canReturn) {
      toast({
        title: 'لا يمكن إرجاع الجهاز',
        description: validation.reason,
        variant: 'destructive',
      });
      setSearchState((s) => ({
        ...s,
        foundSale: null,
        foundDevice: null,
        warrantyStatus: '',
      }));
      return;
    }

    let saleContainingDevice: Sale | undefined;
    let deviceInSale: SaleItem | undefined;

    for (const sale of sales) {
      const found = (Array.isArray(sale.items) ? sale.items.find(
        (item) => item.deviceId === searchState.imei
      ) : undefined);
      if (found) {
        saleContainingDevice = sale;
        deviceInSale = found;
        break;
      }
    }

    if (saleContainingDevice && deviceInSale) {
      if (returnItems.length === 0) {
        const client = clients.find(
          (c) => c.name === saleContainingDevice?.clientName
    );
        const warehouse = warehouses.find(
          (w) => w.name === saleContainingDevice?.warehouseName
    );
        setFormState((s) => ({
          ...s,
          clientId: client?.id.toString() || '',
          warehouseId: warehouse?.id.toString() || '',
        }));
      }

      const warranty = calculateWarranty(saleContainingDevice as Sale);
      const userHasPermission =
        currentUser?.permissions.returns?.acceptWithoutWarranty;
      if (
        (warranty.includes('منتهي') || warranty.includes('بدون')) &&
        userHasPermission
      ) {
        setShowAcceptWithoutWarrantyBtn(true);
      } else {
        setShowAcceptWithoutWarrantyBtn(false);
      }

      setSearchState((s) => ({
        ...s,
        foundSale: saleContainingDevice,
        foundDevice: deviceInSale,
        warrantyStatus: warranty,
      }));
      toast({
        title: 'تم العثور على الجهاز',
        description: `الجهاز ${deviceInSale.model} مباع ضمن الفاتورة ${saleContainingDevice.soNumber}`,
      });
    } else {
      toast({
        title: 'الجهاز غير موجود',
        description: 'لم يتم العثور على جهاز مباع بهذا الرقم التسلسلي.',
        variant: 'destructive',
      });
      setSearchState((s) => ({
        ...s,
        foundSale: null,
        foundDevice: null,
        warrantyStatus: '',
      }));
    }
  };

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    const newItems: ReturnItem[] = [];
    const existingDeviceIds = new Set(returnItems.map((item) => item.deviceId));

    for (const imei of lines) {
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }

      let saleContainingDevice: Sale | undefined;
      let deviceInSale: SaleItem | undefined;

      for (const sale of sales) {
        const found = (Array.isArray(sale.items) ? sale.items.find((item) => item.deviceId === imei) : undefined);
        if (found) {
          saleContainingDevice = sale;
          deviceInSale = found;
          break;
        }
      }

      if (!saleContainingDevice || !deviceInSale) {
        invalidCount++;
        continue;
      }

      if (returnItems.length === 0 && newItems.length === 0) {
        const client = clients.find(
          (c) => c.name === saleContainingDevice?.clientName
    );
        const warehouse = warehouses.find(
          (w) => w.name === saleContainingDevice?.warehouseName
    );
        setFormState((s) => ({
          ...s,
          clientId: client?.id.toString() || '',
          warehouseId: warehouse?.id.toString() || '',
        }));
      }

      const newItem: ReturnItem = {
        deviceId: deviceInSale.deviceId,
        model: deviceInSale.model,
        returnReason: 'خلل مصنعي',
      };
      newItems.push(newItem);
      existingDeviceIds.add(imei);
      addedCount++;
    }

    if (newItems.length > 0) {
      setReturnItems((prev) => [...prev, ...newItems]);
    }

    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${addedCount} جهازًا. تم تخطي ${invalidCount} جهازًا (غير صالح) و ${duplicateCount} جهازًا (مكرر).`,
    });

    if (event.target) event.target.value = '';
  };

  const handleAddItemToReturn = () => {
    if (!searchState.foundSale || !searchState.foundDevice) return;

    if (
      returnItems.some(
        (item) => item.deviceId === searchState.foundDevice!.deviceId,
      )
    ) {
      toast({
        variant: 'destructive',
        title: 'مكرر',
        description: 'هذا الجهاز مضاف بالفعل إلى القائمة.',
      });
      return;
    }

    if (searchState.isReplacement) {
      if (!searchState.replacementImei) {
        toast({
          variant: 'destructive',
          title: 'بيانات غير مكتملة',
          description: 'يرجى إدخال الرقم التسلسلي للجهاز البديل.',
        });
        return;
      }
      const replacementDevice = devices.find(
        (d) => d.id === searchState.replacementImei
    );
      if (!replacementDevice || replacementDevice.status !== 'متاح للبيع') {
        toast({
          variant: 'destructive',
          title: 'جهاز بديل غير صالح',
          description: 'الجهاز البديل غير موجود أو ليس متاحًا للبيع.',
        });
        return;
      }
    }

    const newItem: ReturnItem = {
      deviceId: searchState.foundDevice.deviceId,
      model: searchState.foundDevice.model,
      returnReason: searchState.returnReason,
      isReplacement: searchState.isReplacement,
      replacementDeviceId: searchState.isReplacement ? searchState.replacementImei : undefined,
      originalDeviceId: searchState.isReplacement ? searchState.foundDevice.deviceId : undefined,
    };

    setReturnItems((prev) => [...prev, newItem]);
    setSearchState(initialSearchState);
    setShowAcceptWithoutWarrantyBtn(false);
  };

  // ✅ فحص العلاقات للجهاز الفردي قبل الحذف - منطق صحيح: فحص العمليات التالية فقط
  const checkDeviceRelations = (deviceId: string): { hasRelations: boolean; relatedOperations: string[] } => {
    const relatedOps: string[] = [];

    // ✅ فحص العمليات التالية فقط (التي تعتمد على وجود الجهاز في المرتجع)

    // فحص التحويلات المخزنية التالية - هل تم تحويل الجهاز؟
    const deviceInLaterTransfers = warehouseTransfers.some(transfer => {
      try {
        const items = typeof transfer.items === 'string' ? transfer.items : transfer.items;
        return Array.isArray(items) && items.some(item  => item.deviceId === deviceId);
      } catch {
        return false;
      }
    });
    if (deviceInLaterTransfers) relatedOps.push('تحويلات مخزنية تالية');

    // فحص سجلات الصيانة التالية - هل تم إرسال الجهاز للصيانة بعد المرتجع؟
    const deviceInLaterMaintenance = maintenanceHistory.some(maintenance => {
      if (maintenance.deviceId !== deviceId) return false;
      if (!loadedReturn?.createdAt) return true; // إذا لم نعرف تاريخ المرتجع، نعتبر الصيانة مرتبطة
      return new Date(maintenance.createdAt) > new Date(loadedReturn.createdAt);
    });
    if (deviceInLaterMaintenance) relatedOps.push('صيانة تالية');

    // فحص المرتجعات التالية - هل تم إرجاع الجهاز مرة أخرى؟
    const deviceInLaterReturns = returns.some(returnOrder => {
      if (returnOrder.id === loadedReturn?.id) return false;
      if (!loadedReturn?.createdAt) return false;

      try {
        const items = typeof returnOrder.items === 'string' ? returnOrder.items : returnOrder.items;
        return Array.isArray(items) &&
               items.some(item => item.deviceId === deviceId) &&
               new Date(returnOrder.createdAt) > new Date(loadedReturn.createdAt);
      } catch {
        return false;
      }
    });
    if (deviceInLaterReturns) relatedOps.push('مرتجعات تالية');

    return {
      hasRelations: relatedOps.length > 0,
      relatedOperations: relatedOps
    };
  };

  const handleRemoveItem = (deviceId: string) => {
    // فحص العلاقات قبل الحذف
    const relationCheck = checkDeviceRelations(deviceId);

    if (relationCheck.hasRelations) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن حذف الجهاز',
        description: `الجهاز ${deviceId} مستخدم في: ${relationCheck.relatedOperations.join(', ')}`,
      });
      return;
    }

    setReturnItems((prev) => prev.filter((item) => item.deviceId !== deviceId));
    toast({
      title: 'تم الحذف',
      description: `تم حذف الجهاز ${deviceId} من المرتجع`,
    });
  };



  const handleSaveReturn = () => {
    if (
      !formState.clientId ||
      !formState.warehouseId ||
      returnItems.length === 0
    ) {
      toast({
        variant: 'destructive',
        title: 'بيانات غير مكتملة',
        description:
          'يرجى التأكد من اختيار العميل والمخزن وإضافة جهاز واحد على الأقل.',
      });
      return;
    }

    const firstItemSale = sales.find((s) =>
      (Array.isArray(s.items) && s.items.some((i) => i.deviceId === returnItems[0].deviceId))
    );
    if (!firstItemSale) return;

    const client = clients.find((c) => c.id.toString() === formState.clientId);
    const warehouse = warehouses.find(
      (w) => w.id.toString() === formState.warehouseId
    );

    const returnData = {
      roNumber: roNumber, // ✅ إضافة رقم الأمر للحفظ بنفس الرقم
      opReturnNumber: formState.opReturnNumber,
      saleId: firstItemSale.id,
      soNumber: firstItemSale.soNumber,
      clientName: client?.name || 'غير معروف',
      warehouseName: warehouse?.name || 'غير معروف',
      date: new Date(formState.date),
      items: returnItems,
      notes: formState.notes,
      employeeName: currentUser?.name || 'مدير النظام', // ← إضافة اسم الموظف المنشئ
      createdAt: new Date(), // ← إضافة الختم الزمني
      attachments: attachments.map(file => file.fileName), // ← تحويل إلى أسماء ملفات للتوافق
    };

    if (loadedReturn) {
      updateReturn({
        ...returnData,
        id: loadedReturn.id,
        roNumber: loadedReturn.roNumber,
        status: 'مكتمل',
        employeeName: loadedReturn.employeeName || currentUser?.name || 'مدير النظام', // ← الحفاظ على المنشئ الأصلي
        createdAt: loadedReturn.createdAt || new Date(), // ← الحفاظ على وقت الإنشاء الأصلي
      });
      toast({
        title: 'تم التحديث',
        description: 'تم تحديث أمر المرتجع بنجاح.',
      });
    } else {
      addReturn({
        ...returnData,
        status: 'مكتمل',
      });
      toast({ title: 'تم الحفظ', description: 'تم إنشاء أمر المرتجع بنجاح.' });
    }

    // ✅ الاحتفاظ برقم المرتجع الرسمي إذا كان موجوداً
    const shouldPreserveOpReturnNumber = formState.opReturnNumber && formState.opReturnNumber.trim() !== '';
    resetPage(shouldPreserveOpReturnNumber);
  };

  const handlePrint = async () => {
    if (returnItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن الطباعة',
        description: 'أمر المرتجع فارغ.',
      });
      return;
    }

    const doc = new jsPDF();
    doc.setR2L(true);

    doc.setFontSize(20);
    doc.text('أمر مرتجع', 190, 20, { align: 'right' });

    doc.setFontSize(12);
    doc.text(`رقم الأمر: ${roNumber}`, 190, 30, { align: 'right' });
    const client = clients.find((c) => c.id.toString() === formState.clientId);
    doc.text(`العميل: ${client?.name || 'غير محدد'}`, 190, 37, {
      align: 'right',
    });
    doc.text(
      `التاريخ: ${formatDateTime(formState.date)}`,
      190,
      44,
      { align: 'right' }
    );

    autoTable(doc, {
      startY: 55,
      head: [['نوع العملية', 'البديل (إن وجد)', 'سبب الإرجاع', 'الموديل', 'الرقم التسلسلي']],
      body: returnItems.map((item) => [
        item.isReplacement ? 'استبدال' : 'مرتجع عادي',
        item.replacementDeviceId || 'N/A',
        item.returnReason,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      columnStyles: {
        0: { halign: 'center' },
        1: { halign: 'left' },
        4: { halign: 'left' },
      },
    });

    doc.output('dataurlnewwindow');
  };

  const handleExportPdf = async () => {
    if (returnItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن التصدير',
        description: 'أمر المرتجع فارغ.',
      });
      return;
    }

    const doc = new jsPDF();
    doc.setR2L(true);

    doc.setFontSize(20);
    doc.text('أمر مرتجع', 190, 20, { align: 'right' });

    doc.setFontSize(12);
    doc.text(`رقم الأمر: ${roNumber}`, 190, 30, { align: 'right' });
    const client = clients.find((c) => c.id.toString() === formState.clientId);
    doc.text(`العميل: ${client?.name || 'غير محدد'}`, 190, 37, {
      align: 'right',
    });
    doc.text(
      `التاريخ: ${formatDateTime(formState.date)}`,
      190,
      44,
      { align: 'right' }
    );

    autoTable(doc, {
      startY: 55,
      head: [['الرقم التسلسلي', 'الموديل', 'سبب الإرجاع', 'نوع العملية', 'البديل (إن وجد)']],
      body: returnItems.map((item) => [
        item.deviceId,
        item.model,
        item.returnReason,
        item.isReplacement ? 'استبدال' : 'مرتجع عادي',
        item.replacementDeviceId || 'N/A',
      ]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      columnStyles: {
        0: { halign: 'left' },
        3: { halign: 'center' },
        4: { halign: 'left' },
      },
    });

    doc.save(`return-order-${roNumber}.pdf`);
  };

  const handleExportExcel = async () => {
    if (returnItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن التصدير',
        description: 'أمر المرتجع فارغ.',
      });
      return;
    }
    const XLSX = await import('xlsx');
    const client = clients.find((c) => c.id.toString() === formState.clientId);
    const worksheet = XLSX.utils.json_to_sheet(
      returnItems.map((item) => ({
        'رقم أمر المرتجع': roNumber,
        العميل: client?.name,
        التاريخ: formatDateTime(formState.date),
        'الرقم التسلسلي': item.deviceId,
        الموديل: item.model,
        'سبب الإرجاع': item.returnReason,
        'نوع العملية': item.isReplacement ? 'استبدال' : 'مرتجع عادي',
        'الجهاز البديل': item.replacementDeviceId || 'N/A',
      }))
    );
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير المرتجعات');
    XLSX.writeFile(workbook, `Return-Order-${roNumber}.xlsx`);
  };

  const clientDetails = useMemo(() => {
    if (formState.clientId) return clients.find((c) => c.id.toString() === formState.clientId);
    return null;
  }, [formState.clientId, clients]);

  return (
    <div className="returns-page">
      {/* رأس الصفحة المحسن */}
      <div className="header-card mb-6">
        <div className="p-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                <span className="text-xl font-bold">↩️</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                  {loadedReturn
                    ? `تعديل أمر مرتجع ${loadedReturn.roNumber}`
                    : 'إنشاء أمر مرتجع'}
                </h1>
                <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                  نظام إدارة المرتجعات المتقدم مع تتبع شامل للأسباب والاستبدالات
                </p>

                {/* عرض معلومات المنشئ والوقت */}
                {loadedReturn && (loadedReturn.employeeName || loadedReturn.createdAt) && (
                  <div className="flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                    {loadedReturn.employeeName && (
                      <span className="flex items-center gap-2 enhanced-badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-1 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                          <circle cx="12" cy="7" r="4"/>
                        </svg>
                        👤 أنشأ بواسطة: {loadedReturn.employeeName}
                      </span>
                    )}
                    {loadedReturn.createdAt && (
                      <span className="flex items-center gap-2 enhanced-badge bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 px-2 py-1 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"/>
                          <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        🕒 {formatDateTime(loadedReturn.createdAt)}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* زر الوضع الليلي */}
              <DarkModeToggle
                size="md"
                variant="outline"
                className="enhanced-button"
              />
            </div>
          </div>
        </div>
      </div>
      {/* بطاقة الأزرار الرئيسية */}
      <Card className="enhanced-returns-card actions-section animate-fade-in-up">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center justify-center gap-3">
            <Button
              variant="outline"
              onClick={handleLoadLastOrder}
              className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 h-12 px-6"
            >
              <RotateCcw className="ml-2 h-5 w-5" /> 🔄 عرض آخر أمر
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsLoadOrderDialogOpen(true)}
              className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 h-12 px-6"
            >
              <FolderOpen className="ml-2 h-5 w-5" /> 📂 عرض كل الأوامر
            </Button>
            {(canCreate || canEdit) && !loadedReturn && (
              <>
                <Button
                  variant="outline"
                  onClick={saveDraft}
                  className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 h-12 px-6"
                >
                  <Save className="ml-2 h-5 w-5" /> 💾 حفظ كمسودة
                </Button>
                <Button
                  variant="outline"
                  onClick={loadDraft}
                  disabled={!hasDraft}
                  className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 disabled:bg-gray-100 disabled:border-gray-300 disabled:text-gray-400 transition-all duration-200 h-12 px-6"
                  title={hasDraft ? "فتح المسودة المحفوظة" : "لا توجد مسودة محفوظة"}
                >
                  <FolderOpen className="ml-2 h-5 w-5" /> 📄 فتح المسودة
                </Button>
              </>
            )}
            <Button
              variant="outline"
              onClick={() => setRequestReturn(loadedReturn)}
              className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 h-12 px-6"
            >
              <MessageSquareQuote className="ml-2 h-5 w-5" /> 💬 ملاحظة
            </Button>
            {canCreate && (
              <Button
                onClick={handleCreateNew}
                className="enhanced-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-12 px-8 font-bold"
              >
                <PlusCircle className="ml-2 h-5 w-5" /> ➕ مرتجع جديد
              </Button>
            )}
            {canDelete && (
              <Button
                variant="outline"
                onClick={() => {
                  if (loadedReturn) {
                    setOrderToDelete(loadedReturn);
                  } else {
                    toast({
                      variant: 'destructive',
                      title: 'غير متاح',
                      description: 'لا يوجد أمر محمل للحذف',
                    });
                  }
                }}
                disabled={!loadedReturn}
                className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 disabled:bg-gray-100 disabled:border-gray-300 disabled:text-gray-400 transition-all duration-200 h-12 px-6"
              >
                <Trash className="ml-2 h-5 w-5" /> 🗑️ حذف الأمر
              </Button>
            )}


          </div>
        </CardContent>
      </Card>
      
      {/* اسم المستخدم الحالي */}




      <Card className="enhanced-stocktake-card info-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 py-4">
          <CardTitle className="text-lg text-blue-800 dark:text-blue-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">1</div>
            <div>
              <div className="font-bold">تفاصيل أمر المرتجع الأساسية</div>
              <div className="text-xs text-blue-600 dark:text-blue-300 font-normal mt-1">المعلومات الأساسية لأمر الإرجاع والعميل</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-2 pt-4">
          <div className="space-y-1">
            <Label className="text-xs">رقم أمر المرتجع (RO)</Label>
            <Input
              value={roNumber}
              disabled
              className="h-8 text-xs font-mono bg-blue-50 text-blue-900 font-bold border-blue-300"
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="op-return-number" className="text-xs">رقم المرتجع الرسمي</Label>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Input
                  id="op-return-number"
                  placeholder="أدخل الرقم الرسمي..."
                  value={formState.opReturnNumber}
                  onChange={(e) =>
                    setFormState((s) => ({ ...s, opReturnNumber: e.target.value }))
                  }
                  className="h-8 text-xs hover:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200 flex-1"
                />
                {/* ✅ زر مسح رقم المرتجع الرسمي */}
                {formState.opReturnNumber && (isCreateMode || loadedReturn) && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setFormState({ ...formState, opReturnNumber: '' })}
                    className="h-8 px-3 text-xs"
                    title="مسح رقم المرتجع الرسمي"
                  >
                    ✕
                  </Button>
                )}
              </div>
              {/* ✅ مؤشر بصري للاحتفاظ برقم المرتجع الرسمي */}
              {formState.opReturnNumber && (
                <div className="flex items-center space-x-2 text-xs text-green-600 dark:text-green-400">
                  <span>✅ سيتم الاحتفاظ برقم المرتجع الرسمي عند إنشاء مرتجع جديد</span>
                </div>
              )}
            </div>
          </div>
          <div className="space-y-1">
            <Label className="text-xs">التاريخ والوقت</Label>
            <div className="flex gap-1">
              <Input
                type="datetime-local"
                value={formState.date || new Date().slice(0, 16)}
                onChange={(e) =>
                  setFormState((s) => ({ ...s, date: e.target.value }))
                }
                disabled={(!canCreate && !canEdit) || (!isCreateMode && !loadedReturn)}
                className="h-8 text-xs font-mono hover:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200 bg-white flex-1"
                style={{ direction: 'ltr' }}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setFormState(s => ({ ...s, date: new Date().slice(0, 16) }))}
                disabled={(!canCreate && !canEdit) || (!isCreateMode && !loadedReturn)}
                className="h-8 px-2 text-xs border-blue-300 text-blue-600 hover:bg-blue-50"
                title="تعيين الوقت الحالي"
              >
                الآن
              </Button>
            </div>
          </div>
          <div className="space-y-1">
            <Label className="text-xs">المستخدم الحالي</Label>
            <Input
              value={currentUser?.name || 'غير محدد'}
              disabled
              className="h-8 text-xs bg-gray-50 text-gray-700"
            />
          </div>
          <div className="w-32 space-y-1">
            <Label className="text-xs">المرفقات</Label>
            <div className="flex gap-1">
              <input
                type="file"
                ref={attachmentsInputRef}
                className="hidden"
                multiple
                onChange={async (e) => {
                  const files = Array.from(e.target.files || []);
                  if (files.length === 0) return;

                  try {
                    const formData = new FormData();
                    files.forEach(file => formData.append('files', file));
                    formData.append('section', 'returns');

                    const response = await fetch('/api/upload', {
                      method: 'POST',
                      headers: getAuthHeader(),
                      body: formData,
                    });

                    const result = await response.json();

                    if (result.success) {
                      setAttachments((prev) => [...prev, ...result.files]);
                      toast({
                        title: 'تم إرفاق الملفات',
                        description: result.message,
                      });
                    } else {
                      toast({
                        title: 'خطأ في رفع الملفات',
                        description: result.error,
                        variant: 'destructive',
                      });
                    }
                  } catch (error) {
                    toast({
                      title: 'خطأ في رفع الملفات',
                      description: 'حدث خطأ غير متوقع',
                      variant: 'destructive',
                    });
                  }

                  if (e.target) e.target.value = '';
                }}
                disabled={!isCreateMode && !loadedReturn}
              />
              <Button
                variant="outline"
                onClick={() => attachmentsInputRef.current?.click()}
                className="h-8 flex-1 text-xs border-indigo-300 text-indigo-600 hover:bg-indigo-50 hover:border-indigo-400 disabled:border-gray-200 disabled:text-gray-400 transition-all duration-200"
                disabled={!isCreateMode && !loadedReturn}
              >
                <Upload className="ml-1 h-2 w-2" />
                {attachments.length > 0 ? `${attachments.length}` : '0'}
              </Button>
              {attachments.length > 0 && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setIsAttachmentsModalOpen(true)}
                  className="h-8 w-8 border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
                >
                  <Eye className="h-2 w-2" />
                </Button>
              )}
            </div>
          </div>
          <div className="space-y-2">
            <Label>العميل</Label>
            <Input
              value={clientDetails?.name || 'سيتم تحديده تلقائيًا'}
              disabled
            />
          </div>
          <div className="space-y-2">
            <Label>المخزن المستلم</Label>
            <div className="flex gap-1">
              <Select
                dir="rtl"
                value={formState.warehouseId}
                onValueChange={(val) =>
                  setFormState((s) => ({ ...s, warehouseId: val }))
                }
                disabled={(!canCreate && !canEdit) || (!isCreateMode && !loadedReturn)}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="اختر المخزن" />
                </SelectTrigger>
                <SelectContent>
                {/* عرض المخازن التي لدى المستخدم صلاحية الوصول إليها أولاً */}
                {currentUser?.warehouseAccess && currentUser.warehouseAccess.length > 0 ? (
                  <>
                    <SelectGroup>
                      <SelectLabel>المخازن المتاحة لك</SelectLabel>
                      {warehouses
                        .filter(w => currentUser.warehouseAccess?.includes(w.id))
                        .map((w) => (
                          <SelectItem key={w.id} value={w.id.toString()}>
                            {w.name}
                          </SelectItem>
                        ))}
                    </SelectGroup>
                    
                    {/* عرض باقي المخازن إذا كان هناك مخازن أخرى */}
                    {warehouses.filter(w => !currentUser.warehouseAccess?.includes(w.id)).length > 0 && (
                      <SelectGroup>
                        <SelectLabel>كل المخازن</SelectLabel>
                        {warehouses
                          .filter(w => !currentUser.warehouseAccess?.includes(w.id))
                          .map((w) => (
                            <SelectItem key={w.id} value={w.id.toString()}>
                              {w.name}
                            </SelectItem>
                          ))}
                      </SelectGroup>
                    )}
                  </>
                ) : (
                  /* إذا لم تكن هناك صلاحيات محددة، اعرض جميع المخازن */
                  warehouses.map((w) => (
                    <SelectItem key={w.id} value={w.id.toString()}>
                      {w.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
              </Select>
              {(systemSettings?.defaultWarehouseId || warehouses.length > 0) && !formState.warehouseId && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => {
                    const defaultWarehouseId = determineDefaultWarehouse();
                    if (defaultWarehouseId) {
                      setFormState(prev => ({
                        ...prev,
                        warehouseId: defaultWarehouseId
                      }));
                      
                      const selectedWarehouse = warehouses.find(w => w.id.toString() === defaultWarehouseId);
                      toast({
                        title: 'تم اختيار المخزن الافتراضي',
                        description: `تم اختيار ${selectedWarehouse?.name || 'المخزن الافتراضي'}`
                      });
                    }
                  }}
                  title="اختيار المخزن الافتراضي"
                  className="h-9 w-9"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="m8 12 3 3 5-5"/></svg>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>



      <Card className="enhanced-stocktake-card supplier-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-green-800 dark:text-green-200 flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">2</div>
              <div>
                <div className="font-bold">بحث وإضافة جهاز للإرجاع</div>
                <div className="text-xs text-green-600 dark:text-green-300 font-normal mt-1">البحث عن الأجهزة وإضافتها لأمر الإرجاع</div>
              </div>
            </CardTitle>
            <div className="flex items-center gap-2">
              <Label className="text-xs text-green-700">ملاحظات:</Label>
              <Input
                placeholder="ملاحظات سريعة..."
                value={formState.notes}
                onChange={(e) => setFormState({ ...formState, notes: e.target.value })}
                className="h-6 text-xs w-48 border-green-300 focus:ring-green-200"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="flex gap-2 items-center">
            <div className="flex-1">
              <div className="flex gap-2 items-center">
                <Label className="text-xs text-green-700 min-w-fit">الرقم التسلسلي (IMEI):</Label>
                <Input
                  placeholder={isCreateMode || loadedReturn ? "أدخل 15 رقماً..." : "اضغط 'مرتجع جديد' للبدء"}
                  value={searchState.imei}
                  onChange={(e) =>
                    setSearchState((s) => ({ ...s, imei: e.target.value }))
                  }
                  onKeyDown={(e) => e.key === 'Enter' && handleSearchDevice()}
                  disabled={!isCreateMode && !loadedReturn}
                  className={`font-mono transition-all duration-300 text-gray-900 h-8 text-xs flex-1 ${
                    searchState.imei.length === 15
                      ? 'border-green-500 bg-green-50 shadow-md ring-2 ring-green-200 text-green-900'
                      : searchState.imei.length > 0
                      ? 'border-yellow-500 bg-yellow-50 shadow-sm ring-1 ring-yellow-200 text-yellow-900'
                      : 'hover:border-green-300 focus:ring-2 focus:ring-green-200 bg-white'
                  }`}
                />
                <Button
                  onClick={handleSearchDevice}
                  disabled={!isCreateMode && !loadedReturn}
                  size="sm"
                  className="bg-green-500 hover:bg-green-600 text-white px-3 h-8"
                >
                  <Search className="ml-1 h-3 w-3" /> بحث
                </Button>
              </div>
              <div className={`text-xs font-medium transition-colors duration-200 mt-1 ${
                searchState.imei.length === 15
                  ? 'text-green-600'
                  : searchState.imei.length > 0
                  ? 'text-yellow-600'
                  : 'text-gray-500'
              }`}>
                <span className="inline-flex items-center">
                  {searchState.imei.length === 15 && <span className="mr-1">✓</span>}
                  {searchState.imei.length}/15 رقم
                </span>
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileImport}
              accept=".txt"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              title="استيراد من ملف"
              className="border-green-300 text-green-600 hover:bg-green-50 px-2 h-8"
            >
              <Upload className="h-3 w-3" />
            </Button>
          </div>

          {searchState.foundDevice && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border rounded-md p-4 animate-in fade-in-50">
              <p>
                <span className="font-semibold">الجهاز:</span>{' '}
                {searchState.foundDevice.model}
              </p>
              <p>
                <span className="font-semibold">فاتورة البيع:</span>{' '}
                {searchState.foundSale?.soNumber}
              </p>
              <p>
                <span className="font-semibold">تاريخ البيع:</span>{' '}
                {formatDateTime(searchState.foundSale!.date)}
              </p>
              <p>
                <span className="font-semibold">حالة الضمان:</span>{' '}
                {searchState.warrantyStatus}
              </p>

              <div className="space-y-2">
                <Label>سبب الإرجاع</Label>
                <Select
                  dir="rtl"
                  value={searchState.returnReason}
                  onValueChange={(v: ReturnReason) =>
                    setSearchState((s) => ({ ...s, returnReason: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="خلل مصنعي">خلل مصنعي</SelectItem>
                    <SelectItem value="رغبة العميل">رغبة العميل</SelectItem>
                    <SelectItem value="سبب آخر">سبب آخر</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>نوع العملية</Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant={!searchState.isReplacement ? "default" : "outline"}
                    onClick={() => setSearchState(s => ({ ...s, isReplacement: false, replacementImei: '' }))}
                  >
                    مرتجع عادي
                  </Button>
                  <Button
                    type="button"
                    variant={searchState.isReplacement ? "default" : "outline"}
                    onClick={() => setSearchState(s => ({ ...s, isReplacement: true }))}
                  >
                    استبدال
                  </Button>
                </div>
              </div>

              {searchState.isReplacement && (
                <div className="space-y-2 animate-in fade-in-50">
                  <Label>IMEI الجهاز البديل</Label>
                  <Input
                    placeholder="أدخل IMEI الجهاز البديل"
                    value={searchState.replacementImei}
                    onChange={(e) =>
                      setSearchState((s) => ({
                        ...s,
                        replacementImei: e.target.value,
                      }))
                    }
                  />
                </div>
              )}
              <div className="md:col-span-2">
                {showAcceptWithoutWarrantyBtn ? (
                  <Button onClick={handleAddItemToReturn} variant="secondary">
                    <PlusCircle className="ml-2 h-4 w-4" /> 
                    {searchState.isReplacement ? 'استبدال الجهاز بدون ضمان' : 'قبول جهاز بدون ضمان'}
                  </Button>
                ) : (
                  <Button onClick={handleAddItemToReturn}>
                    <PlusCircle className="ml-2 h-4 w-4" /> 
                    {searchState.isReplacement ? 'تأكيد الاستبدال' : 'إضافة للقائمة'}
                  </Button>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card items-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-950/20 dark:to-cyan-950/20 py-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-teal-800 dark:text-teal-200 flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-teal-500 to-cyan-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">5</div>
              <div>
                <div className="font-bold">الأجهزة في الأمر الحالي</div>
                <div className="text-xs text-teal-600 dark:text-teal-300 font-normal mt-1">قائمة الأجهزة المضافة لأمر الإرجاع</div>
              </div>
            </CardTitle>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="enhanced-badge bg-gradient-to-r from-teal-500 to-cyan-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                📱 {returnItems.length} جهاز
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border max-h-96 overflow-y-auto">
            <Table className="border-collapse border border-gray-300">
              <TableHeader className="sticky top-0 z-10">
                <TableRow className="bg-gradient-to-r from-teal-50 to-cyan-50 border-b-2 border-teal-200">
                  <TableHead className="border border-gray-300 text-center font-bold text-gray-700 bg-teal-200/70 py-2 text-sm w-12">
                    #
                  </TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-teal-100/50 py-2 text-sm">الرقم التسلسلي</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-teal-100/50 py-2 text-sm">الموديل</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-teal-100/50 py-2 text-sm">نوع العملية</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-teal-100/50 py-2 text-sm">سبب الإرجاع</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-teal-100/50 py-2 text-sm">البديل (إن وجد)</TableHead>
                  <TableHead className="border border-gray-300 text-center font-semibold text-gray-700 bg-teal-100/50 py-2 text-sm">إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {returnItems.length === 0 ? (
                  <TableRow className="hover:bg-gray-50">
                    <TableCell colSpan={7} className="h-24 text-center border border-gray-300 text-gray-500 italic">
                      لم يتم إضافة أي أجهزة بعد.
                    </TableCell>
                  </TableRow>
                ) : (
                  returnItems.map((item, index) => (
                    <TableRow
                      key={item.deviceId}
                      className={`
                        hover:bg-teal-50 transition-colors duration-200
                        ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}
                      `}
                    >
                      <TableCell className="border border-gray-300 text-center text-gray-600 font-bold py-2 text-sm w-12 bg-gray-50/50">
                        {index + 1}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right font-mono text-blue-600 font-medium py-2 text-sm" dir="ltr">
                        {item.deviceId}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2 text-sm">
                        {item.model}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2">
                        <Badge variant={item.isReplacement ? "default" : "secondary"} className="text-xs">
                          {item.isReplacement ? 'استبدال' : 'مرتجع عادي'}
                        </Badge>
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2 text-sm">
                        {item.returnReason}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right font-mono py-2 text-sm" dir="ltr">
                        {item.replacementDeviceId || 'N/A'}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-center py-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 transition-all duration-200 rounded-full h-6 w-6"
                          onClick={() => handleRemoveItem(item.deviceId)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card summary-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-950/20 dark:to-blue-950/20 py-4">
          <CardTitle className="text-lg text-indigo-800 dark:text-indigo-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">3</div>
            <div>
              <div className="font-bold">خيارات التصدير والطباعة والحفظ</div>
              <div className="text-xs text-indigo-600 dark:text-indigo-300 font-normal mt-1">تصدير وطباعة وحفظ أمر الإرجاع</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-wrap gap-2 pt-4">
          {/* أزرار التصدير والطباعة */}
          <Button
            variant="outline"
            onClick={() => setIsExportModalOpen(true)}
            disabled={returnItems.length === 0}
            className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 transition-all duration-200"
            title="تصدير مخصص كملف PDF"
          >
            <FileDown className="ml-2 h-3 w-3" />
            PDF مخصص
          </Button>
          <Button
            variant="outline"
            onClick={handleExportPdf}
            disabled={returnItems.length === 0}
            className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 transition-all duration-200"
            title="تصدير سريع كملف PDF"
          >
            <FileDown className="ml-2 h-3 w-3" />
            PDF سريع
          </Button>
          <Button
            variant="outline"
            onClick={handleExportExcel}
            disabled={returnItems.length === 0}
            className="border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
            title="تصدير كملف Excel"
          >
            <FileSpreadsheet className="ml-2 h-3 w-3" />
            Excel
          </Button>
          <Button
            variant="outline"
            onClick={handlePrint}
            disabled={returnItems.length === 0}
            className="border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
            title="طباعة أمر المرتجع"
          >
            <Printer className="ml-2 h-3 w-3" />
            طباعة
          </Button>
          {!loadedReturn && (
            <Button
              variant="outline"
              onClick={saveDraft}
              disabled={returnItems.length === 0 && !formState.clientId}
              className="border-purple-300 text-purple-600 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200"
              title="حفظ مسودة للمتابعة لاحقاً"
            >
              <Save className="ml-2 h-3 w-3" />
              مسودة
            </Button>
          )}

          {/* فاصل */}
          <div className="w-full border-t border-gray-200 my-2"></div>

          {/* أزرار الإلغاء والحفظ */}
          <Button
            variant="destructive"
            onClick={() => setIsCancelAlertOpen(true)}
            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <X className="ml-2 h-4 w-4" /> إلغاء
          </Button>

          {(canCreate || canEdit) ? (
            <Button
              onClick={handleSaveReturn}
              className="px-8 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              size="lg"
            >
              <Save className="ml-2 h-5 w-5" />{' '}
              {loadedReturn ? 'تحديث الأمر' : 'قبول وحفظ المرتجع'}
            </Button>
          ) : (
            <Button
              disabled
              className="px-8 bg-gradient-to-r from-gray-300 to-gray-400 shadow-lg"
              size="lg"
            >
              <Save className="ml-2 h-5 w-5" />
              غير مصرح لك بالحفظ
            </Button>
          )}
        </CardContent>
      </Card>



      {/* تم استبدال النوافذ القديمة بمكون AttachmentsViewer */}
      <AttachmentsViewer
        attachments={attachments}
        isOpen={isAttachmentsModalOpen}
        onClose={() => setIsAttachmentsModalOpen(false)}
        onRemove={(fileName: string) => {
          setAttachments(prev => prev.filter(file => file.fileName !== fileName));
        }}
        canDelete={canCreate || canEdit}
        section="returns"
      />

      {/* تم حذف النوافذ القديمة واستبدالها بـ AttachmentsViewer */}

      {/* نافذة إرسال ملاحظة للإدارة */}
      <Dialog open={!!requestReturn} onOpenChange={(isOpen) => !isOpen && setRequestReturn(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إرسال ملاحظة للإدارة</DialogTitle>
            <DialogDescription>
              يمكنك إرسال ملاحظة أو طلب بخصوص هذا المرتجع للإدارة
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>نوع الطلب</Label>
              <Select
                value={requestFormData.requestType}
                onValueChange={(val) =>
                  setRequestFormData((s) => ({ ...s, requestType: val as EmployeeRequestType }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الطلب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="تعديل">طلب تعديل</SelectItem>
                  <SelectItem value="مراجعة">طلب مراجعة</SelectItem>
                  <SelectItem value="موافقة">طلب موافقة</SelectItem>
                  <SelectItem value="أخرى">أخرى</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>الأهمية</Label>
              <Select
                value={requestFormData.priority}
                onValueChange={(val) =>
                  setRequestFormData((s) => ({ ...s, priority: val as EmployeeRequestPriority }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر مستوى الأهمية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="منخفض">منخفضة</SelectItem>
                  <SelectItem value="عادي">عادية</SelectItem>
                  <SelectItem value="عاجل">عاجلة</SelectItem>
                  <SelectItem value="حرج">حرجة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>تفاصيل المشكلة / الملاحظة</Label>
              <Textarea
                value={requestFormData.notes}
                onChange={(e) =>
                  setRequestFormData((s) => ({ ...s, notes: e.target.value }))
                }
                placeholder="اكتب هنا تفاصيل ملاحظتك أو طلبك..."
                className="min-h-[100px]"
              />
            </div>
            <div className="space-y-2">
              <Label>إرفاق ملف (اختياري)</Label>
              <div className="flex gap-2">
                <Input
                  value={requestFormData.attachmentName}
                  placeholder="اختر ملفًا..."
                  readOnly
                  onClick={() => attachmentInputRef.current?.click()}
                  className="cursor-pointer"
                />
                <input
                  type="file"
                  ref={attachmentInputRef}
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files?.[0]) {
                      setRequestFormData((s) => ({
                        ...s,
                        attachmentName: e.target.files?.[0].name || '',
                      }));
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() =>
                    setRequestFormData((s) => ({ ...s, attachmentName: '' }))
                  }
                  disabled={!requestFormData.attachmentName}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setRequestFormData({
                  requestType: 'تعديل',
                  priority: 'عادي',
                  notes: '',
                  attachmentName: '',
                });
                setRequestReturn(null);
              }}
            >
              إلغاء
            </Button>
            <Button
              onClick={() => {
                if (!requestReturn || !requestFormData.notes) {
                  toast({
                    variant: 'destructive',
                    title: 'خطأ',
                    description: 'يرجى إدخال تفاصيل الملاحظة',
                  });
                  return;
                }

                toast({
                  title: 'تم إرسال الملاحظة',
                  description: 'سيتم إشعار الإدارة بملاحظتك في أقرب وقت.',
                });

                // إعادة ضبط النموذج وإغلاق النافذة
                setRequestFormData({
                  requestType: 'تعديل',
                  priority: 'عادي',
                  notes: '',
                  attachmentName: '',
                });
                setRequestReturn(null);
              }}
            >
              إرسال الملاحظة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نافذة خيارات التصدير */}
      <Dialog open={isExportModalOpen} onOpenChange={setIsExportModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>خيارات التصدير</DialogTitle>
            <DialogDescription>
              حدد الحقول التي تريد تضمينها في التقرير المصدّر
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-returnNumber"
                    checked={exportColumns.returnNumber}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        returnNumber: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-returnNumber">رقم المرتجع</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-date"
                    checked={exportColumns.date}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        date: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-date">تاريخ المرتجع</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-client"
                    checked={exportColumns.client}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        client: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-client">العميل</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-warehouse"
                    checked={exportColumns.warehouse}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        warehouse: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-warehouse">المخزن</Label>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-serialNumber"
                    checked={exportColumns.serialNumber}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        serialNumber: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-serialNumber">الرقم التسلسلي</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-model"
                    checked={exportColumns.model}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        model: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-model">الموديل</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-returnReason"
                    checked={exportColumns.returnReason}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        returnReason: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-returnReason">سبب الإرجاع</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-operationType"
                    checked={exportColumns.operationType}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        operationType: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-operationType">نوع العملية</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-replacementDevice"
                    checked={exportColumns.replacementDevice}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        replacementDevice: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-replacementDevice">الجهاز البديل</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => {
              const doc = new jsPDF();
              doc.setR2L(true);

              const columns = [];
              const columnDefs = [];

              if (exportColumns.returnNumber) {
                columns.push('رقم المرتجع');
                columnDefs.push('roNumber');
              }
              if (exportColumns.date) {
                columns.push('التاريخ');
                columnDefs.push('date');
              }
              if (exportColumns.client) {
                columns.push('العميل');
                columnDefs.push('client');
              }
              if (exportColumns.warehouse) {
                columns.push('المخزن');
                columnDefs.push('warehouse');
              }
              if (exportColumns.serialNumber) {
                columns.push('الرقم التسلسلي');
                columnDefs.push('deviceId');
              }
              if (exportColumns.model) {
                columns.push('الموديل');
                columnDefs.push('model');
              }
              if (exportColumns.returnReason) {
                columns.push('سبب الإرجاع');
                columnDefs.push('returnReason');
              }
              if (exportColumns.operationType) {
                columns.push('نوع العملية');
                columnDefs.push('operationType');
              }
              if (exportColumns.replacementDevice) {
                columns.push('الجهاز البديل');
                columnDefs.push('replacementDeviceId');
              }

              const rows = returnItems.map(item => {
                const rowData = [];
                if (exportColumns.returnNumber) rowData.push(roNumber);
                if (exportColumns.date) rowData.push(formatDateTime(formState.date));
                if (exportColumns.client) rowData.push(clientDetails?.name || '');
                if (exportColumns.warehouse) {
                  const warehouse = warehouses.find(w => w.id.toString() === formState.warehouseId);
                  rowData.push(warehouse?.name || '');
                }
                if (exportColumns.serialNumber) rowData.push(item.deviceId);
                if (exportColumns.model) rowData.push(item.model);
                if (exportColumns.returnReason) rowData.push(item.returnReason);
                if (exportColumns.operationType) rowData.push(item.isReplacement ? 'استبدال' : 'مرتجع عادي');
                if (exportColumns.replacementDevice) rowData.push(item.replacementDeviceId || 'N/A');
                return rowData;
              });

              doc.setFontSize(20);
              doc.text('تقرير أمر المرتجع المخصص', 190, 20, { align: 'right' });
              doc.setFontSize(12);
              doc.text(`تاريخ التقرير: ${formatDateTime(new Date())}`, 190, 30, { align: 'right' });
              doc.text(`رقم أمر المرتجع: ${roNumber}`, 190, 38, { align: 'right' });

              autoTable(doc, {
                startY: 45,
                head: [columns],
                body: rows,
                styles: { font: 'Helvetica', halign: 'right' },
                headStyles: { halign: 'center', fillColor: [44, 51, 51] },
              });

              doc.save(`تقرير-مرتجع-مخصص-${roNumber}.pdf`);
              setIsExportModalOpen(false);

              toast({
                title: 'تم التصدير',
                description: 'تم تصدير التقرير المخصص بنجاح.',
              });
            }}>
              <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
            </Button>
            <Button variant="outline" onClick={() => setIsExportModalOpen(false)}>
              إلغاء
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isLoadOrderDialogOpen}
        onOpenChange={setIsLoadOrderDialogOpen}
      >
        <DialogContent className="enhanced-dialog sm:max-w-4xl max-h-[80vh]">
          <DialogHeader className="enhanced-dialog-header">
            <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
              <FolderOpen className="h-5 w-5 text-primary" />
              <span>عرض أوامر المرتجعات السابقة</span>
            </DialogTitle>
            <DialogDescription className="enhanced-dialog-description">
              اختر أمر مرتجع لتحميل بياناته وتعديله من القائمة أدناه
            </DialogDescription>
          </DialogHeader>
          <div className="enhanced-scroll-area p-4">
            <Table className="enhanced-modal-table">
              <TableHeader className="sticky top-0 z-10">
                <TableRow className="bg-gradient-to-r from-red-50 to-pink-50 border-b-2 border-red-200">
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-red-100/90 py-3 text-sm">📋 رقم الأمر</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-red-100/90 py-3 text-sm">🧾 فاتورة البيع</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-red-100/90 py-3 text-sm">👤 العميل</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-red-100/90 py-3 text-sm">📅 التاريخ</TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-red-100/90 py-3 text-sm">⚙️ إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {returns.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>{order.roNumber}</TableCell>
                    <TableCell>{order.soNumber}</TableCell>
                    <TableCell>{order.clientName}</TableCell>
                    <TableCell>
                      {formatDateTime(order.date)}
                    </TableCell>
                    <TableCell className="flex gap-1">
                      <Button size="sm" onClick={() => handleLoadReturn(order)}>
                        تحميل
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        title="هل أنت متأكد من الحذف؟"
        description="هذا الإجراء سيحذف أمر المرتجع ويعيد حالة الأجهزة إلى ما كانت عليه قبل الإرجاع. لا يمكن التراجع عن هذا الإجراء."
        onConfirm={handleDeleteReturn}
        confirmText="متابعة الحذف"
        cancelText="تراجع"
      >
        <Button
          variant="destructive"
          onClick={() => setOrderToDelete(loadedReturn)}
          disabled={!loadedReturn}
        >
          <Trash className="ml-2 h-4 w-4" /> حذف الأمر
        </Button>
      </ConfirmationDialog>

      <AlertDialog open={isCancelAlertOpen} onOpenChange={setIsCancelAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الإلغاء؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم فقدان جميع البيانات غير المحفوظة في هذا الأمر.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancel}
              className="bg-destructive hover:bg-destructive/90"
            >
              تأكيد الإلغاء
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}