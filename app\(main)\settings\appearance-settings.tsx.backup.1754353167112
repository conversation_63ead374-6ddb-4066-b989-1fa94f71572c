'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Save, ImageIcon, Eye, FileText, Download, Palette } from 'lucide-react';
import { SystemSettings } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { apiClient } from '@/lib/api-client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// تعريف واجهة إعدادات التقارير المحسنة
interface ReportSettings {
  showLogo: boolean;
  logoPosition: 'left' | 'center' | 'right';
  logoSize: 'small' | 'medium' | 'large' | 'custom';
  customLogoSize?: {
    width: string;
    height: string;
  };
  companyName: {
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
    position: 'below-logo' | 'right-logo' | 'left-logo' | 'above-logo';
  };
  companyDetails: {
    enabled: boolean;
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
  };
  address: {
    enabled: boolean;
    text: string;
    fontSize: 'small' | 'medium' | 'large' | 'custom';
    customFontSize?: string;
    color: string;
  };
  qrCode: {
    enabled: boolean;
    type: 'whatsapp' | 'website' | 'custom';
    value: string;
    size: 'small' | 'medium' | 'large';
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
  headerStyle: 'minimal' | 'detailed' | 'corporate';
  footerStyle: 'simple' | 'detailed' | 'branded';
  colorScheme: 'default' | 'blue' | 'green' | 'purple' | 'custom';
  fontSize: 'small' | 'medium' | 'large';
  pageOrientation: 'portrait' | 'landscape';
  language: 'ar' | 'en' | 'both';
  includeTimestamp: boolean;
  includeWatermark: boolean;
  watermarkText: string;
  customColors: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

// إعدادات التقارير الافتراضية
const defaultReportSettings: ReportSettings = {
  showLogo: true,
  logoPosition: 'center',
  logoSize: 'medium',
  companyName: {
    text: '',
    fontSize: 'large',
    color: '#1a202c',
    position: 'below-logo'
  },
  companyDetails: {
    enabled: true,
    text: '',
    fontSize: 'medium',
    color: '#4a5568'
  },
  address: {
    enabled: true,
    text: '',
    fontSize: 'small',
    color: '#718096'
  },
  qrCode: {
    enabled: false,
    type: 'whatsapp',
    value: '',
    size: 'medium',
    position: 'top-right'
  },
  headerStyle: 'corporate',
  footerStyle: 'detailed',
  colorScheme: 'default',
  fontSize: 'medium',
  pageOrientation: 'portrait',
  language: 'both',
  includeTimestamp: true,
  includeWatermark: false,
  watermarkText: '',
  customColors: {
    primary: '#4299e1',
    secondary: '#2b6cb0',
    accent: '#6366f1'
  }
};

export default function AppearanceSettings() {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [localSettings, setLocalSettings] = useState<SystemSettings | null>(null);
  const [reportSettings, setReportSettings] = useState<ReportSettings>(defaultReportSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const logoRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // جلب الإعدادات عند تحميل المكون
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await apiClient.get('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
        setLocalSettings(data);
        
        // جلب إعدادات التقارير إذا كانت موجودة
        if (data.reportSettings) {
          setReportSettings({ ...defaultReportSettings, ...data.reportSettings });
        }
      } else {
        throw new Error('فشل في جلب الإعدادات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الإعدادات',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setLocalSettings(prev => prev ? { ...prev, [name]: value } : null);
  };

  const handleReportSettingChange = (key: keyof ReportSettings, value: any) => {
    setReportSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleColorChange = (colorKey: keyof ReportSettings['customColors'], value: string) => {
    setReportSettings(prev => ({
      ...prev,
      customColors: { ...prev.customColors, [colorKey]: value }
    }));
  };

  const handleLogoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // رفع الملف عبر API
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiClient.post('/api/attachments', formData);

      if (response.ok) {
        const result = await response.json();
        setLocalSettings(prev => prev ? { ...prev, logoUrl: result.url } : null);
        toast({
          title: 'تم رفع الشعار',
          description: 'تم رفع الشعار بنجاح',
        });
      } else {
        throw new Error('فشل في رفع الشعار');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في رفع الشعار',
        variant: 'destructive',
      });
    }
  };

  const handleSave = async () => {
    if (!localSettings) return;

    setIsSaving(true);
    try {
      // دمج إعدادات التقارير مع الإعدادات العامة
      const settingsToSave = {
        ...localSettings,
        reportSettings: reportSettings
      };

      const response = await apiClient.put('/api/settings', settingsToSave);

      if (response.ok) {
        const result = await response.json();
        setSettings(result.data);
        toast({
          title: 'تم الحفظ',
          description: 'تم حفظ الإعدادات بنجاح',
        });
      } else {
        throw new Error('فشل في حفظ الإعدادات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في حفظ الإعدادات',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const generatePreviewReport = () => {
    if (!localSettings) return;

    // إنشاء بيانات تجريبية للمعاينة
    const sampleDeviceInfo = {
      id: 'DEV-2024-001',
      model: 'iPhone 15 Pro Max',
      status: 'في المخزن',
      lastSale: {
        clientName: 'أحمد محمد علي',
        soNumber: 'SO-2024-001',
        opNumber: 'OP-2024-001',
        date: '2024-01-15'
      },
      warrantyInfo: {
        status: 'ضمان ساري المفعول',
        expiryDate: '2025-01-15',
        remaining: '11 شهراً و 20 يوماً'
      }
    };

    const sampleTimelineEvents = [
      {
        id: '1',
        title: 'تم استلام الجهاز',
        description: 'تم استلام الجهاز من المورد وفحصه وإدخاله في المخزن',
        date: '2024-01-01',
        formattedDate: '1 يناير 2024',
        user: 'موظف الاستلام',
        type: 'supply'
      },
      {
        id: '2',
        title: 'تم بيع الجهاز',
        description: 'تم بيع الجهاز للعميل أحمد محمد علي مع ضمان سنة كاملة',
        date: '2024-01-15',
        formattedDate: '15 يناير 2024',
        user: 'موظف المبيعات',
        type: 'sale'
      }
    ];

    // إنشاء معاينة HTML مع الإعدادات المحدثة
    const previewHTML = generatePreviewHTML(localSettings, reportSettings, sampleDeviceInfo, sampleTimelineEvents);
    
    // فتح المعاينة في نافذة جديدة
    const previewWindow = window.open('', '_blank');
    if (previewWindow) {
      previewWindow.document.write(previewHTML);
      previewWindow.document.close();
    }
  };

  const downloadSampleReport = async () => {
    if (!localSettings) return;

    try {
      // استيراد وظيفة التصدير المحسنة
      const { exportDeviceTrackingReportHTML } = await import('@/lib/export-utils/enhanced-html-export');
      
      // بيانات تجريبية
      const sampleDeviceInfo = {
        id: 'DEV-PREVIEW-001',
        model: 'iPhone 15 Pro Max (معاينة)',
        status: 'عينة للمعاينة',
        lastSale: {
          clientName: 'عميل تجريبي',
          soNumber: 'SO-PREVIEW-001',
          opNumber: 'OP-PREVIEW-001',
          date: new Date().toISOString()
        },
        warrantyInfo: {
          status: 'ضمان تجريبي',
          expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          remaining: '12 شهراً'
        }
      };

      const sampleTimelineEvents = [
        {
          id: '1',
          title: 'مثال على حدث',
          description: 'هذا مثال على كيفية ظهور الأحداث في التقرير',
          date: new Date().toISOString(),
          formattedDate: new Date().toLocaleDateString('ar-EG'),
          user: 'مستخدم تجريبي',
          type: 'sample'
        }
      ];

      await exportDeviceTrackingReportHTML(
        sampleDeviceInfo,
        sampleTimelineEvents,
        {
          fileName: 'نموذج-معاينة-التقرير',
          isCustomerView: false,
          action: 'download',
          language: reportSettings.language,
          systemSettings: localSettings,
          reportSettings: reportSettings
        }
      );

      toast({
        title: 'تم تحميل النموذج',
        description: 'تم تحميل نموذج التقرير بالإعدادات الحالية',
      });
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في تحميل النموذج',
        variant: 'destructive',
      });
    }
  };

  // دالة إنشاء معاينة HTML
  const generatePreviewHTML = (
    settings: SystemSettings,
    reportSettings: ReportSettings,
    deviceInfo: any,
    timelineEvents: any[]
  ) => {
    const colorSchemes = {
      default: { primary: '#4299e1', secondary: '#2b6cb0', accent: '#6366f1' },
      blue: { primary: '#3b82f6', secondary: '#1d4ed8', accent: '#60a5fa' },
      green: { primary: '#10b981', secondary: '#047857', accent: '#34d399' },
      purple: { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#a78bfa' },
      custom: reportSettings.customColors
    };

    const currentColors = colorSchemes[reportSettings.colorScheme];
    const isRTL = reportSettings.language === 'ar' || reportSettings.language === 'both';

    return `
      <!DOCTYPE html>
      <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${reportSettings.language === 'en' ? 'en' : 'ar'}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>معاينة التقرير</title>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
            font-size: ${reportSettings.fontSize === 'small' ? '12px' : reportSettings.fontSize === 'large' ? '16px' : '14px'};
            line-height: 1.6;
            color: #1a202c;
            background: #f7fafc;
          }
          .preview-container {
            max-width: ${reportSettings.pageOrientation === 'landscape' ? '1024px' : '800px'};
            margin: 20px auto;
            background: white;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
          }
          .header {
            background: linear-gradient(135deg, ${currentColors.primary}, ${currentColors.secondary});
            color: white;
            padding: 30px;
            text-align: ${reportSettings.logoPosition};
          }
          .header.minimal { padding: 15px; }
          .header.detailed { padding: 40px; }
          .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: ${reportSettings.logoPosition === 'center' ? '0 auto 20px' : reportSettings.logoPosition === 'right' ? '0 0 20px auto' : '0 auto 20px 0'};
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: ${currentColors.primary};
          }
          .company-name {
            font-size: ${reportSettings.headerStyle === 'corporate' ? '28px' : '24px'};
            font-weight: 700;
            margin-bottom: 10px;
          }
          .company-details {
            opacity: 0.9;
            font-size: ${reportSettings.headerStyle === 'detailed' ? '14px' : '12px'};
          }
          .content {
            padding: 30px;
          }
          .report-title {
            background: ${currentColors.accent}20;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid ${currentColors.primary};
            margin-bottom: 30px;
          }
          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }
          .info-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
          }
          .info-card h3 {
            color: ${currentColors.primary};
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
          }
          .timeline {
            margin-top: 30px;
          }
          .timeline-item {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
          }
          .timeline-marker {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: ${currentColors.primary};
            margin-top: 6px;
            flex-shrink: 0;
          }
          .footer {
            background: #f8fafc;
            padding: ${reportSettings.footerStyle === 'detailed' ? '30px' : '20px'};
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
          }
          .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 60px;
            color: rgba(0,0,0,0.05);
            z-index: 1;
            pointer-events: none;
          }
          .timestamp {
            font-size: 11px;
            color: #94a3b8;
            margin-top: 10px;
          }
        </style>
      </head>
      <body>
        ${reportSettings.includeWatermark && reportSettings.watermarkText ? 
          `<div class="watermark">${reportSettings.watermarkText}</div>` : ''
        }
        <div class="preview-container">
          <header class="header ${reportSettings.headerStyle}">
            ${reportSettings.showLogo ? 
              `<div class="logo">${settings.logoUrl ? `<img src="${settings.logoUrl}" alt="Logo" style="width:100%;height:100%;object-fit:cover;border-radius:50%;">` : '🏢'}</div>` : ''
            }
            <h1 class="company-name">
              ${reportSettings.language === 'en' ? settings.companyNameEn : reportSettings.language === 'ar' ? settings.companyNameAr : `${settings.companyNameAr} - ${settings.companyNameEn}`}
            </h1>
            <div class="company-details">
              ${reportSettings.language === 'en' ? settings.addressEn : reportSettings.language === 'ar' ? settings.addressAr : `${settings.addressAr} | ${settings.addressEn}`}
              <br>
              📞 ${settings.phone} | ✉️ ${settings.email} | 🌐 ${settings.website}
            </div>
          </header>

          <main class="content">
            <div class="report-title">
              <h2>تقرير تتبع الجهاز - معاينة</h2>
              <p>رقم الجهاز: ${deviceInfo.id} | الموديل: ${deviceInfo.model}</p>
            </div>

            <div class="info-grid">
              <div class="info-card">
                <h3>📱 معلومات الجهاز</h3>
                <p><strong>الموديل:</strong> ${deviceInfo.model}</p>
                <p><strong>الحالة:</strong> ${deviceInfo.status}</p>
                <p><strong>الرقم التسلسلي:</strong> ${deviceInfo.id}</p>
              </div>

              <div class="info-card">
                <h3>💰 معلومات البيع</h3>
                <p><strong>العميل:</strong> ${deviceInfo.lastSale?.clientName || 'غير محدد'}</p>
                <p><strong>رقم الفاتورة:</strong> ${deviceInfo.lastSale?.soNumber || 'غير محدد'}</p>
                <p><strong>تاريخ البيع:</strong> ${deviceInfo.lastSale?.date ? new Date(deviceInfo.lastSale.date).toLocaleDateString('ar-EG') : 'غير محدد'}</p>
              </div>

              <div class="info-card">
                <h3>🛡️ معلومات الضمان</h3>
                <p><strong>حالة الضمان:</strong> ${deviceInfo.warrantyInfo?.status || 'غير محدد'}</p>
                <p><strong>تاريخ الانتهاء:</strong> ${deviceInfo.warrantyInfo?.expiryDate ? new Date(deviceInfo.warrantyInfo.expiryDate).toLocaleDateString('ar-EG') : 'غير محدد'}</p>
                <p><strong>المدة المتبقية:</strong> ${deviceInfo.warrantyInfo?.remaining || 'غير محدد'}</p>
              </div>
            </div>

            <div class="timeline">
              <h3 style="color: ${currentColors.primary}; margin-bottom: 20px;">📋 سجل الأحداث</h3>
              ${timelineEvents.map(event => `
                <div class="timeline-item">
                  <div class="timeline-marker"></div>
                  <div>
                    <h4 style="color: ${currentColors.secondary};">${event.title}</h4>
                    <p style="margin: 5px 0;">${event.description}</p>
                    <small style="color: #64748b;">${event.formattedDate} - بواسطة: ${event.user}</small>
                  </div>
                </div>
              `).join('')}
            </div>
          </main>

          <footer class="footer">
            <div>
              ${reportSettings.language === 'en' ? settings.footerTextEn : reportSettings.language === 'ar' ? settings.footerTextAr : `${settings.footerTextAr} | ${settings.footerTextEn}`}
            </div>
            ${reportSettings.includeTimestamp ? 
              `<div class="timestamp">تم إنشاء التقرير في: ${new Date().toLocaleString('ar-EG')}</div>` : ''
            }
          </footer>
        </div>
      </body>
      </html>
    `;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">جاري التحميل...</div>
        </CardContent>
      </Card>
    );
  };

  if (!localSettings) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">فشل في تحميل الإعدادات</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* معلومات الشركة الأساسية */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات الشركة الأساسية</CardTitle>
          <CardDescription>
            تُستخدم هذه البيانات في ترويسة وتذييل جميع المستندات والتقارير
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* شعار الشركة */}
          <div className="flex items-center gap-6">
            <Avatar className="h-24 w-24">
              <AvatarImage src={localSettings.logoUrl} alt="Logo" />
              <AvatarFallback><ImageIcon /></AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <Label>شعار الشركة</Label>
              <Input
                type="file"
                className="hidden"
                ref={logoRef}
                accept="image/*"
                onChange={handleLogoChange}
              />
              <Button
                variant="outline"
                onClick={() => logoRef.current?.click()}
              >
                تغيير الشعار
              </Button>
            </div>
          </div>

          {/* عمودان، عربى وإنجليزى */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* عربي */}
            <div className="space-y-4 rtl text-right">
              <h3 className="text-lg font-semibold">البيانات العربية</h3>
              <div className="space-y-2">
                <Label>اسم الشركة (عربى)</Label>
                <Input
                  name="companyNameAr"
                  value={localSettings.companyNameAr}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label>العنوان (عربى)</Label>
                <Input
                  name="addressAr"
                  value={localSettings.addressAr}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label>تذييل (عربى)</Label>
                <Textarea
                  name="footerTextAr"
                  value={localSettings.footerTextAr}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* English */}
            <div className="space-y-4 ltr text-left">
              <h3 className="text-lg font-semibold">English Data</h3>
              <div className="space-y-2">
                <Label>Company Name (EN)</Label>
                <Input
                  name="companyNameEn"
                  value={localSettings.companyNameEn}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label>Address (EN)</Label>
                <Input
                  name="addressEn"
                  value={localSettings.addressEn}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label>Footer (EN)</Label>
                <Textarea
                  name="footerTextEn"
                  value={localSettings.footerTextEn}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          {/* معلومات اتصال مشتركة */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label>رقم الهاتف</Label>
              <Input
                name="phone"
                value={localSettings.phone}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label>البريد الإلكترونى</Label>
              <Input
                name="email"
                type="email"
                value={localSettings.email}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label>الموقع</Label>
              <Input
                name="website"
                value={localSettings.website}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إعدادات التصميم المتقدمة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            إعدادات التصميم المتقدمة
          </CardTitle>
          <CardDescription>
            تحكم كامل في مظهر وتصميم التقارير
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* تصميم اسم الشركة */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold border-b pb-2">تصميم اسم الشركة</h4>
            
            <div className="grid md:grid-cols-2 gap-4">
              {/* نص اسم الشركة */}
              <div className="space-y-2">
                <Label>نص اسم الشركة للتقارير</Label>
                <Input
                  value={reportSettings.companyName.text}
                  onChange={(e) => setReportSettings(prev => ({
                    ...prev,
                    companyName: { ...prev.companyName, text: e.target.value }
                  }))}
                  placeholder="اسم الشركة"
                />
              </div>

              {/* موقع اسم الشركة */}
              <div className="space-y-2">
                <Label>موقع اسم الشركة</Label>
                <Select
                  value={reportSettings.companyName.position}
                  onValueChange={(value: 'below-logo' | 'right-logo' | 'left-logo' | 'above-logo') => 
                    setReportSettings(prev => ({
                      ...prev,
                      companyName: { ...prev.companyName, position: value }
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="above-logo">أعلى الشعار</SelectItem>
                    <SelectItem value="below-logo">أسفل الشعار</SelectItem>
                    <SelectItem value="right-logo">يمين الشعار</SelectItem>
                    <SelectItem value="left-logo">يسار الشعار</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* حجم خط اسم الشركة */}
              <div className="space-y-2">
                <Label>حجم خط اسم الشركة</Label>
                <Select
                  value={reportSettings.companyName.fontSize}
                  onValueChange={(value: 'small' | 'medium' | 'large' | 'custom') => 
                    setReportSettings(prev => ({
                      ...prev,
                      companyName: { ...prev.companyName, fontSize: value }
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">صغير</SelectItem>
                    <SelectItem value="medium">متوسط</SelectItem>
                    <SelectItem value="large">كبير</SelectItem>
                    <SelectItem value="custom">مخصص</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* حجم خط مخصص */}
              {reportSettings.companyName.fontSize === 'custom' && (
                <div className="space-y-2">
                  <Label>حجم الخط المخصص (px)</Label>
                  <Input
                    value={reportSettings.companyName.customFontSize || ''}
                    onChange={(e) => setReportSettings(prev => ({
                      ...prev,
                      companyName: { ...prev.companyName, customFontSize: e.target.value }
                    }))}
                    placeholder="24"
                  />
                </div>
              )}

              {/* لون اسم الشركة */}
              <div className="space-y-2">
                <Label>لون اسم الشركة</Label>
                <Input
                  type="color"
                  value={reportSettings.companyName.color}
                  onChange={(e) => setReportSettings(prev => ({
                    ...prev,
                    companyName: { ...prev.companyName, color: e.target.value }
                  }))}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* تفاصيل الشركة */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold border-b pb-2">تفاصيل الشركة</h4>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                checked={reportSettings.companyDetails.enabled}
                onCheckedChange={(checked) => setReportSettings(prev => ({
                  ...prev,
                  companyDetails: { ...prev.companyDetails, enabled: checked }
                }))}
              />
              <Label>إظهار تفاصيل الشركة</Label>
            </div>

            {reportSettings.companyDetails.enabled && (
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>نص تفاصيل الشركة</Label>
                  <Textarea
                    value={reportSettings.companyDetails.text}
                    onChange={(e) => setReportSettings(prev => ({
                      ...prev,
                      companyDetails: { ...prev.companyDetails, text: e.target.value }
                    }))}
                    placeholder="مثال: لخدمات الشحن والتصدير"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label>حجم خط التفاصيل</Label>
                  <Select
                    value={reportSettings.companyDetails.fontSize}
                    onValueChange={(value: 'small' | 'medium' | 'large' | 'custom') => 
                      setReportSettings(prev => ({
                        ...prev,
                        companyDetails: { ...prev.companyDetails, fontSize: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">صغير</SelectItem>
                      <SelectItem value="medium">متوسط</SelectItem>
                      <SelectItem value="large">كبير</SelectItem>
                      <SelectItem value="custom">مخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {reportSettings.companyDetails.fontSize === 'custom' && (
                  <div className="space-y-2">
                    <Label>حجم الخط المخصص (px)</Label>
                    <Input
                      value={reportSettings.companyDetails.customFontSize || ''}
                      onChange={(e) => setReportSettings(prev => ({
                        ...prev,
                        companyDetails: { ...prev.companyDetails, customFontSize: e.target.value }
                      }))}
                      placeholder="16"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label>لون النص</Label>
                  <Input
                    type="color"
                    value={reportSettings.companyDetails.color}
                    onChange={(e) => setReportSettings(prev => ({
                      ...prev,
                      companyDetails: { ...prev.companyDetails, color: e.target.value }
                    }))}
                  />
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* عنوان الشركة */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold border-b pb-2">عنوان الشركة</h4>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                checked={reportSettings.address.enabled}
                onCheckedChange={(checked) => setReportSettings(prev => ({
                  ...prev,
                  address: { ...prev.address, enabled: checked }
                }))}
              />
              <Label>إظهار عنوان الشركة</Label>
            </div>

            {reportSettings.address.enabled && (
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>نص العنوان</Label>
                  <Textarea
                    value={reportSettings.address.text}
                    onChange={(e) => setReportSettings(prev => ({
                      ...prev,
                      address: { ...prev.address, text: e.target.value }
                    }))}
                    placeholder="الرياض، المملكة العربية السعودية"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label>حجم خط العنوان</Label>
                  <Select
                    value={reportSettings.address.fontSize}
                    onValueChange={(value: 'small' | 'medium' | 'large' | 'custom') => 
                      setReportSettings(prev => ({
                        ...prev,
                        address: { ...prev.address, fontSize: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">صغير</SelectItem>
                      <SelectItem value="medium">متوسط</SelectItem>
                      <SelectItem value="large">كبير</SelectItem>
                      <SelectItem value="custom">مخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {reportSettings.address.fontSize === 'custom' && (
                  <div className="space-y-2">
                    <Label>حجم الخط المخصص (px)</Label>
                    <Input
                      value={reportSettings.address.customFontSize || ''}
                      onChange={(e) => setReportSettings(prev => ({
                        ...prev,
                        address: { ...prev.address, customFontSize: e.target.value }
                      }))}
                      placeholder="14"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label>لون النص</Label>
                  <Input
                    type="color"
                    value={reportSettings.address.color}
                    onChange={(e) => setReportSettings(prev => ({
                      ...prev,
                      address: { ...prev.address, color: e.target.value }
                    }))}
                  />
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* حجم الشعار */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold border-b pb-2">حجم الشعار</h4>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>حجم الشعار</Label>
                <Select
                  value={reportSettings.logoSize}
                  onValueChange={(value: 'small' | 'medium' | 'large' | 'custom') => 
                    setReportSettings(prev => ({ ...prev, logoSize: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">صغير (40px)</SelectItem>
                    <SelectItem value="medium">متوسط (60px)</SelectItem>
                    <SelectItem value="large">كبير (80px)</SelectItem>
                    <SelectItem value="custom">مخصص</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {reportSettings.logoSize === 'custom' && (
                <>
                  <div className="space-y-2">
                    <Label>العرض (px)</Label>
                    <Input
                      value={reportSettings.customLogoSize?.width || ''}
                      onChange={(e) => setReportSettings(prev => ({
                        ...prev,
                        customLogoSize: { 
                          ...prev.customLogoSize, 
                          width: e.target.value,
                          height: prev.customLogoSize?.height || ''
                        }
                      }))}
                      placeholder="60"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>الارتفاع (px)</Label>
                    <Input
                      value={reportSettings.customLogoSize?.height || ''}
                      onChange={(e) => setReportSettings(prev => ({
                        ...prev,
                        customLogoSize: { 
                          ...prev.customLogoSize, 
                          height: e.target.value,
                          width: prev.customLogoSize?.width || ''
                        }
                      }))}
                      placeholder="60"
                    />
                  </div>
                </>
              )}
            </div>
          </div>

          <Separator />

          {/* QR Code */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold border-b pb-2">رمز الاستجابة السريعة (QR Code)</h4>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <Switch
                checked={reportSettings.qrCode.enabled}
                onCheckedChange={(checked) => setReportSettings(prev => ({
                  ...prev,
                  qrCode: { ...prev.qrCode, enabled: checked }
                }))}
              />
              <Label>إضافة رمز QR</Label>
            </div>

            {reportSettings.qrCode.enabled && (
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>نوع الرمز</Label>
                  <Select
                    value={reportSettings.qrCode.type}
                    onValueChange={(value: 'whatsapp' | 'website' | 'custom') => 
                      setReportSettings(prev => ({
                        ...prev,
                        qrCode: { ...prev.qrCode, type: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="whatsapp">رقم واتساب</SelectItem>
                      <SelectItem value="website">موقع الويب</SelectItem>
                      <SelectItem value="custom">مخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>
                    {reportSettings.qrCode.type === 'whatsapp' ? 'رقم الواتساب' : 
                     reportSettings.qrCode.type === 'website' ? 'رابط الموقع' : 'القيمة المخصصة'}
                  </Label>
                  <Input
                    value={reportSettings.qrCode.value}
                    onChange={(e) => setReportSettings(prev => ({
                      ...prev,
                      qrCode: { ...prev.qrCode, value: e.target.value }
                    }))}
                    placeholder={
                      reportSettings.qrCode.type === 'whatsapp' ? '+966501234567' : 
                      reportSettings.qrCode.type === 'website' ? 'https://company.com' : 'أي نص أو رابط'
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>حجم الرمز</Label>
                  <Select
                    value={reportSettings.qrCode.size}
                    onValueChange={(value: 'small' | 'medium' | 'large') => 
                      setReportSettings(prev => ({
                        ...prev,
                        qrCode: { ...prev.qrCode, size: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">صغير (50px)</SelectItem>
                      <SelectItem value="medium">متوسط (80px)</SelectItem>
                      <SelectItem value="large">كبير (120px)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>موقع الرمز</Label>
                  <Select
                    value={reportSettings.qrCode.position}
                    onValueChange={(value: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left') => 
                      setReportSettings(prev => ({
                        ...prev,
                        qrCode: { ...prev.qrCode, position: value }
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="top-right">أعلى اليمين</SelectItem>
                      <SelectItem value="top-left">أعلى اليسار</SelectItem>
                      <SelectItem value="bottom-right">أسفل اليمين</SelectItem>
                      <SelectItem value="bottom-left">أسفل اليسار</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* إعدادات التقارير المتقدمة */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            إعدادات التقارير المتقدمة
          </CardTitle>
          <CardDescription>
            تخصيص مظهر وتنسيق التقارير المصدَّرة
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* إعدادات أساسية */}
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>موضع الشعار</Label>
              <Select 
                value={reportSettings.logoPosition} 
                onValueChange={(value: 'left' | 'center' | 'right') => 
                  handleReportSettingChange('logoPosition', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="left">يسار</SelectItem>
                  <SelectItem value="center">وسط</SelectItem>
                  <SelectItem value="right">يمين</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>نمط الترويسة</Label>
              <Select 
                value={reportSettings.headerStyle} 
                onValueChange={(value: 'minimal' | 'detailed' | 'corporate') => 
                  handleReportSettingChange('headerStyle', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="minimal">بسيط</SelectItem>
                  <SelectItem value="detailed">مفصل</SelectItem>
                  <SelectItem value="corporate">مؤسسي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>نمط التذييل</Label>
              <Select 
                value={reportSettings.footerStyle} 
                onValueChange={(value: 'simple' | 'detailed' | 'branded') => 
                  handleReportSettingChange('footerStyle', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simple">بسيط</SelectItem>
                  <SelectItem value="detailed">مفصل</SelectItem>
                  <SelectItem value="branded">بالشعار</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* إعدادات المظهر */}
          <div className="grid md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>نظام الألوان</Label>
              <Select 
                value={reportSettings.colorScheme} 
                onValueChange={(value: 'default' | 'blue' | 'green' | 'purple' | 'custom') => 
                  handleReportSettingChange('colorScheme', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">افتراضي</SelectItem>
                  <SelectItem value="blue">أزرق</SelectItem>
                  <SelectItem value="green">أخضر</SelectItem>
                  <SelectItem value="purple">بنفسجي</SelectItem>
                  <SelectItem value="custom">مخصص</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>حجم الخط</Label>
              <Select 
                value={reportSettings.fontSize} 
                onValueChange={(value: 'small' | 'medium' | 'large') => 
                  handleReportSettingChange('fontSize', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">صغير</SelectItem>
                  <SelectItem value="medium">متوسط</SelectItem>
                  <SelectItem value="large">كبير</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>اتجاه الصفحة</Label>
              <Select 
                value={reportSettings.pageOrientation} 
                onValueChange={(value: 'portrait' | 'landscape') => 
                  handleReportSettingChange('pageOrientation', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="portrait">طولي</SelectItem>
                  <SelectItem value="landscape">عرضي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>اللغة</Label>
              <Select 
                value={reportSettings.language} 
                onValueChange={(value: 'ar' | 'en' | 'both') => 
                  handleReportSettingChange('language', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ar">عربي</SelectItem>
                  <SelectItem value="en">إنجليزي</SelectItem>
                  <SelectItem value="both">كلاهما</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* ألوان مخصصة */}
          {reportSettings.colorScheme === 'custom' && (
            <div className="p-4 border rounded-lg space-y-4">
              <h4 className="font-medium">الألوان المخصصة</h4>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>اللون الأساسي</Label>
                  <Input
                    type="color"
                    value={reportSettings.customColors.primary}
                    onChange={(e) => handleColorChange('primary', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>اللون الثانوي</Label>
                  <Input
                    type="color"
                    value={reportSettings.customColors.secondary}
                    onChange={(e) => handleColorChange('secondary', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>لون التمييز</Label>
                  <Input
                    type="color"
                    value={reportSettings.customColors.accent}
                    onChange={(e) => handleColorChange('accent', e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          <Separator />

          {/* خيارات إضافية */}
          <div className="space-y-4">
            <h4 className="font-medium">خيارات إضافية</h4>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>إظهار الشعار</Label>
                <p className="text-sm text-muted-foreground">عرض شعار الشركة في التقارير</p>
              </div>
              <Switch
                checked={reportSettings.showLogo}
                onCheckedChange={(checked) => handleReportSettingChange('showLogo', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>الطابع الزمني</Label>
                <p className="text-sm text-muted-foreground">إضافة تاريخ ووقت الإنشاء</p>
              </div>
              <Switch
                checked={reportSettings.includeTimestamp}
                onCheckedChange={(checked) => handleReportSettingChange('includeTimestamp', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>العلامة المائية</Label>
                <p className="text-sm text-muted-foreground">إضافة علامة مائية للتقارير</p>
              </div>
              <Switch
                checked={reportSettings.includeWatermark}
                onCheckedChange={(checked) => handleReportSettingChange('includeWatermark', checked)}
              />
            </div>

            {reportSettings.includeWatermark && (
              <div className="space-y-2">
                <Label>نص العلامة المائية</Label>
                <Input
                  value={reportSettings.watermarkText}
                  onChange={(e) => handleReportSettingChange('watermarkText', e.target.value)}
                  placeholder="أدخل نص العلامة المائية"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* أزرار الإجراءات */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            معاينة وتحميل النموذج
          </CardTitle>
          <CardDescription>
            اختبر الإعدادات الحالية وقم بتحميل نموذج تجريبي
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              onClick={generatePreviewReport}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              معاينة النموذج
            </Button>

            <Button
              variant="outline"
              onClick={downloadSampleReport}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              تحميل نموذج تجريبي
            </Button>

            <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  معاينة متقدمة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>معاينة التقرير بالإعدادات الحالية</DialogTitle>
                  <DialogDescription>
                    هذا مثال على كيفية ظهور التقارير بالإعدادات المحددة
                  </DialogDescription>
                </DialogHeader>
                <div 
                  className="border rounded-lg overflow-hidden"
                  dangerouslySetInnerHTML={{
                    __html: localSettings ? generatePreviewHTML(
                      localSettings,
                      reportSettings,
                      {
                        id: 'DEV-PREVIEW-001',
                        model: 'iPhone 15 Pro Max',
                        status: 'في المخزن',
                        lastSale: { clientName: 'عميل تجريبي', soNumber: 'SO-001', date: new Date().toISOString() },
                        warrantyInfo: { status: 'ضمان ساري', expiryDate: new Date().toISOString(), remaining: '12 شهراً' }
                      },
                      [{ id: 1, title: 'حدث تجريبي', description: 'وصف الحدث', formattedDate: 'اليوم', user: 'المستخدم' }]
                    ) : ''
                  }}
                />
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>

        <CardFooter className="justify-end">
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="ml-2 h-4 w-4" />
            {isSaving ? 'جاري الحفظ...' : 'حفظ جميع الإعدادات'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}