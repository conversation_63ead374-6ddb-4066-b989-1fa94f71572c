{"timestamp": "2025-08-05T00:16:43.315Z", "totalFixes": 198, "filesModified": ["app/api/returns/route.ts", "app/api/evaluations/route.ts", "app/api/delivery-orders/route.ts", "app/api/escalation/overdue/route.ts", "app/api/notifications/check-overdue/route.ts", "app/api/response-templates/route.ts", "app/api/response-templates/suggest/route.ts", "app/api/search/quick/route.ts", "app/api/search/requests/route.ts", "app/api/upload/route.ts", "app/api/archive/route.ts"], "totalFilesProcessed": 13, "fixes": [{"search": {}, "replace": "function safeToISOString(dateValue: Date | string | null): string | null", "description": "تحسين نوع معامل safeToISOString"}, {"search": {}, "replace": "const sanitizeDate = (dateStr: Date | string | null): string | null", "description": "تحسين نوع معامل sanitizeDate"}, {"search": {}, "replace": "function getOverdueTimeText(priority: string): string", "description": "تحسين دالة getOverdueTimeText"}, {"search": {}, "replace": "function getOverdueTime(priority: string): string", "description": "تحسين دالة getOverdueTime"}, {"search": {}, "replace": "date: Date;", "description": "تحويل date إلى Date في الواجهات"}, {"search": {}, "replace": "processedDate: Date;", "description": "تحويل processedDate إلى Date"}, {"search": {}, "replace": "requestDate: Date;", "description": "تحويل requestDate إلى Date"}, {"search": {}, "replace": "dateFrom?: Date;", "description": "تحويل dateFrom إلى Date"}, {"search": {}, "replace": "dateTo?: Date;", "description": "تحويل dateTo إلى Date"}, {"search": {}, "replace": "requestDate: request.requestDate", "description": "إزالة toISOString غير الضرورية من requestDate"}, {"search": {}, "replace": "repairDate: repairDate", "description": "إزالة toISOString غير الضرورية من repairDate"}, {"search": {}, "replace": "createdAt: template.createdAt", "description": "إزالة toISOString غير الضرورية من createdAt"}, {"search": {}, "replace": "updatedAt: template.updatedAt", "description": "إزالة toISOString غير الضرورية من updatedAt"}, {"search": {}, "replace": "uploadedAt: stats.mtime", "description": "إزالة toISOString غير الضرورية من uploadedAt"}, {"search": {}, "replace": "date: newReceipt.date || new Date()", "description": "استخدام Date object مباشرة"}, {"search": {}, "replace": "timestamp: new Date()", "description": "استخدام Date object مباشرة للـ timestamp"}, {"search": {}, "replace": "cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);", "description": "تحسين عمليات التاريخ"}, {"search": {}, "replace": "const timestamp = new Date().toISOString().replace(/[:.]/g, '-');", "description": "إصلاح regex للـ timestamp"}]}