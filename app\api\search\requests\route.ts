import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

interface SearchFilters {
  status?: string[];
  priority?: string[];
  requestType?: string[];
  employeeName?: string;
  dateFrom?: Date;
  dateTo?: Date;
  tags?: string[];
  hasAttachments?: boolean;
  isArchived?: boolean;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // استخراج معاملات البحث
    const query = searchParams.get('q') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    // بناء الفلاتر
    const filters: SearchFilters = {};
    
    // فلاتر متعددة القيم
    const status = searchParams.getAll('status');
    if (status.length > 0) filters.status = status;
    
    const priority = searchParams.getAll('priority');
    if (priority.length > 0) filters.priority = priority;
    
    const requestType = searchParams.getAll('requestType');
    if (requestType.length > 0) filters.requestType = requestType;
    
    // فلاتر مفردة
    const employeeName = searchParams.get('employeeName');
    if (employeeName) filters.employeeName = employeeName;
    
    const dateFrom = searchParams.get('dateFrom');
    if (dateFrom) filters.dateFrom = dateFrom;
    
    const dateTo = searchParams.get('dateTo');
    if (dateTo) filters.dateTo = dateTo;
    
    const hasAttachments = searchParams.get('hasAttachments');
    if (hasAttachments !== null) filters.hasAttachments = hasAttachments === 'true';
    
    const isArchived = searchParams.get('isArchived');
    if (isArchived !== null) filters.isArchived = isArchived === 'true';
    
    // تنفيذ البحث مباشرة
    const offset = (page - 1) * limit;

    // بناء شروط البحث
    const whereConditions: any = {
      isArchived: filters.isArchived || false
    };

    // فلترة حسب الحالة
    if (filters.status && filters.status.length > 0) {
      whereConditions.status = { in: filters.status };
    }

    // فلترة حسب الأولوية
    if (filters.priority && filters.priority.length > 0) {
      whereConditions.priority = { in: filters.priority };
    }

    // فلترة حسب نوع الطلب
    if (filters.requestType && filters.requestType.length > 0) {
      whereConditions.requestType = { in: filters.requestType };
    }

    // فلترة حسب اسم الموظف
    if (filters.employeeName) {
      whereConditions.employeeName = {
        contains: filters.employeeName,
        mode: 'insensitive'
      };
    }

    // فلترة حسب التاريخ
    if (filters.dateFrom || filters.dateTo) {
      whereConditions.requestDate = {};
      if (filters.dateFrom) {
        whereConditions.requestDate.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        whereConditions.requestDate.lte = new Date(filters.dateTo);
      }
    }

    // البحث النصي
    if (query && query.trim()) {
      const searchTerms = query.trim().split(/\s+/);
      const searchConditions = searchTerms.map(term => ({
        OR: [
          { requestNumber: { contains: term, mode: 'insensitive' } },
          { employeeName: { contains: term, mode: 'insensitive' } },
          { notes: { contains: term, mode: 'insensitive' } },
          { adminNotes: { contains: term, mode: 'insensitive' } },
          { requestType: { contains: term, mode: 'insensitive' } }
        ]
      }));

      if (whereConditions.AND) {
        whereConditions.AND.push({ AND: searchConditions });
      } else {
        whereConditions.AND = searchConditions;
      }
    }

    // تنفيذ البحث
    const [results, total] = await Promise.all([
      prisma.employeeRequest.findMany({
        where: whereConditions,
        orderBy: [
          { priority: 'desc' },
          { requestDate: 'desc' }
        ],
        skip: offset,
        take: limit
      }),
      prisma.employeeRequest.count({
        where: whereConditions
      })
    ]);

    const processedResults = results.map(request => ({
      id: request.id,
      requestNumber: request.requestNumber,
      employeeName: request.employeeName,
      requestType: request.requestType,
      priority: request.priority,
      status: request.status,
      requestDate: request.requestDate,
      notes: request.notes,
      adminNotes: request.adminNotes || undefined,
      attachments: [],
      tags: request.tags as string[] || []
    }));

    return NextResponse.json({
      results: processedResults,
      total,
      hasMore: offset + results.length < total
    });
  } catch (error) {
    console.error('خطأ في البحث:', error);
    return NextResponse.json({ error: 'Search failed' }, { status: 500 });
  }
}
