{"timestamp": "2025-08-04T23:57:36.209Z", "totalFixes": 70, "filesModified": ["app/api/supply/route.ts", "app/api/maintenance-orders/route.ts", "app/api/evaluations/route.ts", "app/api/maintenance-logs/route.ts", "app/api/devices/route.ts", "app/api/supply-batch/route.ts", "app/api/upload/route.ts"], "fixes": [{"search": {}, "replace": "new Date()", "description": "إزالة toISOString() غير الضرورية"}, {"search": {}, "replace": "date: new Date()", "description": "استخدام Date object مباشرة للتاريخ"}, {"search": {}, "replace": "createdAt: new Date()", "description": "استخدام Date object مباشرة للـ createdAt"}, {"search": {}, "replace": "requestDate: new Date()", "description": "استخدام Date object مباشرة للـ requestDate"}, {"search": {}, "replace": "processedDate: new Date()", "description": "استخدام Date object مباشرة للـ processedDate"}, {"search": {}, "replace": "sentDate: new Date()", "description": "استخدام Date object مباشرة للـ sentDate"}, {"search": {}, "replace": "repairDate: new Date()", "description": "استخدام Date object مباشرة للـ repairDate"}, {"search": {}, "replace": "acknowledgedDate: new Date()", "description": "استخدام Date object مباشرة للـ acknowledgedDate"}, {"search": {}, "replace": "timestamp: new Date()", "description": "استخدام Date object مباشرة للـ timestamp"}, {"search": {}, "replace": "uploadedAt: new Date()", "description": "استخدام Date object مباشرة للـ uploadedAt"}]}