/**
 * Fix Split Errors Urgent Script
 * Date: 2025-08-04
 * Description: Fix all new Date().split() errors immediately
 */

const fs = require('fs');
const path = require('path');

// Search for files with split errors
function findSplitErrors() {
  const results = [];
  
  function searchInFile(filePath) {
    if (!fs.existsSync(filePath)) return [];
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];
    
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      // Check for new Date().split() patterns
      if (line.includes('new Date().split(')) {
        issues.push({
          file: filePath,
          line: lineNumber,
          content: line.trim(),
          type: 'new Date().split()'
        });
      }
    });
    
    return issues;
  }
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (item !== 'node_modules' && item !== '.next' && item !== '.git') {
          walkDir(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
          const fileIssues = searchInFile(fullPath);
          results.push(...fileIssues);
        }
      }
    }
  }
  
  walkDir(process.cwd());
  return results;
}

// Fixes for split errors
const splitErrorFixes = [
  {
    search: /new Date\(\)\.split\('T'\)\[0\]/g,
    replace: 'new Date().toISOString().split(\'T\')[0]',
    description: 'إصلاح new Date().split(\'T\')[0] إلى new Date().toISOString().split(\'T\')[0]'
  },
  {
    search: /new Date\(\)\.split\('T'\)\[1\]/g,
    replace: 'new Date().toISOString().split(\'T\')[1]',
    description: 'إصلاح new Date().split(\'T\')[1] إلى new Date().toISOString().split(\'T\')[1]'
  },
  {
    search: /new Date\(\)\.split\(/g,
    replace: 'new Date().toISOString().split(',
    description: 'إصلاح new Date().split( إلى new Date().toISOString().split('
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixSplitErrors() {
  console.log('🚨 البحث عن أخطاء split وإصلاحها...\n');

  try {
    // Find all split errors
    console.log('🔍 البحث عن أخطاء split...');
    const splitErrors = findSplitErrors();
    
    if (splitErrors.length === 0) {
      console.log('✅ لم يتم العثور على أخطاء split');
      return;
    }
    
    console.log(`🚨 تم العثور على ${splitErrors.length} خطأ split:\n`);
    
    // Display errors
    splitErrors.forEach(error => {
      console.log(`📁 ${path.relative(process.cwd(), error.file)}`);
      console.log(`   🔴 السطر ${error.line}: ${error.content}`);
      console.log('');
    });
    
    // Get unique files
    const uniqueFiles = [...new Set(splitErrors.map(error => error.file))];
    
    console.log(`🔧 إصلاح ${uniqueFiles.length} ملف...\n`);
    
    let totalFixed = 0;
    let filesModified = [];
    
    for (const file of uniqueFiles) {
      console.log(`🔍 فحص: ${path.basename(file)}`);
      
      if (applyFixes(file, splitErrorFixes)) {
        totalFixed += splitErrorFixes.length;
        filesModified.push(file);
      }
    }
    
    // Generate summary
    console.log('\n📊 ملخص إصلاح أخطاء split:');
    console.log('='.repeat(40));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`🚨 أخطاء split المكتشفة: ${splitErrors.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }
    
    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      splitErrors: splitErrors,
      fixes: splitErrorFixes
    };
    
    fs.writeFileSync('split-errors-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح split في: split-errors-fixes-report.json');
    
    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح جميع أخطاء split بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على أخطاء split للإصلاح');
    }
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح أخطاء split:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixSplitErrors()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح أخطاء split');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح أخطاء split:', error);
      process.exit(1);
    });
}

module.exports = { fixSplitErrors };
