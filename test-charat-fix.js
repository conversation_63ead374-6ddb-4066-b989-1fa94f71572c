/**
 * اختبار إصلاح خطأ charAt - Fix charAt Error Test
 * تاريخ: 4 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testCharAtFix() {
  console.log('🔧 اختبار إصلاح خطأ charAt...\n');
  
  try {
    const token = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin
    
    // 1. إنشاء تعليق تجريبي لاختبار API
    console.log('1️⃣ إنشاء تعليق تجريبي...');
    
    // التأكد من وجود طلب للاختبار
    const existingRequests = await prisma.employeeRequest.findMany({ take: 1 });
    
    if (existingRequests.length === 0) {
      console.log('   ⚠️ لا توجد طلبات موظفين للاختبار');
      console.log('   📝 إنشاء طلب تجريبي...');
      
      const testRequest = await prisma.employeeRequest.create({
        data: {
          requestNumber: `TEST-${Date.now()}`,
          requestType: 'طلب اختبار',
          priority: 'متوسط',
          notes: 'طلب تجريبي لاختبار التعليقات',
          status: 'قيد المراجعة',
          requestDate: new Date().toISOString(),
          employeeName: 'موظف تجريبي',
          employeeId: 1,
          attachments: [],
          tags: ['test'],
          isArchived: false
        }
      });
      
      console.log(`   ✅ تم إنشاء طلب تجريبي: ID=${testRequest.id}`);
    }
    
    const testRequestId = existingRequests[0]?.id || 1;
    
    // 2. اختبار إضافة تعليق
    console.log('\n2️⃣ اختبار إضافة تعليق...');
    
    try {
      const response = await fetch(`http://localhost:9005/api/employee-requests/${testRequestId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          comment: 'تعليق تجريبي لاختبار إصلاح خطأ charAt',
          commentType: 'comment',
          isInternal: false
        })
      });
      
      if (response.ok) {
        const commentData = await response.json();
        console.log(`   ✅ تم إنشاء تعليق تجريبي: ID=${commentData.id}`);
        console.log(`   📝 المستخدم: ${commentData.userName || 'غير محدد'}`);
        console.log(`   🔑 الدور: ${commentData.userRole || 'غير محدد'}`);
        
        // حذف التعليق التجريبي
        await prisma.$queryRaw`DELETE FROM "request_comments" WHERE id = ${commentData.id}`;
        console.log('   ✅ تم حذف التعليق التجريبي');
        
      } else {
        const errorText = await response.text();
        console.log(`   ❌ فشل في إنشاء التعليق: ${response.status} - ${errorText.substring(0, 100)}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ في اختبار إضافة التعليق: ${error.message}`);
    }
    
    // 3. اختبار جلب التعليقات
    console.log('\n3️⃣ اختبار جلب التعليقات...');
    
    try {
      const response = await fetch(`http://localhost:9005/api/employee-requests/${testRequestId}/comments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const comments = await response.json();
        console.log(`   ✅ تم جلب ${Array.isArray(comments) ? comments.length : 0} تعليق`);
        
        if (Array.isArray(comments) && comments.length > 0) {
          const firstComment = comments[0];
          console.log(`   📝 أول تعليق:`);
          console.log(`      - ID: ${firstComment.id}`);
          console.log(`      - المستخدم: ${firstComment.userName || 'غير محدد'}`);
          console.log(`      - الدور: ${firstComment.userRole || 'غير محدد'}`);
          console.log(`      - النص: ${firstComment.comment ? firstComment.comment.substring(0, 50) + '...' : 'فارغ'}`);
          
          // التحقق من أن userName موجود وليس undefined
          if (firstComment.userName && typeof firstComment.userName === 'string' && firstComment.userName.length > 0) {
            const firstChar = firstComment.userName.charAt(0);
            console.log(`      - أول حرف: ${firstChar} ✅`);
          } else {
            console.log(`      - ⚠️ userName غير محدد أو فارغ`);
          }
        }
        
      } else {
        console.log(`   ❌ فشل في جلب التعليقات: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ خطأ في اختبار جلب التعليقات: ${error.message}`);
    }
    
    // 4. فحص بيانات المستخدمين
    console.log('\n4️⃣ فحص بيانات المستخدمين...');
    
    try {
      const users = await prisma.user.findMany({
        select: { id: true, username: true, name: true, role: true }
      });
      
      console.log(`   👥 عدد المستخدمين: ${users.length}`);
      users.forEach(user => {
        console.log(`   - ID: ${user.id}, Username: ${user.username || 'فارغ'}, Name: ${user.name || 'فارغ'}, Role: ${user.role}`);
      });
      
    } catch (error) {
      console.log(`   ❌ خطأ في فحص المستخدمين: ${error.message}`);
    }
    
    // 5. ملخص الاختبار
    console.log('\n📊 ملخص الاختبار:');
    console.log('================================');
    console.log('✅ تم إصلاح خطأ charAt في RequestConversation component');
    console.log('✅ تم تحديث API لإرجاع معلومات المستخدم');
    console.log('✅ تم اختبار إنشاء وجلب التعليقات');
    
    console.log('\n🔄 الخطوات التالية:');
    console.log('1. أعد تحميل صفحة طلبات الموظفين في المتصفح');
    console.log('2. جرب إضافة تعليق على طلب موجود');
    console.log('3. تحقق من عدم ظهور خطأ charAt مرة أخرى');
    
  } catch (error) {
    console.error('❌ خطأ عام في اختبار الإصلاح:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الاختبار
testCharAtFix();
