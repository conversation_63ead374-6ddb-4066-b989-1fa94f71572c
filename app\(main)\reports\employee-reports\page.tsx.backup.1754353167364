'use client';

import { useMemo } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Printer, FileDown } from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings, User, Sale } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc.setFontSize(16).text(settings.companyName, 190, 15, { align: 'right' });
      doc.setFontSize(10).text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc.setFontSize(8).text(`صفحة ${data.pageNumber} من ${pageCount - 1}`, data.settings.margin.left, doc.internal.pageSize.height - 10);
      if (settings.reportFooter) {
        doc.text(settings.reportFooter, 195, doc.internal.pageSize.height - 10, { align: 'right' });
      }
    };
    return { addHeader, addFooter };
};

type EmployeeStats = {
  user: User;
  supplyOrdersCount: number;
  evaluatedDevicesCount: number;
  salesCount: number;
  returnsCount: number;
};

export default function EmployeeReports() {
  const { users, systemSettings, supplyOrders, evaluationOrders, sales, returns } = useStore();

  const employeeStats = useMemo((): EmployeeStats[] => {
    return users.map(user => {
      const supplyOrdersCount = supplyOrders.filter(o => o.employeeName === user.name).length;
      const evaluatedDevicesCount = evaluationOrders
        .filter(o => o.employeeName === user.name)
        .reduce((sum, o) => sum + (Array.isArray(o.items) ? o.items.length : 0), 0);
      
      // Since sales do not have an employee name, we can't accurately track them per employee yet.
      // This is a placeholder. For a real implementation, employeeName should be added to the Sale type.
      const salesCount = sales.length / users.length; // Simplified placeholder logic
      
      // Same for returns
      const returnsCount = returns.filter(r => r.processedBy === user.name).length;

      return {
        user,
        supplyOrdersCount,
        evaluatedDevicesCount,
        salesCount: Math.round(salesCount), // Placeholder
        returnsCount,
      };
    });
  }, [users, supplyOrders, evaluationOrders, sales, returns]);


  const handleExport = (action: 'print' | 'download') => {
    if (employeeStats.length === 0) return;
    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();
    
    const title = 'تقرير إنتاجية الموظفين';
    doc.setFontSize(20).text(title, 190, 45, { align: 'right' });
    
    const head = [['المرتجعات', 'المبيعات', 'التقييم', 'التوريد', 'الحالة', 'اسم الموظف']];
    const body = employeeStats.map(stat => [
        stat.returnsCount,
        stat.salesCount,
        stat.evaluatedDevicesCount,
        stat.supplyOrdersCount,
        stat.user.status || 'Active',
        stat.user.name,
    ]);
    
    autoTable(doc, {
        startY: 55,
        head,
        body,
        theme: 'grid',
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
        didDrawPage: addFooter,
    });

    if (action === 'print') {
        doc.output('dataurlnewwindow');
    } else {
        doc.save(`employee_productivity_report_${new Date().toISOString().slice(0, 10)}.pdf`);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>تقرير إنتاجية الموظفين</CardTitle>
          <CardDescription>
            عرض لإنتاجية الموظفين في العمليات المختلفة المسجلة في النظام.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>اسم الموظف</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>أوامر التوريد</TableHead>
                <TableHead>أجهزة تم تقييمها</TableHead>
                <TableHead>فواتير مبيعات (تقديري)</TableHead>
                <TableHead>أوامر مرتجعات</TableHead>
                <TableHead>آخر دخول</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {employeeStats.map((stat) => (
                <TableRow key={stat.user.id}>
                  <TableCell className="font-medium">{stat.user.name}</TableCell>
                   <TableCell>
                    <Badge variant={stat.user.status === 'Active' ? 'default' : 'destructive'}>
                      {stat.user.status || 'Active'}
                    </Badge>
                  </TableCell>
                  <TableCell>{stat.supplyOrdersCount}</TableCell>
                  <TableCell>{stat.evaluatedDevicesCount}</TableCell>
                  <TableCell>{stat.salesCount}</TableCell>
                  <TableCell>{stat.returnsCount}</TableCell>
                  <TableCell>
                    {stat.user.lastLogin ? new Date(stat.user.lastLogin).toLocaleString('ar-EG') : 'لم يسجل دخول'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex gap-2">
            <Button variant="outline" onClick={() => handleExport('print')}>
                <Printer className="ml-2 h-4 w-4" /> طباعة التقرير
            </Button>
            <Button variant="outline" onClick={() => handleExport('download')}>
                <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
            </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
