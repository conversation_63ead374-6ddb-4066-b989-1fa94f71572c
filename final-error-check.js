/**
 * Final Error Check Script
 * Date: 2025-08-04
 * Description: Check for any remaining JavaScript errors in date handling
 */

const fs = require('fs');
const path = require('path');

// Common error patterns to check for
const errorPatterns = [
  {
    pattern: /new Date\([^)]*\)\.slice/g,
    description: 'استخدام .slice() على Date object مباشرة',
    severity: 'critical'
  },
  {
    pattern: /new Date\([^)]*\)\.split/g,
    description: 'استخدام .split() على Date object مباشرة',
    severity: 'critical'
  },
  {
    pattern: /\{new Date\([^}]*\)\}/g,
    description: 'عرض Date object مباشرة في JSX',
    severity: 'high'
  },
  {
    pattern: /dateTimeString/g,
    description: 'متغير dateTimeString غير معرف',
    severity: 'critical'
  },
  {
    pattern: /dateString/g,
    description: 'متغير dateString محتمل غير معرف',
    severity: 'medium'
  },
  {
    pattern: /const formatDateTime = \([^)]*\): string => \{[^}]*dateTimeString/g,
    description: 'دالة formatDateTime محلية تستخدم متغير غير معرف',
    severity: 'critical'
  }
];

function checkFile(filePath) {
  if (!fs.existsSync(filePath)) return [];
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];
  
  errorPatterns.forEach(errorPattern => {
    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      
      if (errorPattern.pattern.test(line)) {
        // Skip comments
        if (line.trim().startsWith('//') || line.trim().startsWith('*')) return;
        
        issues.push({
          file: filePath,
          line: lineNumber,
          content: line.trim(),
          description: errorPattern.description,
          severity: errorPattern.severity,
          pattern: errorPattern.pattern.source
        });
      }
    });
  });
  
  return issues;
}

function checkDirectory(dirPath) {
  const issues = [];
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (item !== 'node_modules' && item !== '.next' && item !== '.git') {
          walkDir(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
          const fileIssues = checkFile(fullPath);
          issues.push(...fileIssues);
        }
      }
    }
  }
  
  walkDir(dirPath);
  return issues;
}

async function finalErrorCheck() {
  console.log('🔍 فحص نهائي للأخطاء المحتملة...\n');
  
  try {
    const issues = checkDirectory(process.cwd());
    
    if (issues.length === 0) {
      console.log('✅ لم يتم العثور على أخطاء محتملة - النظام نظيف!');
      return;
    }
    
    // Group by severity
    const critical = issues.filter(i => i.severity === 'critical');
    const high = issues.filter(i => i.severity === 'high');
    const medium = issues.filter(i => i.severity === 'medium');
    
    console.log(`🚨 تم العثور على ${issues.length} مشكلة محتملة:\n`);
    
    if (critical.length > 0) {
      console.log(`🔴 مشاكل حرجة (${critical.length}):`);
      console.log('='.repeat(40));
      critical.forEach(issue => {
        console.log(`📁 ${path.relative(process.cwd(), issue.file)}`);
        console.log(`   السطر ${issue.line}: ${issue.description}`);
        console.log(`   الكود: ${issue.content}`);
        console.log('');
      });
    }
    
    if (high.length > 0) {
      console.log(`🟠 مشاكل عالية الأولوية (${high.length}):`);
      console.log('='.repeat(40));
      high.forEach(issue => {
        console.log(`📁 ${path.relative(process.cwd(), issue.file)}`);
        console.log(`   السطر ${issue.line}: ${issue.description}`);
        console.log(`   الكود: ${issue.content}`);
        console.log('');
      });
    }
    
    if (medium.length > 0) {
      console.log(`🟡 مشاكل متوسطة الأولوية (${medium.length}):`);
      console.log('='.repeat(40));
      medium.slice(0, 10).forEach(issue => {
        console.log(`📁 ${path.relative(process.cwd(), issue.file)}`);
        console.log(`   السطر ${issue.line}: ${issue.description}`);
        console.log(`   الكود: ${issue.content}`);
        console.log('');
      });
      
      if (medium.length > 10) {
        console.log(`... و ${medium.length - 10} مشكلة أخرى متوسطة الأولوية`);
      }
    }
    
    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalIssues: issues.length,
      critical: critical.length,
      high: high.length,
      medium: medium.length,
      issues: issues
    };
    
    fs.writeFileSync('final-error-check-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير الفحص النهائي في: final-error-check-report.json');
    
    // Summary
    console.log('\n📊 ملخص الفحص النهائي:');
    console.log('='.repeat(30));
    console.log(`🔴 مشاكل حرجة: ${critical.length}`);
    console.log(`🟠 مشاكل عالية: ${high.length}`);
    console.log(`🟡 مشاكل متوسطة: ${medium.length}`);
    console.log(`📊 إجمالي: ${issues.length}`);
    
    if (critical.length > 0) {
      console.log('\n⚠️ يوجد مشاكل حرجة تحتاج إصلاح فوري!');
    } else if (high.length > 0) {
      console.log('\n⚠️ يوجد مشاكل عالية الأولوية تحتاج إصلاح.');
    } else {
      console.log('\n✅ لا توجد مشاكل حرجة - النظام مستقر!');
    }
    
  } catch (error) {
    console.error('❌ خطأ في الفحص النهائي:', error);
    throw error;
  }
}

// Run check
if (require.main === module) {
  finalErrorCheck()
    .then(() => {
      console.log('\n✅ تم الانتهاء من الفحص النهائي');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في الفحص النهائي:', error);
      process.exit(1);
    });
}

module.exports = { finalErrorCheck };
