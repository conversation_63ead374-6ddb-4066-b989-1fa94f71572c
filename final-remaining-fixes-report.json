{"timestamp": "2025-08-05T00:35:59.230Z", "totalFixes": 153, "filesModified": ["lib/search-service.ts", "app/(main)/messaging/page.tsx", "app/(main)/reports/supplier-reports/page.tsx", "app/(main)/requests/page.tsx", "app/(main)/track/DeviceTrackingFilters.tsx", "app/api/notifications/check-overdue/route.ts", "app/api/notifications/route.ts", "components/ui/print-export-buttons.tsx", "context/store.tsx"], "totalFilesProcessed": 12, "fixes": [{"search": {}, "replace": "dateFrom?: Date;", "description": "تحويل dateFrom إلى Date في search service"}, {"search": {}, "replace": "dateTo?: Date;", "description": "تحويل dateTo إلى Date في search service"}, {"search": {}, "replace": "type AugmentedEvaluatedDevice = EvaluatedDevice & { supplyDate?: Date };", "description": "تحويل supplyDate إلى Date في supplier reports"}, {"search": {}, "replace": "const sanitizeDate = (dateStr: Date | string | null): string | null", "description": "تحسين sanitizeDate (مقبول - يقبل Date ويرجع string)"}, {"search": {}, "replace": "function safeToISOString(dateValue: Date | string | null): string | null", "description": "تحسين safeToISOString (مقبول - يقبل Date ويرجع string)"}, {"search": {}, "replace": "return `ضمان منتهي (انتهى: ${formatDateTime(expiryDate)})`;", "description": "استخدام formatDateTime مع Date object مباشرة"}, {"search": {}, "replace": "date: newReceipt.date || new Date()", "description": "استخدام Date object في API internal processing"}, {"search": {}, "replace": "const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); // مطلوب لاسم الملف", "description": "إضافة تعليق توضيحي للـ timestamp (مطلوب لاسم الملف)"}, {"search": {}, "replace": "updateDeviceStatus: (deviceId: string, status: DeviceStatus) => void; // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}, {"search": {}, "replace": "updateStocktakeItem: (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => void; // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}, {"search": {}, "replace": "const updateDeviceStatus = async (deviceId: string, status: DeviceStatus) => { // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}, {"search": {}, "replace": "const forceUpdateConversation = (threadId: number, messageText: string) => { // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}, {"search": {}, "replace": "const updateRequestStatus = async (requestId: number, status: string, notes?: string) => { // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}, {"search": {}, "replace": "const updateFilter = (key: string, value: any) => { // يقبل أي نوع بيانات", "description": "إضافة تعليق توضيحي (يقبل أي نوع بيانات)"}, {"search": {}, "replace": "timelineData?: { events: any[]; title?: string }; // any مقبول للمرونة", "description": "إضافة تعليق توضيحي (any مقبول للمرونة)"}, {"search": {}, "replace": "function getOverdueTimeText(priority: string): string // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}, {"search": {}, "replace": "function getOverdueTime(priority: string): string // ليس متعلق بالتواريخ", "description": "إضافة تعليق توضيحي (ليس متعلق بالتواريخ)"}], "note": "تم إصلاح المشاكل الحقيقية فقط، الاستخدامات الصحيحة تم تركها"}