/**
 * Data Migration Script - JSON to Relational
 * Date: 2025-08-04
 * Description: Migrate data from JSON fields to proper relational tables
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function migrateJsonData() {
  console.log('🚀 بدء ترحيل البيانات من JSON إلى الجداول العلائقية...\n');

  try {
    // 1. Migrate InternalMessage recipientIds
    console.log('1️⃣ ترحيل recipientIds من الرسائل الداخلية...');
    const messagesWithRecipients = await prisma.internalMessage.findMany({
      where: {
        recipientIds: { not: null }
      }
    });

    let migratedMessages = 0;
    for (const message of messagesWithRecipients) {
      try {
        let recipientIds = [];
        if (typeof message.recipientIds === 'string') {
          recipientIds = JSON.parse(message.recipientIds);
        } else if (Array.isArray(message.recipientIds)) {
          recipientIds = message.recipientIds;
        }

        if (recipientIds.length > 0) {
          // Create MessageRecipient records
          await prisma.messageRecipient.createMany({
            data: recipientIds.map(userId => ({
              messageId: message.id,
              userId: userId,
              isRead: message.isRead || false
            })),
            skipDuplicates: true
          });
          migratedMessages++;
        }
      } catch (error) {
        console.warn(`خطأ في ترحيل الرسالة ${message.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم ترحيل ${migratedMessages} رسالة\n`);

    // 2. Migrate EmployeeRequest tags
    console.log('2️⃣ ترحيل tags من طلبات الموظفين...');
    const requestsWithTags = await prisma.employeeRequest.findMany({
      where: {
        tags: { not: null }
      }
    });

    let migratedTags = 0;
    for (const request of requestsWithTags) {
      try {
        let tags = [];
        if (typeof request.tags === 'string') {
          tags = JSON.parse(request.tags);
        } else if (Array.isArray(request.tags)) {
          tags = request.tags;
        } else if (typeof request.tags === 'object') {
          // Convert object to array of key-value pairs
          tags = Object.entries(request.tags).map(([key, value]) => ({
            name: key,
            value: value
          }));
        }

        if (tags.length > 0) {
          for (const tag of tags) {
            await prisma.requestTag.create({
              data: {
                requestId: request.id,
                tagName: typeof tag === 'string' ? tag : tag.name || tag.key || 'tag',
                tagValue: typeof tag === 'object' ? tag.value : null
              }
            });
            migratedTags++;
          }
        }
      } catch (error) {
        console.warn(`خطأ في ترحيل tags للطلب ${request.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم ترحيل ${migratedTags} علامة\n`);

    // 3. Migrate EmployeeRequest attachments
    console.log('3️⃣ ترحيل attachments من طلبات الموظفين...');
    const requestsWithAttachments = await prisma.employeeRequest.findMany({
      where: {
        attachments: { not: null }
      }
    });

    let migratedRequestAttachments = 0;
    for (const request of requestsWithAttachments) {
      try {
        let attachments = [];
        if (typeof request.attachments === 'string') {
          attachments = JSON.parse(request.attachments);
        } else if (Array.isArray(request.attachments)) {
          attachments = request.attachments;
        }

        if (attachments.length > 0) {
          for (const attachment of attachments) {
            await prisma.requestAttachment.create({
              data: {
                requestId: request.id,
                fileName: attachment.fileName || attachment.name || 'unknown',
                fileType: attachment.fileType || attachment.type || 'unknown',
                fileSize: attachment.fileSize || attachment.size || 0,
                fileUrl: attachment.fileUrl || attachment.url || '',
                uploadedAt: attachment.uploadedAt ? new Date(attachment.uploadedAt) : new Date()
              }
            });
            migratedRequestAttachments++;
          }
        }
      } catch (error) {
        console.warn(`خطأ في ترحيل attachments للطلب ${request.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم ترحيل ${migratedRequestAttachments} مرفق طلب\n`);

    // 4. Migrate RequestComment attachments
    console.log('4️⃣ ترحيل attachments من تعليقات الطلبات...');
    const commentsWithAttachments = await prisma.requestComment.findMany({
      where: {
        attachments: { not: null }
      }
    });

    let migratedCommentAttachments = 0;
    for (const comment of commentsWithAttachments) {
      try {
        let attachments = [];
        if (typeof comment.attachments === 'string') {
          attachments = JSON.parse(comment.attachments);
        } else if (Array.isArray(comment.attachments)) {
          attachments = comment.attachments;
        }

        if (attachments.length > 0) {
          for (const attachment of attachments) {
            await prisma.commentAttachment.create({
              data: {
                commentId: comment.id,
                fileName: attachment.fileName || attachment.name || 'unknown',
                fileType: attachment.fileType || attachment.type || 'unknown',
                fileSize: attachment.fileSize || attachment.size || 0,
                fileUrl: attachment.fileUrl || attachment.url || ''
              }
            });
            migratedCommentAttachments++;
          }
        }
      } catch (error) {
        console.warn(`خطأ في ترحيل attachments للتعليق ${comment.id}:`, error.message);
      }
    }
    console.log(`   ✅ تم ترحيل ${migratedCommentAttachments} مرفق تعليق\n`);

    console.log('🎉 تم الانتهاء من ترحيل البيانات بنجاح!');
    console.log('\n📊 ملخص الترحيل:');
    console.log(`- الرسائل: ${migratedMessages}`);
    console.log(`- العلامات: ${migratedTags}`);
    console.log(`- مرفقات الطلبات: ${migratedRequestAttachments}`);
    console.log(`- مرفقات التعليقات: ${migratedCommentAttachments}`);

  } catch (error) {
    console.error('❌ خطأ في ترحيل البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run migration
if (require.main === module) {
  migrateJsonData()
    .then(() => {
      console.log('\n✅ تم الانتهاء من سكريبت الترحيل');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في تنفيذ سكريبت الترحيل:', error);
      process.exit(1);
    });
}

module.exports = { migrateJsonData };
