{"timestamp": "2025-08-04T23:58:51.375Z", "totalFixes": 80, "filesModified": ["app/(main)/maintenance/page.tsx", "app/(main)/maintenance-transfer/page.tsx", "app/(main)/inventory/page.tsx", "app/(main)/grading/page.tsx", "app/(main)/accept-devices/page.tsx", "app/(main)/warehouse-transfer/page.tsx", "app/(main)/track/page.tsx", "app/(main)/returns/page.tsx"], "pageFixes": [{"search": {}, "replace": "new Date()", "description": "إزالة toISOString() غير الضرورية"}, {"search": {}, "replace": "new Date().toISOString().slice(0, 16)", "description": "الاحتفاظ بـ slice للـ datetime-local inputs"}, {"search": {}, "replace": "new Date().toISOString().slice(0, 10)", "description": "الاحتفاظ بـ slice للـ date inputs"}, {"search": {}, "replace": "", "description": "إزالة toLocaleDateString للاستبدال بـ formatDate"}, {"search": {}, "replace": "", "description": "إزالة toLocaleDateString للاستبدال بـ formatDate"}, {"search": {}, "replace": "", "description": "إزالة toLocaleString للاستبدال بـ formatDateTime"}, {"search": {}, "replace": "formatDate(date, { format: \"full\" })", "description": "استبدال التنسيق اليدوي بـ formatDate"}, {"search": {}, "replace": "formatDate(date, { format: \"full\" })", "description": "استبدال التنسيق اليدوي بـ formatDate"}, {"search": {}, "replace": "formatDate(date, { format: \"full\" })", "description": "استبدال التنسيق اليدوي بـ formatDate"}], "specialFixes": [{"search": {}, "replace": "// استخدم formatDateTime من date-utils بدلاً من هذه الدالة", "description": "إزالة دوال التنسيق المحلية"}, {"search": {}, "replace": "// استخدم formatDate من date-utils بدلاً من هذه الدالة", "description": "إزالة دوال التنسيق العربية المحلية"}]}