/**
 * Database Performance Monitor
 * Date: 2025-08-05
 * Description: Monitor database performance and slow queries
 * Author: Augment Agent
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class DatabasePerformanceMonitor {
  constructor() {
    this.slowQueryThreshold = 1000; // 1 second
    this.performanceLog = [];
    this.isMonitoring = false;
  }

  // بدء مراقبة الأداء
  startMonitoring() {
    if (this.isMonitoring) {
      console.log('📊 مراقب الأداء يعمل بالفعل');
      return;
    }

    console.log('🚀 بدء مراقبة أداء قاعدة البيانات...\n');
    this.isMonitoring = true;

    // مراقبة الاستعلامات البطيئة
    prisma.$on('query', (e) => {
      if (e.duration > this.slowQueryThreshold) {
        const slowQuery = {
          timestamp: new Date().toISOString(),
          query: e.query,
          duration: e.duration,
          params: e.params,
          severity: this.getQuerySeverity(e.duration)
        };

        this.performanceLog.push(slowQuery);
        this.logSlowQuery(slowQuery);
      }
    });

    console.log(`✅ مراقب الأداء نشط - عتبة التحذير: ${this.slowQueryThreshold}ms`);
  }

  // تحديد مستوى خطورة الاستعلام البطيء
  getQuerySeverity(duration) {
    if (duration > 5000) return 'CRITICAL';
    if (duration > 3000) return 'HIGH';
    if (duration > 1000) return 'MEDIUM';
    return 'LOW';
  }

  // تسجيل الاستعلام البطيء
  logSlowQuery(slowQuery) {
    const severityEmoji = {
      'CRITICAL': '🔴',
      'HIGH': '🟠', 
      'MEDIUM': '🟡',
      'LOW': '🔵'
    };

    console.log(`${severityEmoji[slowQuery.severity]} استعلام بطيء مكتشف:`);
    console.log(`   ⏱️ المدة: ${slowQuery.duration}ms`);
    console.log(`   📝 الاستعلام: ${slowQuery.query.substring(0, 100)}...`);
    console.log(`   📅 الوقت: ${slowQuery.timestamp}`);
    console.log('');
  }

  // إيقاف المراقبة
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('⏹️ تم إيقاف مراقب الأداء');
  }

  // تشغيل اختبارات الأداء
  async runPerformanceTests() {
    console.log('🔍 تشغيل اختبارات الأداء الشاملة...\n');

    const tests = [
      {
        name: 'Device Status Query',
        description: 'البحث في حالة الأجهزة',
        query: async () => {
          const start = Date.now();
          await prisma.device.findMany({ 
            where: { status: 'متاح' }, 
            take: 50 
          });
          return Date.now() - start;
        }
      },
      {
        name: 'Employee Requests Dashboard',
        description: 'لوحة معلومات طلبات الموظفين',
        query: async () => {
          const start = Date.now();
          await prisma.employeeRequest.findMany({ 
            where: { 
              status: 'قيد المراجعة',
              priority: 'عالية'
            }, 
            take: 20,
            orderBy: { requestDate: 'desc' }
          });
          return Date.now() - start;
        }
      },
      {
        name: 'Sales Report Query',
        description: 'تقرير المبيعات الشهري',
        query: async () => {
          const start = Date.now();
          const startDate = new Date('2025-01-01');
          const endDate = new Date('2025-12-31');
          await prisma.sale.findMany({ 
            where: { 
              date: { gte: startDate, lte: endDate }
            }, 
            include: {
              items: true
            },
            take: 30
          });
          return Date.now() - start;
        }
      },
      {
        name: 'Returns Processing',
        description: 'معالجة المرتجعات',
        query: async () => {
          const start = Date.now();
          await prisma.return.findMany({ 
            where: { 
              status: 'معلق'
            }, 
            include: {
              items: true
            },
            take: 25
          });
          return Date.now() - start;
        }
      },
      {
        name: 'Evaluation Orders Status',
        description: 'حالة أوامر التقييم',
        query: async () => {
          const start = Date.now();
          await prisma.evaluationOrder.findMany({ 
            where: { 
              status: 'معلق'
            }, 
            take: 20
          });
          return Date.now() - start;
        }
      },
      {
        name: 'Supply Orders Report',
        description: 'تقرير أوامر التوريد',
        query: async () => {
          const start = Date.now();
          await prisma.supplyOrder.findMany({ 
            include: {
              items: true
            },
            take: 20,
            orderBy: { supplyDate: 'desc' }
          });
          return Date.now() - start;
        }
      },
      {
        name: 'User Activity Audit',
        description: 'تدقيق نشاط المستخدمين',
        query: async () => {
          const start = Date.now();
          await prisma.auditLog.findMany({ 
            where: {
              timestamp: {
                gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // آخر 24 ساعة
              }
            },
            take: 50,
            orderBy: { timestamp: 'desc' }
          });
          return Date.now() - start;
        }
      },
      {
        name: 'Complex Join Query',
        description: 'استعلام معقد بربط متعدد',
        query: async () => {
          const start = Date.now();
          await prisma.device.findMany({
            where: {
              status: 'متاح'
            },
            take: 15
          });
          return Date.now() - start;
        }
      }
    ];

    const results = [];
    let totalTime = 0;
    let slowQueries = 0;
    let fastQueries = 0;

    for (const test of tests) {
      try {
        console.log(`🧪 تشغيل: ${test.name}`);
        console.log(`   📝 ${test.description}`);
        
        // تشغيل الاختبار 3 مرات وأخذ المتوسط
        const times = [];
        for (let i = 0; i < 3; i++) {
          const duration = await test.query();
          times.push(duration);
        }
        
        const avgDuration = Math.round(times.reduce((a, b) => a + b, 0) / times.length);
        const minDuration = Math.min(...times);
        const maxDuration = Math.max(...times);
        
        totalTime += avgDuration;
        
        // تصنيف الأداء
        let performance;
        let emoji;
        if (avgDuration < 50) {
          performance = 'ممتاز';
          emoji = '🟢';
          fastQueries++;
        } else if (avgDuration < 200) {
          performance = 'جيد';
          emoji = '🟡';
          fastQueries++;
        } else if (avgDuration < 500) {
          performance = 'مقبول';
          emoji = '🟠';
        } else {
          performance = 'بطيء';
          emoji = '🔴';
          slowQueries++;
        }
        
        console.log(`   ${emoji} النتيجة: ${avgDuration}ms (${minDuration}-${maxDuration}ms) - ${performance}`);
        console.log('');
        
        results.push({
          name: test.name,
          description: test.description,
          avgDuration,
          minDuration,
          maxDuration,
          performance,
          emoji
        });
        
      } catch (error) {
        console.log(`   ❌ خطأ في الاختبار: ${error.message}`);
        console.log('');
        results.push({
          name: test.name,
          description: test.description,
          error: error.message,
          performance: 'خطأ',
          emoji: '❌'
        });
      }
    }

    // عرض التقرير النهائي
    this.displayPerformanceReport(results, totalTime, fastQueries, slowQueries);
    
    return results;
  }

  // عرض تقرير الأداء
  displayPerformanceReport(results, totalTime, fastQueries, slowQueries) {
    console.log('\n' + '='.repeat(60));
    console.log('📊 تقرير الأداء الشامل');
    console.log('='.repeat(60));
    
    console.log('\n📈 الإحصائيات العامة:');
    console.log(`⏱️ إجمالي وقت الاختبارات: ${totalTime}ms`);
    console.log(`⚡ استعلامات سريعة: ${fastQueries}`);
    console.log(`🐌 استعلامات بطيئة: ${slowQueries}`);
    console.log(`📊 متوسط الأداء: ${Math.round(totalTime / results.length)}ms`);
    
    // ترتيب النتائج حسب الأداء
    const sortedResults = results.sort((a, b) => (a.avgDuration || 0) - (b.avgDuration || 0));
    
    console.log('\n🏆 أفضل الاستعلامات أداءً:');
    sortedResults.slice(0, 3).forEach((result, index) => {
      if (!result.error) {
        console.log(`   ${index + 1}. ${result.emoji} ${result.name}: ${result.avgDuration}ms`);
      }
    });
    
    if (slowQueries > 0) {
      console.log('\n🐌 الاستعلامات التي تحتاج تحسين:');
      sortedResults.slice(-3).forEach((result, index) => {
        if (!result.error && result.avgDuration > 200) {
          console.log(`   ${index + 1}. ${result.emoji} ${result.name}: ${result.avgDuration}ms`);
        }
      });
    }
    
    // تقييم الأداء العام
    const averageTime = totalTime / results.length;
    let overallGrade;
    let overallEmoji;
    
    if (averageTime < 100) {
      overallGrade = 'A+ (ممتاز)';
      overallEmoji = '🏆';
    } else if (averageTime < 200) {
      overallGrade = 'A (جيد جداً)';
      overallEmoji = '🥇';
    } else if (averageTime < 400) {
      overallGrade = 'B (جيد)';
      overallEmoji = '🥈';
    } else {
      overallGrade = 'C (يحتاج تحسين)';
      overallEmoji = '🥉';
    }
    
    console.log(`\n${overallEmoji} التقييم العام: ${overallGrade}`);
    console.log(`📊 متوسط الاستجابة: ${Math.round(averageTime)}ms`);
    
    // التوصيات
    console.log('\n💡 التوصيات:');
    if (averageTime < 100) {
      console.log('✅ الأداء ممتاز! النظام محسن بشكل مثالي');
    } else if (averageTime < 200) {
      console.log('👍 الأداء جيد، مراقبة دورية مطلوبة');
    } else {
      console.log('⚠️ يُنصح بمراجعة الفهارس وتحسين الاستعلامات');
    }
  }

  // جلب إحصائيات الفهارس
  async getIndexStatistics() {
    console.log('📊 جلب إحصائيات الفهارس...\n');
    
    try {
      // محاولة PostgreSQL أولاً
      const indexes = await prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE schemaname = 'public'
        ORDER BY tablename, indexname;
      `;
      
      console.log(`📋 إجمالي الفهارس: ${indexes.length}`);
      
      // تجميع حسب الجدول
      const indexesByTable = {};
      indexes.forEach(index => {
        if (!indexesByTable[index.tablename]) {
          indexesByTable[index.tablename] = [];
        }
        indexesByTable[index.tablename].push(index.indexname);
      });
      
      console.log('\n📊 توزيع الفهارس حسب الجداول:');
      Object.entries(indexesByTable)
        .sort(([,a], [,b]) => b.length - a.length)
        .slice(0, 10)
        .forEach(([table, tableIndexes]) => {
          console.log(`   📋 ${table}: ${tableIndexes.length} فهرس`);
        });
        
      return indexes;
      
    } catch (error) {
      try {
        // محاولة SQLite
        const indexes = await prisma.$queryRaw`
          SELECT name FROM sqlite_master WHERE type = 'index';
        `;
        console.log(`📋 إجمالي الفهارس: ${indexes.length}`);
        return indexes;
      } catch (sqliteError) {
        console.log('⚠️ لا يمكن جلب إحصائيات الفهارس');
        return [];
      }
    }
  }

  // جلب تقرير الاستعلامات البطيئة
  getSlowQueriesReport() {
    if (this.performanceLog.length === 0) {
      console.log('✅ لا توجد استعلامات بطيئة مسجلة');
      return;
    }
    
    console.log('\n📊 تقرير الاستعلامات البطيئة:');
    console.log('='.repeat(50));
    
    const sortedQueries = this.performanceLog.sort((a, b) => b.duration - a.duration);
    
    sortedQueries.slice(0, 5).forEach((query, index) => {
      console.log(`\n${index + 1}. 🔴 استعلام بطيء:`);
      console.log(`   ⏱️ المدة: ${query.duration}ms`);
      console.log(`   🎯 الخطورة: ${query.severity}`);
      console.log(`   📝 الاستعلام: ${query.query.substring(0, 80)}...`);
      console.log(`   📅 الوقت: ${query.timestamp}`);
    });
    
    // إحصائيات
    const totalQueries = this.performanceLog.length;
    const averageDuration = this.performanceLog.reduce((sum, q) => sum + q.duration, 0) / totalQueries;
    const criticalQueries = this.performanceLog.filter(q => q.severity === 'CRITICAL').length;
    
    console.log(`\n📈 الإحصائيات:`);
    console.log(`📊 إجمالي الاستعلامات البطيئة: ${totalQueries}`);
    console.log(`⏱️ متوسط المدة: ${Math.round(averageDuration)}ms`);
    console.log(`🔴 استعلامات حرجة: ${criticalQueries}`);
  }

  // تنظيف سجل الأداء
  clearPerformanceLog() {
    this.performanceLog = [];
    console.log('🧹 تم تنظيف سجل الأداء');
  }
}

// تشغيل مراقب الأداء
async function runPerformanceMonitor() {
  const monitor = new DatabasePerformanceMonitor();
  
  console.log('🚀 بدء مراقب أداء قاعدة البيانات...\n');
  
  // بدء المراقبة
  monitor.startMonitoring();
  
  // جلب إحصائيات الفهارس
  await monitor.getIndexStatistics();
  
  // تشغيل اختبارات الأداء
  await monitor.runPerformanceTests();
  
  // عرض تقرير الاستعلامات البطيئة
  monitor.getSlowQueriesReport();
  
  console.log('\n✅ انتهى تشغيل مراقب الأداء');
  
  await prisma.$disconnect();
}

// تشغيل مراقب الأداء
if (require.main === module) {
  runPerformanceMonitor()
    .then(() => {
      console.log('\n🎯 تم الانتهاء من مراقبة الأداء');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ خطأ في مراقب الأداء:', error);
      process.exit(1);
    });
}

module.exports = { DatabasePerformanceMonitor };
