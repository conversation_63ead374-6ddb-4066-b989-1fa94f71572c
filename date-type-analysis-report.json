{"timestamp": "2025-08-05T01:01:55.668Z", "summary": {"totalIssues": 116, "highSeverityCount": 67, "mediumSeverityCount": 36, "lowSeverityCount": 13, "issueTypes": {"TO_ISO_STRING_USAGE": 36, "NEW_DATE_TO_ISO": 20, "STRING_DATE_TYPE": 47, "JSON_STRINGIFY_DATE": 13}, "fileCount": 33}, "details": {"lib\\date-utils.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 7, "content": "* 1. toISOString().slice() في form inputs:", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 9, "content": "*    - مثال: defaultValue={new Date().toISOString().slice(0, 16)}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 9, "content": "*    - مثال: defaultValue={new Date().toISOString().slice(0, 16)}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 11, "content": "* 2. toISOString() في أسماء الملفات:", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 13, "content": "*    - مثال: doc.save(`report_${new Date().toISOString().slice(0, 10)}.pdf`)", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 13, "content": "*    - مثال: doc.save(`report_${new Date().toISOString().slice(0, 10)}.pdf`)", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 17, "content": "*    - مثال: formatDate(date): string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "JSON_STRINGIFY_DATE", "line": 19, "content": "* 4. JSON.stringify مع Date objects:", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 21, "content": "*    - مثال: JSON.stringify(data, (key, value) => value instanceof Date ? value.toISOString() : value)", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 21, "content": "*    - مثال: JSON.stringify(data, (key, value) => value instanceof Date ? value.toISOString() : value)", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 23, "content": "* 5. console.log مع toISOString():", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 25, "content": "*    - مثال: console.log(`التاريخ: ${date.toISOString()}`)", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 27, "content": "* 6. formattedDate?: string:", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 29, "content": "*    - مثال: { date: Date; formattedDate?: string }", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 32, "content": "*    - validateIpAddress(ip: string): boolean", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 33, "content": "*    - getOverdueTimeText(priority: string): string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 38, "content": "* 1. toISOString().slice() في form inputs - مطلوب لـ HTML date inputs", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 39, "content": "* 2. toISOString() في أسماء الملفات - مطلوب لتجنب الأحرف الخاصة", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 41, "content": "* 4. JSON.stringify مع Date objects - مطلوب للتسلسل", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 42, "content": "* 5. console.log مع toISOString() - مفيد للتشخيص", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 247, "content": "export function formatDateForCSV(date: Date | null | undefined): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 257, "content": "export function getRelativeTimeArabic(date: Date | null | undefined): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 292, "content": "export function getCurrentArabicDate(): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 301, "content": "export function parseArabicDate(input: string | Date | null | undefined): Date {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 312, "content": "export function formatArabicDate(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 320, "content": "export function formatArabicDateOnly(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 328, "content": "export function formatArabicTime(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 336, "content": "export function formatShortDate(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\device-tracking-utils.ts": [{"type": "STRING_DATE_TYPE", "line": 518, "content": "function formatTrackingDate(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\export-utils\\enhanced-html-export.ts": [{"type": "STRING_DATE_TYPE", "line": 83, "content": "formattedDate?: string; // يبقى string للعرض", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 923, "content": "arabicDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 924, "content": "englishDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 925, "content": "time: string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1082, "content": "function generateTimelineSection(timelineEvents: TimelineEvent[], isCustomerView: boolean, language: string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1124, "content": "arabicDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1125, "content": "englishDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1126, "content": "time: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\network.ts": [{"type": "STRING_DATE_TYPE", "line": 80, "content": "export function validateIpAddress(ip: string): boolean {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 102, "content": "export function formatConnectionTime(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\notification-service.ts": [{"type": "STRING_DATE_TYPE", "line": 111, "content": "static getOverdueTimeText(priority: string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 135, "content": "static getTimeText(minutes: number): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\print-templates\\index.ts": [{"type": "STRING_DATE_TYPE", "line": 321, "content": "formatDate: (date: Date): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\types.ts": [{"type": "STRING_DATE_TYPE", "line": 698, "content": "timezone?: string; // يبقى string (ليس تاريخ)", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\grading\\2page.tsx": [{"type": "STRING_DATE_TYPE", "line": 214, "content": "const validateDeviceData = (deviceId: string, currentItems: EvaluatedDevice[]) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 905, "content": "defaultValue={new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 905, "content": "defaultValue={new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\inventory\\page_backup.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 373, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 373, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 390, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 390, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 932, "content": "doc.save(`all_devices_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 932, "content": "doc.save(`all_devices_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 953, "content": "`all_devices_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 953, "content": "`all_devices_report_${new Date().toISOString().slice(0, 10)}.xlsx` // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\maintenance\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 954, "content": "setOrderDate(new Date(order.date).toISOString().slice(0, 16)) // مطلوب لـ HTML input; // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1303, "content": "setDeliveryOrderDate(new Date(order.date).toISOString().slice(0, 16)) // مطلوب لـ HTML input; // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\(main)\\maintenance-transfer\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 1225, "content": "date: new Date(order.date).toISOString().slice(0, 16), // مطلوب لـ HTML input format", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1495, "content": "date: new Date(order.date).toISOString().slice(0, 16), // مطلوب لـ HTML input format", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\(main)\\messaging\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 730, "content": "const forceUpdateConversation = (threadId: number, messageText: string) => { // ليس متعلق بالتواريخ", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\reports\\employee-reports\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 109, "content": "doc.save(`employee_productivity_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 109, "content": "doc.save(`employee_productivity_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\reports\\grading-reports\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 204, "content": "doc.save(`grading_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 204, "content": "doc.save(`grading_report_${new Date().toISOString().slice(0, 10)}.pdf` // مطلوب لاسم الملف); // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\requests\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 108, "content": "const updateRequestStatus = async (requestId: number, status: string, notes?: string) => { // ليس متعلق بالتواريخ", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\sales\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 382, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 382, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 416, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 416, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 504, "content": "date: sale.date || new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 504, "content": "date: sale.date || new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\stocktaking\\page.tsx": [{"type": "JSON_STRINGIFY_DATE", "line": 1288, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1342, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1393, "content": "localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1397, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1411, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1416, "content": "localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 1606, "content": "defaultValue={new Date().toISOString().split('T')[0]} // مطلوب لـ HTML date input", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1606, "content": "defaultValue={new Date().toISOString().split('T')[0]} // مطلوب لـ HTML date input", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\supply\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 111, "content": "supplyDate: new Date().toISOString().slice(0, 16), // مطلوب لـ HTML input format", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 111, "content": "supplyDate: new Date().toISOString().slice(0, 16), // مطلوب لـ HTML input format", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 461, "content": "supplyDate: draft.formState?.supplyDate || new Date().toISOString().split('T')[0],", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 461, "content": "supplyDate: draft.formState?.supplyDate || new Date().toISOString().split('T')[0],", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 468, "content": "createdAt: draft.timestamp || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 468, "content": "createdAt: draft.timestamp || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 601, "content": ": (order.supplyDate || new Date().toISOString().slice(0, 10)) + 'T00:00', // إذا كان تاريخ فقط، أضف وقت افتراضي", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 601, "content": ": (order.supplyDate || new Date().toISOString().slice(0, 10)) + 'T00:00', // إذا كان تاريخ فقط، أضف وقت افتراضي", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 637, "content": "supplyDate: new Date().toISOString().slice(0, 16), // تاريخ ووقت اليوم", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 637, "content": "supplyDate: new Date().toISOString().slice(0, 16), // تاريخ ووقت اليوم", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1866, "content": "value={formState.supplyDate || new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1866, "content": "value={formState.supplyDate || new Date().toISOString().slice(0, 16)} // مطلوب لـ HTML input", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\track\\DeviceDetailsSection.tsx": [{"type": "STRING_DATE_TYPE", "line": 35, "content": "function formatArabicDate(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\track\\DeviceHistoryTimeline.tsx": [{"type": "STRING_DATE_TYPE", "line": 39, "content": "formattedDate?: string; // يبقى string للعرض // يبقى string للعرض", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 51, "content": "function formatArabicDate(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\track\\DeviceTrackingFilters.tsx": [{"type": "STRING_DATE_TYPE", "line": 31, "content": "formattedDate?: string; // يبقى string للعرض // يبقى string للعرض", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 146, "content": "const updateFilter = (key: string, value: any) => { // يقبل أي نوع بيانات", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\track\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 52, "content": "formattedDate?: string; // يبقى string للعرض // يبقى string للعرض", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\track\\simple-page.tsx": [{"type": "STRING_DATE_TYPE", "line": 52, "content": "formattedDate?: string; // يبقى string للعرض // يبقى string للعرض", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\api\\database\\backup\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 94, "content": "const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); // مطلوب لاسم الملف", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 94, "content": "const timestamp = new Date().toISOString().replace(/[:.]/g, '-'); // مطلوب لاسم الملف", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\evaluations\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 70, "content": "const sanitizeDate = (dateStr: Date | string | null): string | null => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\api\\maintenance-orders\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 124, "content": "items: newOrder.items ? JSON.stringify(newOrder.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 124, "content": "items: newOrder.items ? JSON.stringify(newOrder.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 235, "content": "items: updatedOrder.items ? JSON.stringify(updatedOrder.items, (key, value) => value instanceof Date ? value.toISOString() : value) : existingOrder.items,", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 235, "content": "items: updatedOrder.items ? JSON.stringify(updatedOrder.items, (key, value) => value instanceof Date ? value.toISOString() : value) : existingOrder.items,", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}], "app\\api\\maintenance-receipts\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 118, "content": "items: newReceipt.items ? JSON.stringify(newReceipt.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 118, "content": "items: newReceipt.items ? JSON.stringify(newReceipt.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 213, "content": "items: updatedReceipt.items ? JSON.stringify(updatedReceipt.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 213, "content": "items: updatedReceipt.items ? JSON.stringify(updatedReceipt.items, (key, value) => value instanceof Date ? value.toISOString() : value) : JSON.stringify([]),", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}], "app\\api\\notifications\\check-overdue\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 78, "content": "function getOverdueTimeText(priority: string): string // ليس متعلق بالتواريخ {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\api\\notifications\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 145, "content": "function getOverdueTime(priority: string): string // ليس متعلق بالتواريخ {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\api\\returns\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 8, "content": "function safeToISOString(dateValue: Date | string | null): string | null {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "components\\AttachmentsViewer.tsx": [{"type": "STRING_DATE_TYPE", "line": 85, "content": "const formatUploadDate = (date: Date): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "components\\ui\\print-export-buttons.tsx": [{"type": "STRING_DATE_TYPE", "line": 191, "content": "timelineData?: { events: any[]; title?: string }; // any مقبول للمرونة", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "context\\store.tsx": [{"type": "STRING_DATE_TYPE", "line": 101, "content": "updateDeviceStatus: (deviceId: string, status: DeviceStatus) => void; // ليس متعلق بالتواريخ", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 205, "content": "updateStocktakeItem: (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => void; // ليس متعلق بالتواريخ", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 549, "content": "const updateDeviceStatus = async (deviceId: string, status: DeviceStatus) => { // ليس متعلق بالتواريخ", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}]}}