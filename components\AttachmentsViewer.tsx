'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  Image, 
  FileVideo, 
  FileAudio, 
  File, 
  Download, 
  Eye, 
  Trash2,
  ExternalLink
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// دالة للحصول على headers التفويض
const getAuthHeader = () => {
  const adminToken = btoa('user:admin:admin');
  return { 'Authorization': `Bearer ${adminToken}` };
};

interface AttachmentFile {
  originalName: string;
  fileName: string;
  filePath: string;
  size: number;
  type: string;
  uploadedAt: string;
}

interface AttachmentsViewerProps {
  isOpen: boolean;
  onClose: () => void;
  attachments: AttachmentFile[];
  onAttachmentsChange?: (attachments: AttachmentFile[]) => void;
  onRemove?: (fileName: string) => void;
  canDelete?: boolean;
  title?: string;
  section?: string;
}

export default function AttachmentsViewer({
  isOpen,
  onClose,
  attachments,
  onAttachmentsChange,
  onRemove,
  canDelete = false,
  title = "المرفقات",
  section = "general"
}: AttachmentsViewerProps) {
  const [selectedFile, setSelectedFile] = useState<AttachmentFile | null>(null);

  // تحديد أيقونة الملف حسب النوع
  const getFileIcon = (type: string, size: number = 20) => {
    if (type.startsWith('image/')) {
      return <Image size={size} className="text-blue-500" />;
    } else if (type.startsWith('video/')) {
      return <FileVideo size={size} className="text-purple-500" />;
    } else if (type.startsWith('audio/')) {
      return <FileAudio size={size} className="text-green-500" />;
    } else if (type.includes('pdf') || type.includes('document') || type.includes('text')) {
      return <FileText size={size} className="text-red-500" />;
    } else {
      return <File size={size} className="text-gray-500" />;
    }
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // تنسيق تاريخ الرفع
  const formatUploadDate = (date: Date): string => {
    return formatDateTime(date);
  };

  // تحميل الملف
  const handleDownload = (file: AttachmentFile) => {
    const link = document.createElement('a');
    link.href = file.filePath;
    link.download = file.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: 'تم التحميل',
      description: `تم تحميل الملف: ${file.originalName}`,
    });
  };

  // عرض الملف
  const handleView = (file: AttachmentFile) => {
    if (file.type.startsWith('image/')) {
      setSelectedFile(file);
    } else {
      window.open(file.filePath, '_blank');
    }
  };

  // حذف الملف
  const handleRemove = async (file: AttachmentFile) => {
    try {
      // حذف الملف من الخادم
      const response = await fetch(`/api/upload?fileName=${file.fileName}&section=${section}`, {
        method: 'DELETE',
        headers: getAuthHeader(),
      });

      if (response.ok) {
        // إزالة الملف من القائمة
        const updatedAttachments = attachments.filter(f => f.fileName !== file.fileName);
        if (onAttachmentsChange) {
          onAttachmentsChange(updatedAttachments);
        } else if (onRemove) {
          onRemove(file.fileName);
        }

        toast({
          title: 'تم الحذف',
          description: `تم حذف الملف: ${file.originalName}`,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'خطأ في الحذف',
          description: 'فشل في حذف الملف',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'خطأ في الحذف',
        description: 'حدث خطأ غير متوقع',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {title} ({attachments.length})
            </DialogTitle>
          </DialogHeader>
          
          <ScrollArea className="h-[60vh] pr-4">
            {attachments.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                <FileText className="h-12 w-12 mb-2 opacity-50" />
                <p>لا توجد مرفقات</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {attachments.map((file, index) => (
                  <div
                    key={index}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        {getFileIcon(file.type, 24)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate" title={file.originalName}>
                          {file.originalName}
                        </h4>
                        
                        <div className="flex flex-wrap gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            {formatFileSize(file.size)}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {file.type.split('/')[1]?.toUpperCase() || 'FILE'}
                          </Badge>
                        </div>
                        
                        <p className="text-xs text-gray-500 mt-1">
                          {formatUploadDate(file.uploadedAt)}
                        </p>
                        
                        <div className="flex gap-2 mt-3">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleView(file)}
                            className="h-7 px-2 text-xs"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            عرض
                          </Button>
                          
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownload(file)}
                            className="h-7 px-2 text-xs"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            تحميل
                          </Button>
                          
                          {canDelete && (onRemove || onAttachmentsChange) && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRemove(file)}
                              className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              حذف
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* مودال عرض الصور */}
      {selectedFile && selectedFile.type.startsWith('image/') && (
        <Dialog open={!!selectedFile} onOpenChange={() => setSelectedFile(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{selectedFile.originalName}</DialogTitle>
            </DialogHeader>
            <div className="flex justify-center">
              <img
                src={selectedFile.filePath}
                alt={selectedFile.originalName}
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
