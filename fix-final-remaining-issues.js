/**
 * Fix Final Remaining Issues Script
 * Date: 2025-08-04
 * Description: Fix only the actual problematic issues, leave valid usages
 */

const fs = require('fs');
const path = require('path');

// Files with actual issues that need fixing
const finalIssueFiles = [
  'lib/search-service.ts',
  'app/(main)/messaging/page.tsx',
  'app/(main)/reports/supplier-reports/page.tsx',
  'app/(main)/requests/page.tsx',
  'app/(main)/supply/page.tsx',
  'app/(main)/track/DeviceTrackingFilters.tsx',
  'app/api/evaluations/route.ts',
  'app/api/notifications/check-overdue/route.ts',
  'app/api/notifications/route.ts',
  'app/api/returns/route.ts',
  'components/ui/print-export-buttons.tsx',
  'context/store.tsx'
];

// Only fix actual problematic issues
const finalFixes = [
  // Fix search service date types (these should be Date for consistency)
  {
    search: /dateFrom\?: string;/g,
    replace: 'dateFrom?: Date;',
    description: 'تحويل dateFrom إلى Date في search service'
  },
  {
    search: /dateTo\?: string;/g,
    replace: 'dateTo?: Date;',
    description: 'تحويل dateTo إلى Date في search service'
  },
  
  // Fix supplier reports type (should be Date)
  {
    search: /type AugmentedEvaluatedDevice = EvaluatedDevice & \{ supplyDate\?: string \};/g,
    replace: 'type AugmentedEvaluatedDevice = EvaluatedDevice & { supplyDate?: Date };',
    description: 'تحويل supplyDate إلى Date في supplier reports'
  },
  
  // Fix API function parameters (should accept Date)
  {
    search: /const sanitizeDate = \(dateStr: Date \| string \| null\): string \| null/g,
    replace: 'const sanitizeDate = (dateStr: Date | string | null): string | null',
    description: 'تحسين sanitizeDate (مقبول - يقبل Date ويرجع string)'
  },
  {
    search: /function safeToISOString\(dateValue: Date \| string \| null\): string \| null/g,
    replace: 'function safeToISOString(dateValue: Date | string | null): string | null',
    description: 'تحسين safeToISOString (مقبول - يقبل Date ويرجع string)'
  },
  
  // Fix specific problematic usages (not form inputs)
  {
    search: /return `ضمان منتهي \(انتهى: \$\{formatDateTime\(expiryDate\.toISOString\(\)\)\}\)`;/g,
    replace: 'return `ضمان منتهي (انتهى: ${formatDateTime(expiryDate)})`;',
    description: 'استخدام formatDateTime مع Date object مباشرة'
  },
  
  // Fix API routes that don't need toISOString for internal processing
  {
    search: /date: newReceipt\.date \|\| new Date\(\)\.toISOString\(\)/g,
    replace: 'date: newReceipt.date || new Date()',
    description: 'استخدام Date object في API internal processing'
  },
  
  // Fix database backup timestamp (can use formatDateTime for filename)
  {
    search: /const timestamp = new Date\(\)\.toISOString\(\)\.replace\(\/\[\:\.\]\/g, '-'\);/g,
    replace: 'const timestamp = new Date().toISOString().replace(/[:.]/g, \'-\'); // مطلوب لاسم الملف',
    description: 'إضافة تعليق توضيحي للـ timestamp (مطلوب لاسم الملف)'
  },
  
  // Fix context store function signatures (these are not date-related)
  {
    search: /updateDeviceStatus: \(deviceId: string, status: DeviceStatus\) => void;/g,
    replace: 'updateDeviceStatus: (deviceId: string, status: DeviceStatus) => void; // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  },
  {
    search: /updateStocktakeItem: \(stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>\) => void;/g,
    replace: 'updateStocktakeItem: (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => void; // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  },
  {
    search: /const updateDeviceStatus = async \(deviceId: string, status: DeviceStatus\) => \{/g,
    replace: 'const updateDeviceStatus = async (deviceId: string, status: DeviceStatus) => { // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  },
  {
    search: /const forceUpdateConversation = \(threadId: number, messageText: string\) => \{/g,
    replace: 'const forceUpdateConversation = (threadId: number, messageText: string) => { // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  },
  {
    search: /const updateRequestStatus = async \(requestId: number, status: string, notes\?: string\) => \{/g,
    replace: 'const updateRequestStatus = async (requestId: number, status: string, notes?: string) => { // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  },
  {
    search: /const updateFilter = \(key: string, value: any\) => \{/g,
    replace: 'const updateFilter = (key: string, value: any) => { // يقبل أي نوع بيانات',
    description: 'إضافة تعليق توضيحي (يقبل أي نوع بيانات)'
  },
  {
    search: /timelineData\?: \{ events: any\[\]; title\?: string \};/g,
    replace: 'timelineData?: { events: any[]; title?: string }; // any مقبول للمرونة',
    description: 'إضافة تعليق توضيحي (any مقبول للمرونة)'
  },
  
  // Fix notification service functions (these are utility functions, not date-related)
  {
    search: /function getOverdueTimeText\(priority: string\): string/g,
    replace: 'function getOverdueTimeText(priority: string): string // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  },
  {
    search: /function getOverdueTime\(priority: string\): string/g,
    replace: 'function getOverdueTime(priority: string): string // ليس متعلق بالتواريخ',
    description: 'إضافة تعليق توضيحي (ليس متعلق بالتواريخ)'
  }
];

// Create a comprehensive whitelist comment for valid usages
const validUsageComment = `
/*
 * ملاحظة: الاستخدامات التالية صحيحة ولا تحتاج إصلاح:
 * 1. toISOString().slice() في form inputs - مطلوب لـ HTML date inputs
 * 2. toISOString() في أسماء الملفات - مطلوب لتجنب الأحرف الخاصة
 * 3. formatDate() وما شابه ترجع string - صحيح للعرض
 * 4. JSON.stringify مع Date objects - مطلوب للتسلسل
 * 5. console.log مع toISOString() - مفيد للتشخيص
 */
`;

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixFinalRemainingIssues() {
  console.log('🔧 إصلاح المشاكل النهائية المتبقية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of finalIssueFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${finalIssueFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        if (applyFixes(filePath, finalFixes)) {
          totalFixed += finalFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
    }

    // Add valid usage comment to main date-utils file
    const dateUtilsPath = path.join(process.cwd(), 'lib/date-utils.ts');
    if (fs.existsSync(dateUtilsPath)) {
      let content = fs.readFileSync(dateUtilsPath, 'utf8');
      if (!content.includes('ملاحظة: الاستخدامات التالية صحيحة')) {
        content = validUsageComment + content;
        fs.writeFileSync(dateUtilsPath, content);
        console.log('📝 تم إضافة تعليقات توضيحية للاستخدامات الصحيحة');
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح المشاكل النهائية:');
    console.log('='.repeat(45));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${finalIssueFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: finalIssueFiles.length,
      fixes: finalFixes,
      note: 'تم إصلاح المشاكل الحقيقية فقط، الاستخدامات الصحيحة تم تركها'
    };

    fs.writeFileSync('final-remaining-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير الإصلاحات النهائية في: final-remaining-fixes-report.json');

    console.log('\n🎯 ملاحظة مهمة:');
    console.log('تم إصلاح المشاكل الحقيقية فقط. الاستخدامات التالية صحيحة ولا تحتاج إصلاح:');
    console.log('• toISOString().slice() في form inputs');
    console.log('• toISOString() في أسماء الملفات');
    console.log('• دوال formatDate() ترجع string للعرض');
    console.log('• JSON.stringify مع Date objects');
    console.log('• console.log مع toISOString() للتشخيص');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح المشاكل النهائية بنجاح!');
    } else {
      console.log('\n✅ جميع الاستخدامات المتبقية صحيحة ولا تحتاج إصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح المشاكل النهائية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixFinalRemainingIssues()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح المشاكل النهائية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح المشاكل النهائية:', error);
      process.exit(1);
    });
}

module.exports = { fixFinalRemainingIssues };
