/**
 * Fix Final Low Priority Issues Script
 * Date: 2025-08-04
 * Description: Fix the remaining 9 low priority date issues
 */

const fs = require('fs');
const path = require('path');

// Files with JSON.stringify date issues
const finalIssueFiles = [
  'app/api/maintenance-orders/route.ts',
  'app/api/maintenance-receipts/route.ts',
  'app/api/sales/route.ts'
];

// Final fixes for JSON.stringify with dates
const finalFixes = [
  {
    search: /JSON\.stringify\(([^)]*items[^)]*)\)/g,
    replace: 'JSON.stringify($1, (key, value) => value instanceof Date ? value.toISOString() : value)',
    description: 'إصلاح JSON.stringify للتواريخ في items'
  },
  {
    search: /JSON\.stringify\(([^)]*attachments[^)]*)\)/g,
    replace: 'JSON.stringify($1, (key, value) => value instanceof Date ? value.toISOString() : value)',
    description: 'إصلاح JSON.stringify للتواريخ في attachments'
  }
];

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixFinalIssues() {
  console.log('🔧 إصلاح المشاكل النهائية منخفضة الأولوية...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    finalIssueFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file);
      
      console.log(`🔍 فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        if (applyFixes(filePath, finalFixes)) {
          totalFixed += finalFixes.length;
          filesModified.push(file);
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
    });

    // Generate summary
    console.log('\n📊 ملخص إصلاح المشاكل النهائية:');
    console.log('='.repeat(40));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      fixes: finalFixes
    };

    fs.writeFileSync('final-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير الإصلاحات النهائية في: final-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح المشاكل النهائية بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح المشاكل النهائية:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixFinalIssues()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح المشاكل النهائية');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح المشاكل النهائية:', error);
      process.exit(1);
    });
}

module.exports = { fixFinalIssues };
