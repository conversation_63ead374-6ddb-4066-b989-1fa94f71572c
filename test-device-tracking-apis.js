const fetch = require('node-fetch');

async function testAllAPIs() {
  const baseUrl = 'http://localhost:3000';
  
  const endpoints = [
    '/api/devices?view=simple',
    '/api/sales?view=simple', 
    '/api/returns?view=simple',
    '/api/supply?view=simple',
    '/api/suppliers?view=simple',
    '/api/evaluations?view=simple',
    '/api/maintenance-logs?view=simple',
    '/api/maintenance-orders?view=simple',
    '/api/maintenance-receipts?view=simple',
    '/api/warehouse-transfers',
    '/api/delivery-orders?view=simple'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🔍 Testing ${endpoint}...`);
      const response = await fetch(`${baseUrl}${endpoint}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ ${endpoint} - Success (${Array.isArray(data) ? data.length : 'object'} records)`);
        
        // عرض عينة من البيانات للفهم
        if (Array.isArray(data) && data.length > 0) {
          const sample = data[0];
          const keys = Object.keys(sample).slice(0, 5);
          console.log(`   Sample keys: ${keys.join(', ')}`);
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ ${endpoint} - Error ${response.status}: ${errorText.substring(0, 200)}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Network Error: ${error.message}`);
    }
  }
}

console.log('🚀 Testing all APIs for device tracking...');
testAllAPIs().then(() => {
  console.log('\n✅ API testing completed');
}).catch(error => {
  console.error('❌ Test failed:', error);
});
