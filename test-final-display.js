// اختبار سريع لإنشاء فاتورة والتحقق من العرض
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// دالة btoa للـ Node.js
function btoa(str) {
  return Buffer.from(str, 'binary').toString('base64');
}

const createToken = (username, role) => {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData);
};

async function testAndDisplaySales() {
  try {
    console.log('🧪 إنشاء فاتورة جديدة للاختبار...\n');
    
    const adminToken = createToken('admin', 'admin');
    
    // إنشاء فاتورة جديدة
    const saleData = {
      clientName: 'عميل نهائي للاختبار',
      opNumber: '', 
      warehouseName: 'المخزن الرئيسي',
      notes: 'فاتورة اختبار نهائية',
      warrantyPeriod: 'none',
      date: new Date().toISOString(),
      items: [{
        deviceId: 'FINAL_TEST',
        model: 'iPhone 15 Pro',
        price: 2500,
        condition: 'جديد'
      }]
    };

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(saleData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إنشاء الفاتورة بنجاح!');
      console.log(`   SO Number: ${result.soNumber}`);
      console.log(`   OP Number: ${result.opNumber}\n`);
    } else {
      console.log('❌ فشل في إنشاء الفاتورة:', result);
      return;
    }

    // عرض آخر الفواتير من قاعدة البيانات
    console.log('📊 عرض آخر الفواتير (كما ستظهر في الواجهة):');
    console.log('═══════════════════════════════════════════════════════════════════════════════════════');
    console.log('رقم الفاتورة (SO)              | رقم الأمر (OP) | العميل              | التاريخ');
    console.log('───────────────────────────────────────────────────────────────────────────────────────');

    const sales = await prisma.sale.findMany({
      orderBy: { id: 'desc' },
      take: 5,
      select: {
        soNumber: true,
        opNumber: true,
        clientName: true,
        createdAt: true
      }
    });

    sales.forEach(sale => {
      const soNumber = sale.soNumber.padEnd(30);
      const opNumber = (sale.opNumber || 'غير محدد').padEnd(10);
      const clientName = sale.clientName.padEnd(18);
      const date = sale.createdAt.toLocaleDateString('ar-SA');
      
      console.log(`${soNumber} | ${opNumber} | ${clientName} | ${date}`);
    });

    console.log('═══════════════════════════════════════════════════════════════════════════════════════\n');
    
    console.log('💡 الآن في الواجهة ستجد:');
    console.log('   - رقم الفاتورة (SO): الرقم الطويل للنظام الداخلي');
    console.log('   - رقم الأمر (OP): الرقم البسيط للعرض للعملاء (1، 2، 3...)');
    console.log('   - رقم الأمر المخصص: إذا أدخلت رقم أمر، سيظهر ذلك الرقم');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testAndDisplaySales();
