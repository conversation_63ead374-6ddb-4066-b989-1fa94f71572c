import { PrintData, PrintSection } from '@/hooks/usePrintExport';
import { formatDate as formatDateUtil } from '@/lib/date-utils';

// قوالب للأقسام المختلفة

// 1. قالب التوريد
export function createSupplyTemplate(data: {
  supplierInfo: Record<string, any>;
  items: any[];
  orderInfo: Record<string, any>;
  timeline?: any[];
}): PrintData {
  const sections: PrintSection[] = [
    {
      title: 'معلومات المورد',
      type: 'info',
      data: data.supplierInfo
    },
    {
      title: 'تفاصيل الطلب',
      type: 'info',
      data: data.orderInfo
    },
    {
      title: 'الأصناف المطلوبة',
      type: 'table',
      data: data.items,
      columns: ['الصنف', 'الكمية', 'السعر', 'الإجمالي', 'ملاحظات']
    }
  ];

  if (data.timeline && data.timeline.length > 0) {
    sections.push({
      title: 'سجل المتابعة',
      type: 'timeline',
      data: data.timeline
    });
  }

  return {
    title: 'تقرير التوريد',
    subtitle: `طلب رقم: ${data.orderInfo.orderNumber || '-'}`,
    sections
  };
}

// 2. قالب المبيعات
export function createSalesTemplate(data: {
  customerInfo: Record<string, any>;
  items: any[];
  invoiceInfo: Record<string, any>;
  paymentInfo?: Record<string, any>;
}): PrintData {
  const sections: PrintSection[] = [
    {
      title: 'معلومات العميل',
      type: 'info',
      data: data.customerInfo
    },
    {
      title: 'تفاصيل الفاتورة',
      type: 'info',
      data: data.invoiceInfo
    },
    {
      title: 'الأصناف المباعة',
      type: 'table',
      data: data.items,
      columns: ['الصنف', 'الكمية', 'السعر', 'الخصم', 'الإجمالي']
    }
  ];

  if (data.paymentInfo) {
    sections.push({
      title: 'معلومات الدفع',
      type: 'info',
      data: data.paymentInfo
    });
  }

  return {
    title: 'فاتورة مبيعات',
    subtitle: `فاتورة رقم: ${data.invoiceInfo.invoiceNumber || '-'}`,
    sections
  };
}

// 3. قالب المخزون
export function createInventoryTemplate(data: {
  warehouseInfo: Record<string, any>;
  items: any[];
  summary: Record<string, any>;
  movements?: any[];
}): PrintData {
  const sections: PrintSection[] = [
    {
      title: 'معلومات المخزن',
      type: 'info',
      data: data.warehouseInfo
    },
    {
      title: 'ملخص المخزون',
      type: 'info',
      data: data.summary
    },
    {
      title: 'تفاصيل الأصناف',
      type: 'table',
      data: data.items,
      columns: ['الصنف', 'الكمية المتاحة', 'الحد الأدنى', 'الحالة', 'آخر تحديث']
    }
  ];

  if (data.movements && data.movements.length > 0) {
    sections.push({
      title: 'حركات المخزون',
      type: 'timeline',
      data: data.movements
    });
  }

  return {
    title: 'تقرير المخزون',
    subtitle: `مخزن: ${data.warehouseInfo.name || '-'}`,
    sections
  };
}

// 4. قالب الصيانة
export function createMaintenanceTemplate(data: {
  deviceInfo: Record<string, any>;
  issueInfo: Record<string, any>;
  repairSteps: any[];
  partsUsed?: any[];
  timeline?: any[];
}): PrintData {
  const sections: PrintSection[] = [
    {
      title: 'معلومات الجهاز',
      type: 'info',
      data: data.deviceInfo
    },
    {
      title: 'تفاصيل العطل',
      type: 'info',
      data: data.issueInfo
    },
    {
      title: 'خطوات الإصلاح',
      type: 'timeline',
      data: data.repairSteps
    }
  ];

  if (data.partsUsed && data.partsUsed.length > 0) {
    sections.push({
      title: 'القطع المستخدمة',
      type: 'table',
      data: data.partsUsed,
      columns: ['القطعة', 'الكمية', 'السعر', 'المورد']
    });
  }

  if (data.timeline && data.timeline.length > 0) {
    sections.push({
      title: 'سجل الصيانة',
      type: 'timeline',
      data: data.timeline
    });
  }

  return {
    title: 'تقرير الصيانة',
    subtitle: `جهاز: ${data.deviceInfo.model || '-'} - ${data.deviceInfo.serialNumber || '-'}`,
    sections
  };
}

// 5. قالب الموظفين
export function createEmployeeTemplate(data: {
  employeeInfo: Record<string, any>;
  attendance?: any[];
  performance?: Record<string, any>;
  activities?: any[];
}): PrintData {
  const sections: PrintSection[] = [
    {
      title: 'معلومات الموظف',
      type: 'info',
      data: data.employeeInfo
    }
  ];

  if (data.performance) {
    sections.push({
      title: 'تقييم الأداء',
      type: 'info',
      data: data.performance
    });
  }

  if (data.attendance && data.attendance.length > 0) {
    sections.push({
      title: 'سجل الحضور',
      type: 'table',
      data: data.attendance,
      columns: ['التاريخ', 'وقت الحضور', 'وقت الانصراف', 'ساعات العمل', 'ملاحظات']
    });
  }

  if (data.activities && data.activities.length > 0) {
    sections.push({
      title: 'الأنشطة',
      type: 'timeline',
      data: data.activities
    });
  }

  return {
    title: 'تقرير الموظف',
    subtitle: `${data.employeeInfo.name || '-'} - ${data.employeeInfo.position || '-'}`,
    sections
  };
}

// 6. قالب المالية
export function createFinancialTemplate(data: {
  periodInfo: Record<string, any>;
  summary: Record<string, any>;
  transactions: any[];
  categories?: any[];
}): PrintData {
  const sections: PrintSection[] = [
    {
      title: 'معلومات الفترة',
      type: 'info',
      data: data.periodInfo
    },
    {
      title: 'الملخص المالي',
      type: 'info',
      data: data.summary
    },
    {
      title: 'المعاملات',
      type: 'table',
      data: data.transactions,
      columns: ['التاريخ', 'الوصف', 'النوع', 'المبلغ', 'الرصيد']
    }
  ];

  if (data.categories && data.categories.length > 0) {
    sections.push({
      title: 'التصنيفات',
      type: 'table',
      data: data.categories,
      columns: ['التصنيف', 'المبلغ', 'النسبة']
    });
  }

  return {
    title: 'التقرير المالي',
    subtitle: `من ${data.periodInfo.startDate || '-'} إلى ${data.periodInfo.endDate || '-'}`,
    sections
  };
}

// 7. قالب عام للقوائم
export function createListTemplate(data: {
  title: string;
  subtitle?: string;
  items: any[];
  columns: string[];
  summary?: Record<string, any>;
}): PrintData {
  const sections: PrintSection[] = [];

  if (data.summary) {
    sections.push({
      title: 'الملخص',
      type: 'info',
      data: data.summary
    });
  }

  sections.push({
    title: 'البيانات',
    type: 'table',
    data: data.items,
    columns: data.columns
  });

  return {
    title: data.title,
    subtitle: data.subtitle,
    sections
  };
}

// 8. قالب مخصص
export function createCustomTemplate(
  title: string,
  sections: PrintSection[],
  subtitle?: string
): PrintData {
  return {
    title,
    subtitle,
    sections
  };
}

// مساعدات لإنشاء البيانات
export const TemplateHelpers = {
  // تحويل كائن إلى مصفوفة للجدول
  objectToTableData: (obj: Record<string, any>): [string, any][] => {
    return Object.entries(obj).map(([key, value]) => [key, value]);
  },

  // تنسيق التاريخ
  formatDate: (date: string | Date): string => {
    return formatDateUtil(date, { arabic: true });
  },

  // تنسيق المبلغ
  formatCurrency: (amount: number, currency: string = 'ريال'): string => {
    return `${amount} ${currency}`;
  },

  // إنشاء ملخص للأرقام
  createSummary: (items: any[], fields: string[]): Record<string, any> => {
    const summary: Record<string, any> = {};
    
    fields.forEach(field => {
      const values = items.map(item => Number(item[field]) || 0);
      summary[`إجمالي ${field}`] = values.reduce((sum, val) => sum + val, 0);
      summary[`متوسط ${field}`] = values.length > 0 ? summary[`إجمالي ${field}`] / values.length : 0;
    });

    summary['عدد العناصر'] = items.length;
    
    return summary;
  }
};
