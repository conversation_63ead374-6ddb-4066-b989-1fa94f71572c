import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // جلب الإشعارات للمستخدم
    const notifications = await prisma.$queryRaw`
      SELECT * FROM "notifications" 
      WHERE "userId" = ${parseInt(userId)}
      ORDER BY "createdAt" DESC 
      LIMIT 50
    `;

    return NextResponse.json(notifications);
  } catch (error) {
    console.error('Failed to fetch notifications:', error);
    return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const notificationData = await request.json();

    // التحقق من البيانات المطلوبة
    if (!notificationData.userId || !notificationData.type || !notificationData.title || !notificationData.message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // إنشاء الإشعار
    const notification = await prisma.$queryRaw`
      INSERT INTO "notifications" (
        "userId", "type", "title", "message", 
        "requestId", "actionUrl", "priority"
      ) VALUES (
        ${notificationData.userId}, ${notificationData.type}, 
        ${notificationData.title}, ${notificationData.message},
        ${notificationData.requestId || null}, 
        ${notificationData.actionUrl || null},
        ${notificationData.priority || 'normal'}
      ) RETURNING *;
    `;

    return NextResponse.json(notification[0], { status: 201 });
  } catch (error) {
    console.error('Failed to create notification:', error);
    return NextResponse.json({ error: 'Failed to create notification' }, { status: 500 });
  }
}

// دالة مساعدة لإنشاء إشعارات تلقائية
export async function createNotificationForRequest(
  requestId: number,
  requestNumber: string,
  employeeName: string,
  priority: string,
  type: 'new_request' | 'overdue' | 'urgent_escalation'
) {
  try {
    // جلب جميع المدراء لإرسال الإشعار لهم
    const managers = await prisma.user.findMany({
      where: { role: { in: ['manager', 'admin'] } }
    });

    const notifications = managers.map(manager => ({
      userId: manager.id,
      type,
      title: getNotificationTitle(type, priority),
      message: getNotificationMessage(type, requestNumber, employeeName, priority),
      requestId,
      requestNumber,
      employeeName,
      priority,
      actionRequired: type === 'urgent_escalation' || priority === 'طاريء جدا',
      read: false
    }));

    // إنشاء الإشعارات
    await prisma.notification.createMany({
      data: notifications
    });

    return true;
  } catch (error) {
    console.error('Failed to create notifications:', error);
    return false;
  }
}

function getNotificationTitle(type: string, priority: string): string {
  switch (type) {
    case 'new_request':
      return priority === 'طاريء جدا' ? 'طلب طارئ جداً جديد!' : 
             priority === 'طاريء' ? 'طلب طارئ جديد!' : 'طلب جديد من الموظفين';
    case 'overdue':
      return 'تذكير: طلب معلق يحتاج مراجعة';
    case 'urgent_escalation':
      return 'تصعيد طارئ: طلب يحتاج اهتمام فوري!';
    default:
      return 'إشعار جديد';
  }
}

function getNotificationMessage(type: string, requestNumber: string, employeeName: string, priority: string): string {
  switch (type) {
    case 'new_request':
      return `تم استلام طلب جديد (${requestNumber}) من ${employeeName} بأولوية ${priority}. يرجى المراجعة والرد.`;
    case 'overdue':
      return `الطلب ${requestNumber} من ${employeeName} معلق منذ أكثر من ${getOverdueTime(priority)}. يرجى المراجعة.`;
    case 'urgent_escalation':
      return `الطلب الطارئ ${requestNumber} من ${employeeName} لم يتم الرد عليه خلال الوقت المحدد. يحتاج اهتمام فوري!`;
    default:
      return `إشعار متعلق بالطلب ${requestNumber}`;
  }
}

// ليس متعلق بالتواريخ
function getOverdueTime(priority: string): string {
  switch (priority) {
    case 'طاريء جدا':
      return 'ساعة واحدة';
    case 'طاريء':
      return '4 ساعات';
    default:
      return '24 ساعة';
  }
}
