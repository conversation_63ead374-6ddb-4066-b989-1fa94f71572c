'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  MessageSquare, 
  Send, 
  Paperclip, 
  HelpCircle, 
  CheckCircle2,
  Clock,
  User,
  Shield,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export interface RequestComment {
  id: number;
  requestId: number;
  userId: number;
  userName: string;
  userRole: 'employee' | 'manager' | 'admin';
  comment: string;
  commentType: 'comment' | 'clarification_request' | 'clarification_response';
  attachments?: any[];
  isInternal: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface RequestConversationProps {
  requestId: number;
  requestNumber: string;
  currentUserId: number;
  currentUserRole: 'employee' | 'manager' | 'admin';
  currentUserName: string;
  onStatusChange?: (newStatus: string) => void;
}

export default function RequestConversation({
  requestId,
  requestNumber,
  currentUserId,
  currentUserRole,
  currentUserName,
  onStatusChange
}: RequestConversationProps) {
  const [comments, setComments] = useState<RequestComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [commentType, setCommentType] = useState<'comment' | 'clarification_request' | 'clarification_response'>('comment');
  const [isLoading, setIsLoading] = useState(false);
  const [isInternal, setIsInternal] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // جلب التعليقات
  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/employee-requests/${requestId}/comments`);
      if (response.ok) {
        const data = await response.json();
        setComments(data);
      }
    } catch (error) {
      console.error('خطأ في جلب التعليقات:', error);
    }
  };

  // إضافة تعليق جديد
  const addComment = async () => {
    if (!newComment.trim()) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/employee-requests/${requestId}/comments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          comment: newComment,
          commentType,
          isInternal: isInternal && currentUserRole !== 'employee'
        })
      });

      if (response.ok) {
        const newCommentData = await response.json();
        setComments(prev => [...prev, newCommentData]);
        setNewComment('');
        
        // تحديث حالة الطلب إذا كان طلب توضيح
        if (commentType === 'clarification_request' && onStatusChange) {
          onStatusChange('قيد المراجعة المتقدمة');
        }

        toast({
          title: 'تم إضافة التعليق',
          description: 'تم إضافة تعليقك بنجاح'
        });
      } else {
        throw new Error('فشل في إضافة التعليق');
      }
    } catch (error) {
      console.error('خطأ في إضافة التعليق:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في إضافة التعليق',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // التمرير إلى آخر رسالة
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    fetchComments();
  }, [requestId]);

  useEffect(() => {
    scrollToBottom();
  }, [comments]);

  // أيقونة نوع التعليق
  const getCommentTypeIcon = (type: string) => {
    switch (type) {
      case 'clarification_request':
        return <HelpCircle className="h-4 w-4 text-orange-600" />;
      case 'clarification_response':
        return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      default:
        return <MessageSquare className="h-4 w-4 text-blue-600" />;
    }
  };

  // لون نوع التعليق
  const getCommentTypeColor = (type: string) => {
    switch (type) {
      case 'clarification_request':
        return 'bg-orange-50 border-orange-200';
      case 'clarification_response':
        return 'bg-green-50 border-green-200';
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  // أيقونة دور المستخدم
  const getUserRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-4 w-4 text-red-600" />;
      case 'manager':
        return <Shield className="h-4 w-4 text-blue-600" />;
      default:
        return <User className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 space-x-reverse">
          <MessageSquare className="h-5 w-5 text-primary" />
          <span>محادثة الطلب {requestNumber}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* منطقة الرسائل */}
        <div className="max-h-96 overflow-y-auto space-y-3 p-4 bg-gray-50 rounded-lg">
          {comments.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>لا توجد تعليقات بعد</p>
              <p className="text-sm">ابدأ المحادثة بإضافة تعليق</p>
            </div>
          ) : (
            comments.map((comment) => (
              <div
                key={comment.id}
                className={`flex space-x-3 space-x-reverse ${
                  comment.userId === currentUserId ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg border ${
                    comment.userId === currentUserId
                      ? 'bg-primary text-primary-foreground border-primary/20'
                      : getCommentTypeColor(comment.commentType)
                  }`}
                >
                  {/* معلومات المرسل */}
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs">
                        {comment.userName ? comment.userName.charAt(0) : 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      {getUserRoleIcon(comment.userRole)}
                      <span className="text-xs font-medium">{comment.userName || 'مستخدم غير معروف'}</span>
                    </div>
                    {getCommentTypeIcon(comment.commentType)}
                    {comment.isInternal && (
                      <Badge variant="secondary" className="text-xs">
                        داخلي
                      </Badge>
                    )}
                  </div>
                  
                  {/* محتوى التعليق */}
                  <p className="text-sm whitespace-pre-wrap">{comment.comment}</p>
                  
                  {/* وقت الإرسال */}
                  <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                    <span>{format(new Date(comment.createdAt), 'HH:mm')}</span>
                    <span>{format(new Date(comment.createdAt), 'yyyy/MM/dd')}</span>
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        <Separator />

        {/* منطقة كتابة التعليق */}
        <div className="space-y-3">
          {/* أزرار نوع التعليق */}
          <div className="flex space-x-2 space-x-reverse">
            <Button
              variant={commentType === 'comment' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCommentType('comment')}
            >
              <MessageSquare className="h-4 w-4 ml-1" />
              تعليق
            </Button>
            {currentUserRole !== 'employee' && (
              <Button
                variant={commentType === 'clarification_request' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCommentType('clarification_request')}
              >
                <HelpCircle className="h-4 w-4 ml-1" />
                طلب توضيح
              </Button>
            )}
            <Button
              variant={commentType === 'clarification_response' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCommentType('clarification_response')}
            >
              <CheckCircle2 className="h-4 w-4 ml-1" />
              رد على التوضيح
            </Button>
          </div>

          {/* خيار التعليق الداخلي */}
          {currentUserRole !== 'employee' && (
            <div className="flex items-center space-x-2 space-x-reverse">
              <input
                type="checkbox"
                id="internal"
                checked={isInternal}
                onChange={(e) => setIsInternal(e.target.checked)}
                className="rounded"
              />
              <label htmlFor="internal" className="text-sm text-gray-600">
                تعليق داخلي (للإدارة فقط)
              </label>
            </div>
          )}

          {/* منطقة النص */}
          <div className="flex space-x-2 space-x-reverse">
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="اكتب تعليقك هنا..."
              className="flex-1 min-h-[80px] resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                  addComment();
                }
              }}
            />
            <div className="flex flex-col space-y-2">
              <Button
                onClick={addComment}
                disabled={!newComment.trim() || isLoading}
                size="sm"
              >
                <Send className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Paperclip className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <p className="text-xs text-gray-500">
            اضغط Ctrl+Enter للإرسال السريع
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
