/**
 * إصلاح schema أوامر الصيانة - حذف حقل items إذا كان موجود
 * Fix Maintenance Orders Schema - Remove items column if exists
 * تاريخ: 5 أغسطس 2025
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixMaintenanceOrdersSchema() {
  console.log('🔧 بدء إصلاح schema أوامر الصيانة...\n');

  try {
    // محاولة حذف العمود items إذا كان موجوداً من جدول MaintenanceOrder
    try {
      await prisma.$executeRaw`ALTER TABLE "MaintenanceOrder" DROP COLUMN IF EXISTS "items";`;
      console.log('✅ تم حذف العمود القديم items من جدول MaintenanceOrder');
    } catch (error) {
      console.log('ℹ️ العمود items غير موجود أو تم حذفه مسبقاً من MaintenanceOrder');
    }

    // محاولة حذف العمود items إذا كان موجوداً من جدول MaintenanceReceiptOrder
    try {
      await prisma.$executeRaw`ALTER TABLE "MaintenanceReceiptOrder" DROP COLUMN IF EXISTS "items";`;
      console.log('✅ تم حذف العمود القديم items من جدول MaintenanceReceiptOrder');
    } catch (error) {
      console.log('ℹ️ العمود items غير موجود أو تم حذفه مسبقاً من MaintenanceReceiptOrder');
    }

    // التحقق من وجود جدول MaintenanceOrderItem
    const maintenanceOrderItemsExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'maintenance_order_items'
      );
    `;

    console.log('📋 حالة جدول maintenance_order_items:', maintenanceOrderItemsExists[0]?.exists ? 'موجود' : 'غير موجود');

    if (maintenanceOrderItemsExists[0]?.exists) {
      // عد العناصر الموجودة
      const itemsCount = await prisma.maintenanceOrderItem.count();
      console.log(`📊 عدد عناصر أوامر الصيانة الموجودة: ${itemsCount}`);
    }

    // التحقق من وجود جدول MaintenanceReceiptOrderItem
    const maintenanceReceiptItemsExists = await prisma.$queryRaw`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'MaintenanceReceiptOrderItem'
      );
    `;

    console.log('📋 حالة جدول MaintenanceReceiptOrderItem:', maintenanceReceiptItemsExists[0]?.exists ? 'موجود' : 'غير موجود');

    if (maintenanceReceiptItemsExists[0]?.exists) {
      // عد العناصر الموجودة
      const receiptItemsCount = await prisma.maintenanceReceiptOrderItem.count();
      console.log(`📊 عدد عناصر إيصالات الصيانة الموجودة: ${receiptItemsCount}`);
    }

    // اختبار إنشاء أمر صيانة بسيط للتحقق من العمل
    console.log('\n🧪 اختبار إنشاء أمر صيانة...');
    
    const testOrder = await prisma.maintenanceOrder.create({
      data: {
        orderNumber: 'TEST-' + Date.now(),
        date: new Date(),
        employeeName: 'اختبار',
        status: 'wip',
        source: 'warehouse'
      }
    });

    console.log(`✅ تم إنشاء أمر اختبار بنجاح: ${testOrder.orderNumber}`);

    // اختبار إنشاء عنصر للأمر
    const testItem = await prisma.maintenanceOrderItem.create({
      data: {
        maintenanceOrderId: testOrder.id,
        deviceId: 'TEST-DEVICE',
        model: 'Test Model'
      }
    });

    console.log(`✅ تم إنشاء عنصر اختبار بنجاح: ${testItem.deviceId}`);

    // التحقق من العلاقة
    const orderWithItems = await prisma.maintenanceOrder.findUnique({
      where: { id: testOrder.id },
      include: { items: true }
    });

    console.log(`✅ العلاقة تعمل بشكل صحيح: ${orderWithItems?.items.length || 0} عنصر`);

    // حذف البيانات الاختبارية
    await prisma.maintenanceOrderItem.delete({ where: { id: testItem.id } });
    await prisma.maintenanceOrder.delete({ where: { id: testOrder.id } });
    console.log('🗑️ تم حذف البيانات الاختبارية');

    console.log('\n🎉 تم إصلاح schema أوامر الصيانة بنجاح!');
    console.log('\n📋 ملخص الإصلاحات:');
    console.log('   ✅ تم حذف حقل items القديم إذا كان موجوداً');
    console.log('   ✅ العلاقة مع MaintenanceOrderItem تعمل بشكل صحيح');
    console.log('   ✅ النظام جاهز لإنشاء أوامر الصيانة');

  } catch (error) {
    console.error('❌ حدث خطأ أثناء إصلاح schema أوامر الصيانة:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل السكريبت
fixMaintenanceOrdersSchema()
  .then(() => {
    console.log('\n✅ تم إكمال إصلاح schema أوامر الصيانة بنجاح!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ فشل في إصلاح schema أوامر الصيانة:', error);
    process.exit(1);
  });
