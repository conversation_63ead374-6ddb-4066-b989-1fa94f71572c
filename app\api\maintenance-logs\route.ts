import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // فحص معامل view لتحديد النوع المطلوب
    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view');

    // استرجاع سجلات الصيانة مع ترتيب مختلف حسب النوع
    const orderBy = view === 'simple' 
      ? { deviceId: 'asc' as const }
      : { id: 'desc' as const };

    const maintenanceLogs = await prisma.maintenanceLog.findMany({
      orderBy
    });
    return NextResponse.json(maintenanceLogs);
  } catch (error) {
    console.error('Failed to fetch maintenance logs:', error);
    return NextResponse.json({ error: 'Failed to fetch maintenance logs' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newLog = await request.json();

    // Basic validation
    if (!newLog.deviceId || !newLog.model) {
      return NextResponse.json(
        { error: 'Device ID and model are required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود الجهاز
      const device = await tx.device.findUnique({
        where: { id: newLog.deviceId }
      });

      if (!device) {
        throw new Error('Device not found');
      }

      // Create the maintenance log
      const log = await tx.maintenanceLog.create({
        data: {
          deviceId: newLog.deviceId,
          model: newLog.model,
          repairDate: newLog.repairDate || new Date(),
          notes: newLog.notes || null,
          result: newLog.result || null,
          status: newLog.status || 'pending',
          acknowledgedDate: newLog.acknowledgedDate || null,
          warehouseName: newLog.warehouseName || null,
          acknowledgedBy: newLog.acknowledgedBy || null,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created maintenance log for device: ${newLog.deviceId}`,
        tableName: 'maintenanceLog',
        recordId: log.id.toString()
      });

      return log;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create maintenance log:', error);

    if (error instanceof Error && error.message === 'Device not found') {
      return NextResponse.json({ error: 'Device not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to create maintenance log' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedLog = await request.json();

    if (!updatedLog.id) {
      return NextResponse.json(
        { error: 'Maintenance log ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if log exists
      const existingLog = await tx.maintenanceLog.findUnique({
        where: { id: updatedLog.id }
      });

      if (!existingLog) {
        throw new Error('Maintenance log not found');
      }

      // Update the log
      const log = await tx.maintenanceLog.update({
        where: { id: updatedLog.id },
        data: {
          deviceId: updatedLog.deviceId || existingLog.deviceId,
          model: updatedLog.model || existingLog.model,
          repairDate: updatedLog.repairDate || existingLog.repairDate,
          notes: updatedLog.notes !== undefined ? updatedLog.notes : existingLog.notes,
          result: updatedLog.result !== undefined ? updatedLog.result : existingLog.result,
          status: updatedLog.status || existingLog.status,
          acknowledgedDate: updatedLog.acknowledgedDate !== undefined ? updatedLog.acknowledgedDate : existingLog.acknowledgedDate,
          warehouseName: updatedLog.warehouseName !== undefined ? updatedLog.warehouseName : existingLog.warehouseName,
          acknowledgedBy: updatedLog.acknowledgedBy !== undefined ? updatedLog.acknowledgedBy : existingLog.acknowledgedBy,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated maintenance log for device: ${log.deviceId}`,
        tableName: 'maintenanceLog',
        recordId: log.id.toString()
      });

      return log;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update maintenance log:', error);

    if (error instanceof Error && error.message === 'Maintenance log not found') {
      return NextResponse.json({ error: 'Maintenance log not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update maintenance log' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Maintenance log ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if log exists
      const existingLog = await tx.maintenanceLog.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingLog) {
        throw new Error('Maintenance log not found');
      }

      // Delete the log
      await tx.maintenanceLog.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted maintenance log for device: ${existingLog.deviceId}`,
        tableName: 'maintenanceLog',
        recordId: id.toString()
      });

      return { message: 'Maintenance log deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete maintenance log:', error);

    if (error instanceof Error && error.message === 'Maintenance log not found') {
      return NextResponse.json({ error: 'Maintenance log not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete maintenance log' }, { status: 500 });
  }
}
