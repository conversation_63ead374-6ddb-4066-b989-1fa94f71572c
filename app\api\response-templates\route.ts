import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { TemplateVariable } from '@/lib/template-service';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// جلب جميع القوالب
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    const templates = await prisma.responseTemplate.findMany({
      where: {
        isActive: true,
        ...(category && { category })
      },
      orderBy: [
        { isSystem: 'desc' },
        { usageCount: 'desc' },
        { name: 'asc' }
      ]
    });

    const formattedTemplates = templates.map(template => ({
      ...template,
      variables: template.variables as TemplateVariable[] || [],
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    }));
    return NextResponse.json(formattedTemplates);
  } catch (error) {
    console.error('خطأ في جلب القوالب:', error);
    return NextResponse.json({ error: 'Failed to fetch templates' }, { status: 500 });
  }
}

// إنشاء قالب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, category, title, content, variables } = body;

    if (!name || !category || !title || !content) {
      return NextResponse.json(
        { error: 'Name, category, title, and content are required' },
        { status: 400 }
      );
    }

    // الحصول على معلومات المستخدم الحالي (مؤقت)
    const currentUserId = 1;

    const template = await prisma.responseTemplate.create({
      data: {
        name,
        category,
        title,
        content,
        variables: variables || [],
        isSystem: false,
        createdBy: currentUserId
      }
    });

    const formattedTemplate = {
      ...template,
      variables: template.variables as TemplateVariable[] || [],
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    };

    return NextResponse.json(formattedTemplate, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء القالب:', error);
    return NextResponse.json({ error: 'Failed to create template' }, { status: 500 });
  }
}

// تحديث قالب
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, category, title, content, variables, isActive } = body;

    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    await prisma.responseTemplate.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(category && { category }),
        ...(title && { title }),
        ...(content && { content }),
        ...(variables && { variables }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date()
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في تحديث القالب:', error);
    return NextResponse.json({ error: 'Failed to update template' }, { status: 500 });
  }
}

// حذف قالب
export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();

    if (!id) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    // التحقق من أن القالب ليس نظامي
    const template = await prisma.responseTemplate.findUnique({
      where: { id }
    });

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    if (template.isSystem) {
      return NextResponse.json({ error: 'Cannot delete system template' }, { status: 403 });
    }

    await prisma.responseTemplate.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف القالب:', error);
    return NextResponse.json({ error: 'Failed to delete template' }, { status: 500 });
  }
}
