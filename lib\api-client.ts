/**
 * API Client Utility للتعامل مع طلبات HTTP مع headers التفويض
 */

interface ApiRequestOptions extends RequestInit {
  headers?: Record<string, string>;
}

/**
 * إنشاء headers افتراضية مع التفويض
 */
function createAuthHeaders(): Record<string, string> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // للبيئة التطويرية، سنستخدم token مبسط محدد مسبقاً
  // في الإنتاج، يجب الحصول على token من login
  // استخدام token ثابت لضمان الاستقرار
  const devToken = 'dXNlcjphZG1pbjphZG1pbg=='; // user:admin:admin (base64)
  headers['Authorization'] = `Bearer ${devToken}`;

  return headers;
}

/**
 * دالة مساعدة لإرسال طلبات API مع التفويض
 */
export async function apiRequest(url: string, options: ApiRequestOptions = {}): Promise<Response> {
  const authHeaders = createAuthHeaders();
  
  const mergedOptions: RequestInit = {
    ...options,
    headers: {
      ...authHeaders,
      ...options.headers,
    },
  };

  return fetch(url, mergedOptions);
}

/**
 * دوال مساعدة لطلبات HTTP شائعة
 */
export const apiClient = {
  get: (url: string, options?: ApiRequestOptions) => 
    apiRequest(url, { method: 'GET', ...options }),
    
  post: (url: string, data?: any, options?: ApiRequestOptions) => 
    apiRequest(url, { 
      method: 'POST', 
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
    
  put: (url: string, data?: any, options?: ApiRequestOptions) => 
    apiRequest(url, { 
      method: 'PUT', 
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
    
  delete: (url: string, data?: any, options?: ApiRequestOptions) => 
    apiRequest(url, { 
      method: 'DELETE', 
      body: data ? JSON.stringify(data) : undefined,
      ...options 
    }),
};

/**
 * معالجة استجابة API مع الأخطاء
 */
export async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * دالة شاملة للتعامل مع API
 */
export async function apiCall<T>(url: string, options?: ApiRequestOptions): Promise<T> {
  const response = await apiRequest(url, options);
  return handleApiResponse<T>(response);
}
