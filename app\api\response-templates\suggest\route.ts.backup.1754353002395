import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { TemplateVariable } from '@/lib/template-service';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestType = searchParams.get('requestType') || '';
    const priority = searchParams.get('priority') || '';
    const category = searchParams.get('category');

    // جلب القوالب النشطة
    const templates = await prisma.responseTemplate.findMany({
      where: {
        isActive: true,
        ...(category && { category })
      },
      orderBy: [
        { isSystem: 'desc' },
        { usageCount: 'desc' },
        { name: 'asc' }
      ]
    });

    // ترتيب القوالب بناءً على الملاءمة
    const scored = templates.map(template => {
      let score = template.usageCount;

      if (template.isSystem) score += 100;

      const keywords = [requestType, priority].filter(Boolean);
      keywords.forEach(keyword => {
        if (template.content.includes(keyword) || template.title.includes(keyword)) {
          score += 50;
        }
      });

      return {
        ...template,
        score,
        variables: template.variables as TemplateVariable[] || [],
        createdAt: template.createdAt.toISOString(),
        updatedAt: template.updatedAt.toISOString()
      };
    });

    const suggestedTemplates = scored
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(({ score, ...template }) => template);

    return NextResponse.json(suggestedTemplates);
  } catch (error) {
    console.error('خطأ في اقتراح القوالب:', error);
    return NextResponse.json({ error: 'Failed to suggest templates' }, { status: 500 });
  }
}
