const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkReturnsTableStructure() {
  try {
    console.log('🔍 Checking returns table structure...');
    
    // Get table structure
    const structure = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'Return' 
      ORDER BY ordinal_position
    `;
    
    console.log('Table structure:', structure);
    
    // Try to get some sample data with minimal fields
    const sampleData = await prisma.$queryRaw`
      SELECT * FROM "Return" LIMIT 3
    `;
    
    console.log('Sample data:', sampleData);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkReturnsTableStructure();
