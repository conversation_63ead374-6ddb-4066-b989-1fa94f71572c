import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir, readdir, stat, unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { requireAuth } from '@/lib/auth';
import { createAuditLogInTransaction, executeInTransaction } from '@/lib/transaction-utils';
import { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';

// Allowed file types and max size
const ALLOWED_FILE_TYPES = [
  'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
  'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain', 'text/csv'
];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const section = formData.get('section') as string;

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'لم يتم اختيار أي ملفات' }, { status: 400 });
    }

    if (!section) {
      return NextResponse.json({ error: 'لم يتم تحديد القسم' }, { status: 400 });
    }

    // Validate section name to prevent path traversal
    if (!/^[a-zA-Z0-9_-]+$/.test(section)) {
      return NextResponse.json({ error: 'اسم القسم غير صالح' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      const uploadedFiles = [];
      const uploadDir = path.join(process.cwd(), 'public', 'attachments', section);

      // إنشاء المجلد إذا لم يكن موجوداً
      if (!existsSync(uploadDir)) {
        await mkdir(uploadDir, { recursive: true });
      }

      for (const file of files) {
        if (file.size === 0) continue;

        // التحقق من نوع الملف
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          throw new Error(`File type ${file.type} is not allowed`);
        }

        // التحقق من حجم الملف
        if (file.size > MAX_FILE_SIZE) {
          throw new Error(`File ${file.name} is too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB`);
        }

        // إنشاء اسم ملف فريد
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const fileExtension = path.extname(file.name);
        const fileName = `${timestamp}_${randomString}${fileExtension}`;
        const filePath = path.join(uploadDir, fileName);

        // التحقق من path traversal
        const resolvedPath = path.resolve(filePath);
        const resolvedUploadDir = path.resolve(uploadDir);
        if (!resolvedPath.startsWith(resolvedUploadDir)) {
          throw new Error('Invalid file path');
        }

        // تحويل الملف إلى buffer وحفظه
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        uploadedFiles.push({
          originalName: file.name,
          fileName: fileName,
          filePath: `/attachments/${section}/${fileName}`,
          size: file.size,
          type: file.type,
          uploadedAt: new Date()
        });
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPLOAD',
        details: `Uploaded ${uploadedFiles.length} files to section: ${section}`,
        tableName: 'file_upload',
        recordId: section
      });

      return {
        success: true,
        message: `تم رفع ${uploadedFiles.length} ملف بنجاح`,
        files: uploadedFiles
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في رفع الملفات:', error);

    if (error instanceof Error) {
      if (error.message.includes('not allowed')) {
        return NextResponse.json({ error: 'نوع الملف غير مسموح' }, { status: 400 });
      }
      if (error.message.includes('too large')) {
        return NextResponse.json({ error: 'حجم الملف كبير جداً' }, { status: 400 });
      }
      if (error.message.includes('Invalid file path')) {
        return NextResponse.json({ error: 'مسار الملف غير صالح' }, { status: 400 });
      }
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء رفع الملفات' },
      { status: 500 }
    );
  }
}

// للحصول على قائمة الملفات المرفقة
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');

    if (!section) {
      return NextResponse.json({ error: 'معامل القسم مطلوب' }, { status: 400 });
    }

    // التحقق من صحة القسم
    const validSections = ['clients', 'devices', 'sales', 'maintenance', 'suppliers', 'warehouses', 'supply', 'returns'];
    if (!validSections.includes(section)) {
      return NextResponse.json({ error: 'قسم غير صالح' }, { status: 400 });
    }

    // الحصول على قائمة الملفات من المجلد
    const uploadDir = path.join(process.cwd(), 'public', 'attachments', section);
    let files = [];

    if (existsSync(uploadDir)) {
      const fileNames = await readdir(uploadDir);
      files = await Promise.all(
        fileNames.map(async (fileName) => {
          const filePath = path.join(uploadDir, fileName);
          const stats = await stat(filePath);
          return {
            fileName,
            filePath: `/attachments/${section}/${fileName}`,
            size: stats.size,
            uploadedAt: stats.mtime.toISOString(),
            type: path.extname(fileName)
          };
        })
      );
    }

    return NextResponse.json({
      success: true,
      section,
      files
    });

  } catch (error) {
    console.error('خطأ في جلب الملفات:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب الملفات' },
      { status: 500 }
    );
  }
}

// حذف ملف
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - السماح للمستخدمين العاديين بحذف ملفاتهم
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // قراءة البيانات من query parameters
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');
    const section = searchParams.get('section');

    if (!fileName || !section) {
      return NextResponse.json({ error: 'اسم الملف والقسم مطلوبان' }, { status: 400 });
    }

    // التحقق من صحة القسم
    const validSections = ['clients', 'devices', 'sales', 'maintenance', 'suppliers', 'warehouses', 'supply', 'returns'];
    if (!validSections.includes(section)) {
      return NextResponse.json({ error: 'قسم غير صالح' }, { status: 400 });
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      const filePath = path.join(process.cwd(), 'public', 'attachments', section, fileName);

      // التحقق من path traversal
      const resolvedPath = path.resolve(filePath);
      const resolvedUploadDir = path.resolve(path.join(process.cwd(), 'public', 'attachments', section));
      if (!resolvedPath.startsWith(resolvedUploadDir)) {
        throw new Error('Invalid file path');
      }

      // التحقق من وجود الملف
      if (!existsSync(filePath)) {
        throw new Error('File not found');
      }

      // حذف الملف
      await unlink(filePath);

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted file: ${fileName} from section: ${section}`,
        tableName: 'file_delete',
        recordId: fileName
      });

      return { message: 'تم حذف الملف بنجاح' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في حذف الملف:', error);

    if (error instanceof Error) {
      if (error.message === 'File not found') {
        return NextResponse.json({ error: 'الملف غير موجود' }, { status: 404 });
      }
      if (error.message === 'Invalid file path') {
        return NextResponse.json({ error: 'مسار الملف غير صالح' }, { status: 400 });
      }
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف الملف' },
      { status: 500 }
    );
  }
}
