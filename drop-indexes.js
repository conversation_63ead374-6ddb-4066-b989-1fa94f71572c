/**
 * Drop Indexes Script
 * Date: 2025-08-04
 * Description: Drop remaining indexes that cause permission issues
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function dropIndexes() {
  console.log('🗑️ حذف الفهارس المتبقية...\n');

  try {
    const indexes = [
      'idx_supply_draft_updated_at',
      'idx_supply_draft_user_id',
      'idx_sales_draft_updated_at',
      'idx_sales_draft_user_id',
      'idx_returns_draft_updated_at',
      'idx_returns_draft_user_id',
      'idx_notifications_user_id',
      'idx_notifications_request_id',
      'idx_notifications_read',
      'idx_request_comments_request_id',
      'idx_request_comments_user_id'
    ];

    for (const index of indexes) {
      try {
        await prisma.$executeRawUnsafe(`DROP INDEX IF EXISTS "${index}";`);
        console.log(`   ✅ حذف الفهرس: ${index}`);
      } catch (error) {
        console.log(`   ⚠️ ${index}: ${error.message}`);
      }
    }

    console.log('\n🎉 تم حذف الفهارس بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في حذف الفهارس:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run drop
if (require.main === module) {
  dropIndexes()
    .then(() => {
      console.log('\n✅ تم الانتهاء من حذف الفهارس');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في حذف الفهارس:', error);
      process.exit(1);
    });
}

module.exports = { dropIndexes };
