/**
 * Fix Tracking Components Script
 * Date: 2025-08-04
 * Description: Fix date type issues in device tracking components
 */

const fs = require('fs');
const path = require('path');

// Tracking components to fix
const trackingFiles = [
  'app/(main)/track/DeviceAdvancedStats.tsx',
  'app/(main)/track/DeviceDetailsSection.tsx',
  'app/(main)/track/DeviceHistoryTimeline.tsx',
  'app/(main)/track/DeviceOperationDetails.tsx',
  'app/(main)/track/DeviceTrackingFilters.tsx',
  'app/(main)/track/event-log/page.tsx',
  'app/(main)/track/page.tsx',
  'app/(main)/track/simple-page.tsx',
  'lib/device-tracking-utils.ts',
  'components/DeviceTrackingReport.tsx'
];

// Tracking-specific fixes
const trackingFixes = [
  // Fix string date types in interfaces
  {
    search: /date: string;/g,
    replace: 'date: Date;',
    description: 'تحويل date إلى Date في الواجهات'
  },
  {
    search: /expiryDate: string;/g,
    replace: 'expiryDate: Date;',
    description: 'تحويل expiryDate إلى Date'
  },
  {
    search: /formattedDate\?: string;/g,
    replace: 'formattedDate?: string; // يبقى string للعرض',
    description: 'الاحتفاظ بـ formattedDate كـ string للعرض'
  },
  
  // Fix function parameters
  {
    search: /function formatArabicDate\(date: Date \| string\): string/g,
    replace: 'function formatArabicDate(date: Date): string',
    description: 'تحسين دالة formatArabicDate'
  },
  {
    search: /\(date: Date \| string\): string/g,
    replace: '(date: Date): string',
    description: 'تحسين معاملات الدوال'
  },
  
  // Fix toLocaleDateString usage
  {
    search: /\.toLocaleDateString\('ar-EG'\)/g,
    replace: '',
    description: 'إزالة toLocaleDateString للاستبدال'
  },
  
  // Fix manual date operations
  {
    search: /thirtyDaysAgo\.setDate\(thirtyDaysAgo\.getDate\(\) - 30\);/g,
    replace: 'thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);',
    description: 'تحسين عمليات التاريخ'
  },
  {
    search: /sixtyDaysAgo\.setDate\(sixtyDaysAgo\.getDate\(\) - 60\);/g,
    replace: 'sixtyDaysAgo = new Date(Date.now() - 60 * 24 * 60 * 60 * 1000);',
    description: 'تحسين عمليات التاريخ'
  },
  
  // Fix locale string usage
  {
    search: /value\.toLocaleString\(\)/g,
    replace: 'formatNumber(value)',
    description: 'استخدام دالة تنسيق موحدة للأرقام'
  }
];

// Additional utility functions to add
const utilityFunctions = `
// دوال مساعدة للتتبع
function formatNumber(value: number): string {
  return new Intl.NumberFormat('ar-EG').format(value);
}

function getDateDaysAgo(days: number): Date {
  return new Date(Date.now() - days * 24 * 60 * 60 * 1000);
}

function formatTrackingDate(date: Date): string {
  return formatDate(date, { arabic: true, format: 'full' });
}
`;

function addDateUtilsImport(filePath) {
  if (!fs.existsSync(filePath)) return false;

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if import already exists
  if (content.includes("from '@/lib/date-utils'")) {
    return false;
  }

  // Find the last import statement
  const importRegex = /^import.*from.*['"];$/gm;
  const imports = content.match(importRegex);
  
  if (imports && imports.length > 0) {
    const lastImport = imports[imports.length - 1];
    const importIndex = content.lastIndexOf(lastImport);
    const insertIndex = importIndex + lastImport.length;
    
    const newImport = "\nimport { formatDate, formatDateTime, formatTime } from '@/lib/date-utils';";
    content = content.slice(0, insertIndex) + newImport + content.slice(insertIndex);
    
    fs.writeFileSync(filePath, content);
    console.log(`📦 تم إضافة import للـ date-utils في: ${path.basename(filePath)}`);
    return true;
  }

  return false;
}

function applyFixes(filePath, fixes) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  let appliedFixes = [];

  fixes.forEach(fix => {
    const originalContent = content;
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      modified = true;
      appliedFixes.push(fix.description);
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));
    
    // Apply fixes
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ تم إصلاح: ${path.basename(filePath)}`);
    appliedFixes.forEach(desc => console.log(`   - ${desc}`));
    
    return true;
  }

  return false;
}

async function fixTrackingComponents() {
  console.log('🔧 إصلاح مكونات التتبع...\n');

  let totalFixed = 0;
  let filesModified = [];

  try {
    let processedCount = 0;
    
    for (const file of trackingFiles) {
      const filePath = path.join(process.cwd(), file);
      processedCount++;
      
      console.log(`🔍 [${processedCount}/${trackingFiles.length}] فحص: ${path.basename(file)}`);
      
      if (fs.existsSync(filePath)) {
        // Add date-utils import if needed
        addDateUtilsImport(filePath);
        
        // Apply fixes
        if (applyFixes(filePath, trackingFixes)) {
          totalFixed += trackingFixes.length;
          filesModified.push(file);
        }
        
        // Add utility functions to device-tracking-utils.ts
        if (file.includes('device-tracking-utils.ts')) {
          let content = fs.readFileSync(filePath, 'utf8');
          if (!content.includes('دوال مساعدة للتتبع')) {
            content += utilityFunctions;
            fs.writeFileSync(filePath, content);
            console.log('✅ تم إضافة دوال مساعدة للتتبع');
            totalFixed += 3;
          }
        }
      } else {
        console.log(`⚠️ الملف غير موجود: ${file}`);
      }
      
      // Progress indicator
      if (processedCount % 5 === 0) {
        console.log(`📊 تم معالجة ${processedCount} من ${trackingFiles.length} ملف...\n`);
      }
    }

    // Generate summary
    console.log('\n📊 ملخص إصلاح مكونات التتبع:');
    console.log('='.repeat(40));
    console.log(`✅ إجمالي الإصلاحات: ${totalFixed}`);
    console.log(`📁 الملفات المعدلة: ${filesModified.length}`);
    console.log(`📋 الملفات المفحوصة: ${trackingFiles.length}`);
    
    if (filesModified.length > 0) {
      console.log('\n📋 الملفات المعدلة:');
      filesModified.forEach((file, index) => {
        console.log(`${index + 1}. ${path.basename(file)}`);
      });
    }

    // Create report
    const report = {
      timestamp: new Date().toISOString(),
      totalFixes: totalFixed,
      filesModified: filesModified,
      totalFilesProcessed: trackingFiles.length,
      fixes: trackingFixes
    };

    fs.writeFileSync('tracking-components-fixes-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 تم حفظ تقرير إصلاح مكونات التتبع في: tracking-components-fixes-report.json');

    if (totalFixed > 0) {
      console.log('\n🎉 تم إصلاح مكونات التتبع بنجاح!');
    } else {
      console.log('\n⚠️ لم يتم العثور على مشاكل للإصلاح في مكونات التتبع');
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح مكونات التتبع:', error);
    throw error;
  }
}

// Run fixes
if (require.main === module) {
  fixTrackingComponents()
    .then(() => {
      console.log('\n✅ تم الانتهاء من إصلاح مكونات التتبع');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في إصلاح مكونات التتبع:', error);
      process.exit(1);
    });
}

module.exports = { fixTrackingComponents };
