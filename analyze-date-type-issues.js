/**
 * Analyze Date Type Issues Script
 * Date: 2025-08-04
 * Description: Analyze and report date type mismatches in the system
 */

const fs = require('fs');
const path = require('path');

function analyzeFile(filePath, content) {
  const issues = [];
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    const lineNumber = index + 1;
    
    // Check for string date types in TypeScript
    if (line.includes(': string') && (
      line.includes('date') || 
      line.includes('Date') || 
      line.includes('time') || 
      line.includes('Time') ||
      line.includes('createdAt') ||
      line.includes('updatedAt')
    )) {
      issues.push({
        type: 'STRING_DATE_TYPE',
        line: lineNumber,
        content: line.trim(),
        severity: 'HIGH',
        suggestion: 'تحويل إلى Date type'
      });
    }
    
    // Check for toISOString() usage
    if (line.includes('toISOString()')) {
      issues.push({
        type: 'TO_ISO_STRING_USAGE',
        line: lineNumber,
        content: line.trim(),
        severity: 'MEDIUM',
        suggestion: 'استخدام Date object مباشرة'
      });
    }
    
    // Check for new Date().toISOString()
    if (line.includes('new Date().toISOString()')) {
      issues.push({
        type: 'NEW_DATE_TO_ISO',
        line: lineNumber,
        content: line.trim(),
        severity: 'HIGH',
        suggestion: 'استخدام new Date() مباشرة'
      });
    }
    
    // Check for local date formatting functions
    if (line.includes('toLocaleDateString') || line.includes('toLocaleString')) {
      issues.push({
        type: 'LOCAL_DATE_FORMAT',
        line: lineNumber,
        content: line.trim(),
        severity: 'MEDIUM',
        suggestion: 'استخدام دوال date-utils الموحدة'
      });
    }
    
    // Check for manual date formatting
    if (line.includes('getFullYear()') || line.includes('getMonth()') || line.includes('getDate()')) {
      issues.push({
        type: 'MANUAL_DATE_FORMAT',
        line: lineNumber,
        content: line.trim(),
        severity: 'MEDIUM',
        suggestion: 'استخدام دوال date-utils الموحدة'
      });
    }
    
    // Check for JSON.stringify with dates
    if (line.includes('JSON.stringify') && (line.includes('date') || line.includes('Date'))) {
      issues.push({
        type: 'JSON_STRINGIFY_DATE',
        line: lineNumber,
        content: line.trim(),
        severity: 'LOW',
        suggestion: 'التأكد من معالجة التواريخ بشكل صحيح'
      });
    }
  });
  
  return issues;
}

function analyzeDirectory(dirPath, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const results = {};
  
  function scanDirectory(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const itemPath = path.join(currentPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDirectory(itemPath);
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            const issues = analyzeFile(itemPath, content);
            
            if (issues.length > 0) {
              const relativePath = path.relative(process.cwd(), itemPath);
              results[relativePath] = issues;
            }
          } catch (error) {
            console.warn(`تعذر قراءة الملف: ${itemPath}`);
          }
        }
      }
    });
  }
  
  scanDirectory(dirPath);
  return results;
}

function generateReport(analysisResults) {
  console.log('🔍 تقرير تحليل مشاكل أنواع البيانات للتواريخ\n');
  console.log('=' .repeat(60));
  
  let totalIssues = 0;
  let highSeverityCount = 0;
  let mediumSeverityCount = 0;
  let lowSeverityCount = 0;
  
  const issueTypes = {};
  
  Object.entries(analysisResults).forEach(([filePath, issues]) => {
    console.log(`\n📁 ${filePath}`);
    console.log('-'.repeat(40));
    
    issues.forEach(issue => {
      totalIssues++;
      
      // Count by severity
      if (issue.severity === 'HIGH') highSeverityCount++;
      else if (issue.severity === 'MEDIUM') mediumSeverityCount++;
      else lowSeverityCount++;
      
      // Count by type
      issueTypes[issue.type] = (issueTypes[issue.type] || 0) + 1;
      
      const severityIcon = issue.severity === 'HIGH' ? '🔴' : 
                          issue.severity === 'MEDIUM' ? '🟡' : '🟢';
      
      console.log(`   ${severityIcon} السطر ${issue.line}: ${issue.type}`);
      console.log(`      الكود: ${issue.content}`);
      console.log(`      الاقتراح: ${issue.suggestion}\n`);
    });
  });
  
  // Summary
  console.log('\n📊 ملخص التحليل');
  console.log('=' .repeat(30));
  console.log(`إجمالي المشاكل: ${totalIssues}`);
  console.log(`🔴 عالية الأولوية: ${highSeverityCount}`);
  console.log(`🟡 متوسطة الأولوية: ${mediumSeverityCount}`);
  console.log(`🟢 منخفضة الأولوية: ${lowSeverityCount}`);
  
  console.log('\n📈 أنواع المشاكل:');
  Object.entries(issueTypes).forEach(([type, count]) => {
    console.log(`   ${type}: ${count}`);
  });
  
  // Recommendations
  console.log('\n💡 التوصيات:');
  console.log('1. تحديث lib/types.ts لاستخدام Date بدلاً من string');
  console.log('2. استخدام دوال date-utils الموحدة');
  console.log('3. تجنب toISOString() إلا عند الضرورة');
  console.log('4. استخدام Date objects في المعالجة الداخلية');
  
  return {
    totalIssues,
    highSeverityCount,
    mediumSeverityCount,
    lowSeverityCount,
    issueTypes,
    fileCount: Object.keys(analysisResults).length
  };
}

// Main execution
async function main() {
  console.log('🚀 بدء تحليل مشاكل أنواع البيانات للتواريخ...\n');
  
  try {
    // Analyze key directories
    const directories = [
      './lib',
      './app',
      './components',
      './context'
    ];
    
    let allResults = {};
    
    directories.forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`🔍 فحص المجلد: ${dir}`);
        const results = analyzeDirectory(dir);
        allResults = { ...allResults, ...results };
      }
    });
    
    // Generate report
    const summary = generateReport(allResults);
    
    // Save detailed report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      summary,
      details: allResults
    };
    
    fs.writeFileSync(
      'date-type-analysis-report.json',
      JSON.stringify(reportData, null, 2)
    );
    
    console.log('\n✅ تم حفظ التقرير المفصل في: date-type-analysis-report.json');
    
    // Priority recommendations
    if (summary.highSeverityCount > 0) {
      console.log('\n🚨 مشاكل عالية الأولوية تحتاج إصلاح فوري!');
    }
    
    if (summary.totalIssues === 0) {
      console.log('\n🎉 لم يتم العثور على مشاكل في أنواع البيانات!');
    }
    
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error);
    process.exit(1);
  }
}

// Run analysis
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✅ تم الانتهاء من التحليل');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ فشل في التحليل:', error);
      process.exit(1);
    });
}

module.exports = { analyzeFile, analyzeDirectory, generateReport };
