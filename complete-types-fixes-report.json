{"timestamp": "2025-08-05T00:13:19.103Z", "totalFixes": 18, "filesModified": ["lib/types.ts"], "typesFixes": [{"search": {}, "replace": "createdAt?: Date;", "description": "تحويل created<PERSON><PERSON> Date"}, {"search": {}, "replace": "returnDate: Date;", "description": "تحويل returnDate إلى Date"}, {"search": {}, "replace": "supplyDate: Date;", "description": "تحويل supplyDate إلى Date"}, {"search": {}, "replace": "repairDate: Date;", "description": "تحويل repairDate إلى Date"}, {"search": {}, "replace": "acknowledgedDate?: Date;", "description": "تحويل acknowledgedDate إلى Date"}, {"search": {}, "replace": "resolutionDate?: Date;", "description": "تحويل resolutionDate إلى Date"}, {"search": {}, "replace": "soldDate?: Date;", "description": "تحويل soldDate إلى Date"}, {"search": {}, "replace": "startTime: Date;", "description": "تحويل startTime إلى Date"}, {"search": {}, "replace": "endTime: Date | null;", "description": "تحويل endTime إلى Date"}, {"search": {}, "replace": "scheduledDate?: Date;", "description": "تحويل scheduledDate إلى Date"}, {"search": {}, "replace": "dateFrom?: Date;", "description": "تحويل dateFrom إلى Date"}, {"search": {}, "replace": "dateTo?: Date;", "description": "تحويل dateTo إلى Date"}, {"search": {}, "replace": "lastLogin?: Date;", "description": "تحويل lastL<PERSON>in إلى Date"}], "utilityTypesFixes": [{"search": {}, "replace": "export type DateField = Date; // استخدم Date فقط للاتساق", "description": "توحيد نوع DateField"}]}